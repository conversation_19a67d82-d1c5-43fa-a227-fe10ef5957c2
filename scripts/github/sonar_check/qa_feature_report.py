
'''
Created on 9-Dec-2024
@author: anknema

'''


import argparse
import os
import sys


THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

from common import read_file, write_to_file, sendMail
import cdets_libs
from Utils import *
from cnjenkins import CNJenkins
import Constants as Constants
from version_info import version_info

def main():

    parser = argparse.ArgumentParser(description='Trigger feature report')

    parser.add_argument('-ha','--hash', required=False, help = 'Product hash')
    parser.add_argument('-d', '--db_name', required=True, help='database name')

    try:
        args = parser.parse_args()
    except Exception as e:
        return e

    # upf:2024.04.h1.i4
    hash = args.hash
    qa_build_number = hash.split(":")[1]
    product = hash.split(":")[0]
    release_type = "QA"
    if (product.upper() in ('CEE','DEPLOYER', 'CEE-BM','DEPLOYER-BM','UPF', 'BASE-VM', 'BASE-BM', 'CNI')):
        print ("Feature report is not required for product : ", product)
        exit(0)
    db_name = args.db_name
    if db_name == '5g-dev':
        mount = Constants.TEST_NFS_MOUNT
    else:
        mount = Constants.NFS_MOUNT

    # Find the branch_version
    build_version=qa_build_number.split('.')
    build_version.pop()
    release_dir='.'.join(build_version)

    file_name = "%s_CDETS_List_%s_%s" % (release_type, product, qa_build_number)
    archive_dir = mount+"/"+release_dir+"/"+product.lower()+"/"+qa_build_number+"/"
    file_cdets_list = mount + release_dir+"/"+product.lower()+"/"+qa_build_number+"/"+file_name
    jira_id_list = []
    print("Processing File %s" % file_cdets_list)
    cdets_list = read_file(file_cdets_list)
    if cdets_list:
        s6_cdets = cdets_libs.filter_cdets(cdets_list, 6)
        if s6_cdets:
            print("S6 CDETS are %s" % s6_cdets)
            cd_w_at2 = cdets_libs.get_cdets_field(s6_cdets, 'Attribute2')
            for cdets_at2 in cd_w_at2:
                print("Processing %s" % cdets_at2)
                jira_info = cdets_at2.split()[-1]
                jira_id = jira_info.split("_")[-1]
                jira_id_list.append(jira_id)

    print("JIRA ID list : %s" % jira_id_list)

    if jira_id_list:
        # Trigger cn_repo_stats for the product orgs

        product_org_list = Utils.get_product_orgs(product, "0")
        job_name_number_list = []
        job_name = "Scheduled_Jobs/cn_repo_stats_data"
        jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])

        if product == "rcm":
            DEV_BRANCH = "v21.28.ux"
        else:
            DEV_BRANCH = "main"

        for product_org in product_org_list:
            parameter = {"ORGANIZATION" : product_org,
                         "BRANCH_LIST" : DEV_BRANCH,
                         "explicit" : "true",
                         "DAYS" : 2,
                         "DB_INST": db_name}

            build_url = jenkins_obj.trigger_build_params(job_name,parameter)
            if build_url != "Aborted":
                build_number = build_url.split('/')[-2]
                job_name_number_list.append(job_name+":"+build_number)

        if job_name_number_list:
            jenkins_obj.monitor_multiple_builds_list(job_name_number_list, timeout=None)

        # find the current version of main branch
        table = "BRANCHES"
        conditions = { "PRODUCTS_ORGANIZATION" : product_org_list[0],
                       "BRANCH_NAME" : DEV_BRANCH}
        print(conditions)

        version_info_obj = version_info(config_section_name=db_name)
        dbo = version_info_obj.open_connection()

        return_value = dbo.run_select_query(table, conditions)
        print(return_value)
        main_version = return_value[0]["BRANCH_VERSION"]
        # Trigger feature level coverage report
        job_name = "Code_Coverage/feature_code_coverage"
        for jira_id in jira_id_list:
            parameter = {"PRODUCT" : product,
                         "FEATURE_ID" : jira_id,
                         "MAIN_VERSION" : main_version,
                         "MAIL_TO" : "<EMAIL>",
                         "COMMIT_AUTHOR_CEC_ID" : "cn_releng"}
            build_url = jenkins_obj.trigger_build_params(job_name,parameter)
            if build_url != "Aborted":
                build_number = build_url.split('/')[-2]
                job_name_number_list.append(job_name+":"+build_number)

        if job_name_number_list:
            print("Jobs triggered")
            print(job_name_number_list)

if __name__ == "__main__":
    sys.exit(main())
