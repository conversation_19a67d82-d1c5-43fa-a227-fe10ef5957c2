import sys
import json
import requests
import os
import re
#import commands                                                                                                                                                                                                                            
from datetime import datetime
import argparse
import subprocess
import shlex
import difflib
import shutil
from requests.auth import HTTPBasicAuth

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")


from libs.common import write_to_file, append_to_file
import Constants as constants
from GithubApi import GithubApi
import cdets_libs
from git_libs import do_git_clone

#from db import CPSOracle

pwd=os.getcwd()
print(pwd)
DB_INI= PARENT_DIR + '/libs/db.ini'
bin_loc = "/usr/cisco/bin"
sep = "=========================================================================="
now = datetime.now()
date = now.strftime("%d_%m_%Y")
sys.path.insert(0,'/auto/mitg-sw/tools/apache-maven-3.6.3/bin')
sys.path.insert(0,'/auto/mitg-sw/tools/jdk-11/bin')
os.environ["JAVA_HOME"] = '/auto/mitg-sw/tools/jdk-11'
os.environ["PATH"] = os.environ["JAVA_HOME"] + '/bin' + os.pathsep + os.environ["PATH"] 
state = "failure"
githubobj = GithubApi()

class SAFactory:
    factories = {}
    def addFactory(id, sa_factory):
        SAFactory.factories.put[id] = sa_factory
    addFactory = staticmethod(addFactory)
    # A Template Method to create sa object
    def createSACls(id):

        return eval(id + '.Factory()').create()

    createSACls = staticmethod(createSACls)

class SACls(object):

    def __init__(self):
        self.state = "failure"
        self.python_script_dir = os.path.dirname(os.path.realpath(__file__))
        self.pull_files = []

    def init_vars(self, pr_url):
        # Fetch variables from JSON
        #self.action = pullhash['action']
        
        pr_url_info = pr_url.split("/")
        self.pr_number = pr_url_info[6]
        self.org = pr_url_info[3]
        self.repo_name = pr_url_info[4]
        pr_info = githubobj.get_pr_details(self.org, self.repo_name, self.pr_number)
        #self.product_url = pullhash['repository']['owner']['html_url']
        #self.git_url = pullhash['repository']['ssh_url']
        self.controlled_branch = pr_info["pr_to_branch"]
        self.pull_from_br = pr_info["pr_from_branch"]
        self.pr_head_sha = pr_info["pr_head_sha"]
        self.pr_state = pr_info["state"]
        
        self.pull_req_url = pr_url
        workspace = os.environ["WORKSPACE"]
        self.clone_dir= workspace+ "/" + self.repo_name
        self.git_url = "https://" + constants.USERNAME + ":" + constants.FULL_TOKEN + "@wwwin-github.cisco.com/"
        self.repo_url = self.git_url + self.org + "/" + self.repo_name + ".git"
        self.source_output_file = "%s.txt" % self.pull_from_br
        self.target_output_file = "%s.txt" % self.controlled_branch
        #self.cdets_id           = re.match(r"CSC[a-zA-z]{2}[0-9]{5}", self.title)[0]
        commit_list_msg = GithubApi().get_pr_cdets_list(self.org, self.repo_name, self.pr_number)
        self.cdets_ids = GithubApi().get_pr_cdets_list(self.org, self.repo_name, self.pr_number,commit_list_msg)['valid']
        #pull_request_info  = [cdets_id[0], repo_name, pull_from_br, pr_number]
        self.report_file = ''
        self.org_end = self.org.replace('mobile-cnat-','')
        self.cdets_encl_report_name = self.org_end + '_' + self.repo_name + '_' + self.controlled_branch + '_' + self.pr_number

        self.attach_clean_enclosure = True
        # If Destination branch is not a release managed branch for the ORG, don't add a clean enclosure .\

        print("Clean Enclosure Attachment : " + str(self.attach_clean_enclosure))
        print("Reponame:" + self.repo_name)
        print("target_branch: " + self.controlled_branch)
        print("Source branch: " + self.pull_from_br)

        write_to_file(pr_url, 'email_subject.txt')

        self.filename = os.path.join(THIS_DIR, 'env.properties')
        self.email_body = os.path.join(THIS_DIR, 'email_body.txt')
        data = "pull_url = " + self.pull_req_url + "\npull_from_br = " + self.pull_from_br + "\npr_number = " + self.pr_number + "\ncontrolled_branch = " + self.controlled_branch + "\nrepo_name = " + self.repo_name
        write_to_file(data, self.filename)

    def set_pending_status(self):
        self.state = "pending"
        self.stamp_sa_status()

    def stamp_ut_status(self, force_clean=False):
        description = "Unit Test to be skipped for files changed"
        ut_state =  "success"
        target_url =  os.environ['BUILD_URL'] + "console"
        context = "Unit-test"
        githubobj.update_pr_check(self.org, self.repo_name, self.pr_head_sha, ut_state, target_url, description, context)              
             
    def stamp_sa_status(self, force_clean=False):
        context = "static-analysis"
        target_url = os.environ['BUILD_URL'] + "console"
        print("stamp_sa_status :", force_clean)
        if force_clean:
            self.state = "success"
        if self.state == "success":
            description = "Static analysis is clean"
        elif self.state == "pending":
            description = "Static analysis is in progress"
        else:
            self.state = "failure"
            description = "Static analysis check has found violations"
        githubobj.update_pr_check(self.org, self.repo_name, self.pr_head_sha, self.state, target_url, description, context)


    def get_source_code(self, git_cmd_str, branch_name, common_parent=False):

        git_cmd = ["git", "--no-pager"]
        if "clone" == git_cmd_str:
            status = None
            git_cmd_action = None
            if self.repo_name not in os.getcwd():
                if not os.path.isdir('./' +self.repo_name):
                    print(self.repo_url)
                    status = do_git_clone(self.repo_url, self.repo_name, branch_name)
                    if status:
                        print("Successfully cloned %s"%self.repo_name)
                    #git_cmd_action = git_cmd + ["clone", self.repo_url]		    
            elif common_parent:
                if not os.path.isdir('./java-lib-tools'):
                    jlt_url = self.git_url + "mobile-cnat-infrastructure/java-lib-tools.git"
                    #git_cmd_action = git_cmd + ["clone", jlt_url, "--quiet"]
                    git_libs.do_git_clone(jlt_url, "java-lib-tools", branch_name)
        elif "checkout" == git_cmd_str:
            if self.repo_name not in os.getcwd():
                os.chdir(self.repo_name)
            git_cmd_action = git_cmd + [git_cmd_str, "%s" % branch_name]
        else:
            git_cmd_action = git_cmd + ["pull"]
            
        try:
            if git_cmd_action is not None:
                git_command_out = subprocess.check_output(git_cmd_action)
        except Exception as e:
                print("Failed to copy\n %s"%e)
                sys.exit(2)		

    def get_return_status(self):
        if self.state == "success":
            return 0
        return 1

    def set_attachment_dir(self, parent_dir=True):
        self.attachment_dir = './'
        if parent_dir:
            self.attachment_dir = '.' + self.attachment_dir
            
    def add_clean_attachment(self, parent_dir=True):
        self.set_attachment_dir(parent_dir)
        self.add_report_file_extn('Static_Analysis.txt')
        f = open(self.attachment_dir + self.report_file, 'w')
        f.write("Static analysis is clean\n")
        f.close()
        self.attach_cdet_report()
        
    def add_report_file_extn(self, report_name, report_file = None):
        self.cdets_encl_report_name = self.cdets_encl_report_name  + '_' + report_name
        if report_file is not None:
            self.report_file = self.report_file + report_file
        else:
            self.report_file = self.report_file + report_name
            
    def attach_cdet_report(self, ws_dir=False):

        if ws_dir:
            self.attachment_dir = self.ws
        print('attach_cdet_report ' + self.cdets_encl_report_name + '   '+ self.report_file)
        #cdets_libs.add_file(self.cdets_ids, self.cdets_encl_report_name, self.attachment_dir + self.report_file, True)
        for cdets_id in self.cdets_ids:
            print('attach_cdet_report ' +cdets_id)
            print("cdets_id ", cdets_id)
            print("cdets_encl_report_name ",self.cdets_encl_report_name )
            print("report_file ",self.report_file )
            print("pwd " +os.getcwd())
            print("attachment_dir " +self.attachment_dir)
            print("File location "+self.attachment_dir + self.report_file)
            #os.system('cat '+ self.attachment_dir + self.report_file)
            #os.system('/usr/cisco/bin/addfile -o  '+ cdets_id +' ' + self.cdets_encl_report_name + ' ' + self.attachment_dir + self.report_file)
            cdets_libs.add_file(cdets_id, self.cdets_encl_report_name, self.attachment_dir + self.report_file, True)
            
    def set_archive_file_name(self, java_sa=False):
        if java_sa:
            file = 'spotbugs.html'
        else:
            file = 'Static_Analysis.txt'
        content = '\narchive_file = ' + file
        append_to_file(content, self.filename )

class JavaSA(SACls):
    
    def __init__(self):
        super().__init__()
        res = '/tmp/'
        if self.python_script_dir.endswith('releng-tools/scripts/SA'):
            res = re.sub('releng-tools/scripts/SA', '' , self.python_script_dir)
        self.ws = res
        self.mvn_repo_arg = '-Dmaven.repo.local=' + res + 'local/repo/'
        self.need_external_libs = True

    def run_sa(self, is_target_branch=False):
        
        src_dir='src'
        for path, dirs, files in os.walk('.'):
                if src_dir in dirs:
                        os.chdir(path)
                        break
        self.state = "failure"
        xml_report_file='/tmp/{}_xsp.xml'.format(self.pr_number)
        output_file = self.source_output_file
        if is_target_branch:
            output_file = self.target_output_file
            os.system('rm -rf '+xml_report_file)
            os.system('touch '+xml_report_file)
        self.modify_pom_file()
        self.check_for_parent()
        with open('sapom.xml', 'r') as file :
                filedata = file.read()
        file.close()
        # Replace the file name to pr numbe+ _xsp
        filedata = filedata.replace('xsp', self.pr_number+'_xsp')
        # Create the test_pom
        with open('test_pom.xml', 'w') as file:
                file.write(filedata)
        file.close()
        stgs = ''
        if os.path.isfile('./settings.xml'):
            stgs = ' -s settings.xml'
        if is_target_branch:
                mvn_build_cmd = '/auto/mitg-sw/tools/apache-maven-3.6.3/bin/mvn ' + self.mvn_repo_arg +'  clean install   spotbugs:spotbugs -f test_pom.xml ' + stgs + '  > output.txt 2>&1'
        else:
                mvn_build_cmd = '/auto/mitg-sw/tools/apache-maven-3.6.3/bin/mvn ' + self.mvn_repo_arg +'   clean compile site   spotbugs:check -f test_pom.xml ' + stgs + '  > output.txt 2>&1'
        if os.system(mvn_build_cmd) == 0:
                self.state = "success"
                if is_target_branch:
                        os.system('cp target/site/nosite/spotbugsXml.xml ' + xml_report_file )
        #cdets_encl_report_name = self.repo_name + '_' + self.pull_from_br + '_' + self.pr_number + '_Static_Analysis.html' 
        os.system('cp target/site/spotbugs.html '+pwd)
        os.system('cp target/site/spotbugs.html '+self.ws)
        #os.system('/usr/cisco/bin/addfile -o  '+ self.cdets_id +' ' +cdets_encl_report_name +' target/site/spotbugs.html')
        #os.system('/usr/cisco/bin/addfile -o  '+ self.cdets_id +' ' +'Static-analysis.html' +' target/site/spotbugs.html')
        print("state :", self.state)
        os.system('cat output.txt')
        os.rename("output.txt", output_file)
        
    def modify_pom_file(self):
        os.system('cp '+ self.python_script_dir + '/*.xml .')
        os.system('cp '+ self.python_script_dir + '/modifypom.sh .')
        os.system('./modifypom.sh')
        
    def check_for_parent(self):
        if os.system('grep java-lib-tools  pom.xml') == 0:
            self.get_source_code("clone",None,True)
        if not self.need_external_libs:
            self.need_external_libs = False
            return                          ## No external dependencies to download
        self.need_external_libs = False     ## Turning off , not to download in the target branch
        lib_path = os.getcwd() + '/lib/jdiameter'
        if 'cps-diameter-ep' in lib_path:
            print('lib_path :' +lib_path )
            URL="https://engci-maven-master.cisco.com/artifactory/cps-release/microservices/Thirdparty/Jdiameter/"
            jar_list = ['commons-1.0.0.CR1.jar', 'javolution-5.5.1.jar', 'jdiameter.jar','picocontainer-2.13.5.jar','sctp-api-1.7.8.jar','sctp-impl-1.7.8.jar']
            if not os.path.isdir(lib_path):
                os.makedirs(lib_path)
            for jar in jar_list:
                jar_url = URL + jar
                os.system('curl -o  {lib_dir}/{jar_file}  {j_url}'.format(lib_dir = lib_path, jar_file = jar, j_url = jar_url) )
            
    class Factory:
        def create(self): return JavaSA()


#--------------------------------------------------------------------#
# Parse arguments                                                                                                                                                                                                                           
parser = argparse.ArgumentParser()
parser.add_argument('-url', '--pr_url', required=True, help="pr_url")
#parser.add_argument('-db', '--DB_INST', required=False, help='Please specify the DB_INST type either dev or prod')
args = parser.parse_args()
'''
if args.DB_INST:
    DB_INST = args.DB_INST
else:
    DB_INST = '5g-prod'
'''
pull_req_url = args.pr_url.rstrip("/")

#sa_class, pull_data = check_src_lang(pull_req_url)
try:
    pull_data = requests.get(pull_req_url+'/files', auth=HTTPBasicAuth(constants.USERNAME, constants.FULL_TOKEN))
except Exception as e:
                sys.exit("ERROR: Failed to get the modifoed files from PR: %s\n\n" % e)  

sa_obj   = SAFactory.createSACls("JavaSA")

sa_obj.init_vars(pull_req_url)

if isinstance(sa_obj, JavaSA):
    sa_obj.set_pending_status()
    sa_obj.set_archive_file_name(True)
    sa_obj.stamp_ut_status()
    sa_obj.stamp_sa_status()
    sa_obj.get_source_code("clone", sa_obj.controlled_branch)
    sa_obj.get_source_code("checkout", sa_obj.controlled_branch)
    sa_obj.get_source_code("pull", sa_obj.controlled_branch)
    sa_obj.run_sa(True)    ## SA for the controlled_branch
    sa_obj.get_source_code("checkout", sa_obj.pull_from_br)
    sa_obj.get_source_code("pull", sa_obj.pull_from_br)
    sa_obj.run_sa()        ## SA for the source branch
    sa_obj.stamp_sa_status()
    sa_obj.set_attachment_dir(False)
    sa_obj.add_report_file_extn('Static-analysis.html', 'spotbugs.html')
    if sa_obj.state == "failure" or sa_obj.attach_clean_enclosure == True :
        sa_obj.attach_cdet_report(True)
    sys.exit(sa_obj.get_return_status())
