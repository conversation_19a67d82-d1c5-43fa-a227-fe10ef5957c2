"""
 Copyright (c) 2023, Cisco Systems Inc.
 author: skunjir
 This job is to trigger sonar analysis for all repos for a given product and branch
# Flow of the script
# input : product, test_type, reference_branch & branch name as a parameter
# Flow of Script
# For a given product -> get the list of github orgs
# 1. Check if this org & branch is supported for sonar
# 2. If yes, get the list of repos which are supported
# 3. Loop through the list of repos
# 4. Trigger sonar analysis for all supported repos of a product
"""

import sys
import os
import argparse
import time
import re

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

sonar_checks_ini = BASE_DIR + "/libs/sonar_checks.ini"

from GithubApi import GithubApi
import Constants as Constants
from sonar_utils import list_supported_repos_branches
from Utils import *
from cnjenkins import CNJenkins
from version_info import version_info
from sonar_utils import create_application_branch, get_branch_info_application, update_application_branch, delete_application_branch, get_app_sonar_matrix, sonar_db_update, delete_sonar_branch, get_sonar_project, get_cc_branch, get_parent_branch, check_sonar_branch_existance


def execute_sonar_analysis(product_org_list,product,test_type,reference_branch,sonar_branch,branch,project_date,cc_build_number,push_old_data,delete_branch,githubobj,jenkins_obj,ut_branch,ft_branch,st_branch,ut_coverage_trigger,feature_branch):
    if ut_coverage_trigger:
        job_name = "Developer_Jobs/code_coverage_branch_admin"
    else:
        job_name = "Code_Coverage/sonar_analysis"
    unsuccessful_builds = []
    job_name_number_list = []
    sonar_projects = []
    for organization in product_org_list:    
        print("=" * 50)
        print(organization)
        print("=" * 50)
        supported_repos_list = list_supported_repos_branches(organization)
        if not supported_repos_list:
            print("%s org is not supported for sonar" % organization)
            continue

        # If branch is suported, loop through the list of repos supported.
        for repo in supported_repos_list:
            if githubobj.check_branch_existance(organization, repo, branch):
                sonar_project = get_sonar_project(organization, repo)
                sonar_projects.append(sonar_project)
                print("=" * 50)
                print("Trigger sonar_analysis job for %s:%s"%(organization,repo))
                if ut_coverage_trigger:
                    if delete_branch:
                        sonar_branch_delete_status = delete_sonar_branch(sonar_project, sonar_branch)
                        if not sonar_branch_delete_status:
                            print("Warning: Unable to delete branch in sonar")
                            print("Continue...")
                        else:
                            print("Successfully deleted %s branch in sonar" % sonar_branch)
                    parameter = {"ORGANIZATION" : organization,
                                 "REPO" : repo,
                                 "BRANCH_NAME" : branch,
                                 "REFERENCE_BRANCH" : reference_branch,
                                 "REFERENCE_QA_BUILD" : cc_build_number,
                                 "PRODUCT" : product
                                 }
                else:
                    parameter = {"PRODUCT" : product,
                                "ORGANIZATION" : organization,
                                "REPO" : repo,
                                "TEST_TYPE" : test_type,
                                "REFERENCE_BRANCH" : reference_branch,
                                "SONAR_BRANCH" : sonar_branch,
                                "BRANCH" : branch,
                                "PROJECT_DATE" : project_date,
                                "CC_BUILD_NUMBER" : cc_build_number,
                                "Push_old_data" : push_old_data,
                                "UT_BRANCH" : ut_branch,
                                "FT_BRANCH" : ft_branch,
                                "ST_BRANCH" : st_branch,
                                "Delete_Branch" : delete_branch,
                                "FEATURE_BRANCH": feature_branch,
                                "RELENG_BRANCH_NAME": os.environ.get('RELENG_BRANCH_NAME')}

                print("job_name : %s" % job_name)
                print("parameter : %s" % parameter)
                queue_id = jenkins_obj.build_job(job_name,parameter)
                if not queue_id:
                    print("Build got aborted : %s" % queue_id)
                else:
                    print("Waiting for Build to start.... Jenkins Queue ID %s" % queue_id)
                    build_url = None
                    while build_url is None:
                        queue_detail = jenkins_obj.server.get_queue_item(queue_id)
                        #pprint(queue_detail)
                        if queue_detail:
                            exes = queue_detail.get('executable')
                            if exes:
                                build_url = exes.get('url')
                                if build_url:
                                    build_number = build_url.split('/')[-2]
                                    print("Build Triggered : %s" % build_url)
                                    job_name_number_list.append(job_name+":"+build_number)
                                    break
                                else:
                                    time.sleep(10)
                        else:
                            build_url = "Aborted"
    print("Following builds will be monitored")
    print(job_name_number_list)

    if job_name_number_list:
        job_name_status_list = jenkins_obj.monitor_multiple_builds_list(job_name_number_list, timeout=10800)
        if job_name_status_list:
            for job_info in job_name_status_list:
                job_name = job_info.split(":")[0]
                build_number = job_info.split(":")[1]
                build_status = job_info.split(":")[2]
                if build_status not in ['SUCCESS']:
                    unsuccessful_builds.append(job_name+"/" + build_number)
        else:
            print("Issue in monitoring the jobs triggered on %s" % job_name)
    if unsuccessful_builds:
        print("Following builds are unsuccessful")
        print(unsuccessful_builds)
        return False,sonar_projects
    else:
        return True,sonar_projects
      

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('-p', '--product', required=True, help="product")
    parser.add_argument('-branch', '--branch', required=True, help="branch")
    parser.add_argument('-feature_id', '--feature_id', help="enter the feature id")
    parser.add_argument('-mail_to', '--mail_to', help="enable if it's a feature branch")
    parser.add_argument('-t','--test_type',required=True, choices=['FT','CC','ST','UT','all'], help="test type")
    parser.add_argument('-rb','--reference_branch', help="reference_branch" )
    parser.add_argument('-ub','--ut_branch', help="UT branch" )
    parser.add_argument('-fb','--ft_branch', required=False, help="FT branch" )
    parser.add_argument('-stb','--st_branch', required=False, help="ST branch" )
    parser.add_argument('-sb', '--sonar_branch', help="branch")
    parser.add_argument('-date','--project_date', required=False, help="project_date" )
    parser.add_argument('-push_old', '--push_old_data', required=False, default='False', help = 'Push base branch data only & delete the branch as well')
    parser.add_argument('-bn','--cc_build_number', help="project_Version" )
    parser.add_argument('-org','--organization', required=False, help="organization name" )
    parser.add_argument('-bv','--base_version', help="build number to be used as new code period start point" )
    parser.add_argument('-d', '--db_name', required=True, default='5g-prod', help='database name')
    parser.add_argument('-sab', '--sonarapp_branch', help='sonar application branch name')
    parser.add_argument('-scr', '--scheduled_run', default='False', help = 'Source parent branch as source code for coverage analysis')

    args = parser.parse_args()
    sonar_application_branch = []
    # read inputs

    running_node = os.uname()[1]
    if running_node in Constants.HOS_HOSTS:
        CC_NFS_MOUNT = Constants.HOS_CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.HOS_TEST_CC_NFS_MOUNT
    else:
        CC_NFS_MOUNT = Constants.CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.TEST_CC_NFS_MOUNT

    config_section_name=args.db_name
    version_info_obj = version_info(config_section_name=config_section_name)
    dbo = version_info_obj.open_connection()

    if args.ft_branch:
        ft_branch = args.ft_branch
    else:
        ft_branch = ""

    if args.st_branch:
        st_branch = args.st_branch
    else:
        st_branch=""
        
    if args.cc_build_number:
        cc_build_number = args.cc_build_number
    else:
        cc_build_number = ""
    branch = args.branch
    if args.product:
        product = args.product.lower()
        if product == 'cnsgw':
            product = "sgw"
        if product == 'cnpgw':
            product = 'smf'
            branch = get_cc_branch(cc_build_number,dbo)
        if product == 'smf':
            branch = get_cc_branch(cc_build_number,dbo)
        if product in ['smf','sgw']:
            product_name = "ccg"
        else:
            product_name = product   

    test_type = args.test_type
    sonarapp_bname = args.sonarapp_branch
    reference_branch = args.reference_branch
    ut_branch = args.ut_branch
    base_version = args.base_version
    feature_id = args.feature_id

    if (args.push_old_data in ('true' , 'True', '1' )):
        push_old_data = True 
    else:
        push_old_data = False

    if (args.scheduled_run in ('true' , 'True', '1' )):
        scheduled_run = True
    else:
        scheduled_run = False

    if args.project_date:
        project_date = args.project_date
    else:
        project_date = ""     

    githubobj = GithubApi()
    jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])
    unsuccessful_builds = []

    # get list of org which makes a product.
    product_org_list = []
    if args.organization:
        product_org_list.append(args.organization)
    else:
        if product in ['smf','sgw','amf','ccg','ulb','cnvpc']:
            product_org_list = Utils.get_product_orgs(product, "0")
        else:
            organization = "mobile-cnat-%s"%product
            product_org_list.append(organization)

    application_content = None
    if test_type == "UT" and feature_id:
        if not reference_branch:
            print("Reference branch is a mandatory parameter when feature branch is selected")
            sys.exit(1)

        ut_coverage_trigger = True
        if not args.base_version:
            if reference_branch == "main":
                base_version=""
        # Base Sonar Analysis
        sonar_status, sonar_projects = execute_sonar_analysis(product_org_list,product,test_type,reference_branch,branch,branch,project_date,base_version,True,True,githubobj,jenkins_obj,ut_branch,ft_branch,st_branch,ut_coverage_trigger,True)
        if sonar_status:
            print("Sonar base analysis is successful for %s" % reference_branch)
        else:
            print("Sonar analysis is failed for %s" % reference_branch)
            print("continue... ")
        if sonar_projects:
            print("Following projects needs to be used : %s" % sonar_projects)
            sonar_application_name = Constants.SONAR_FEATURE_APPLICATION
            sonar_application_branch = "UT-%s" % feature_id
            branch_info = get_branch_info_application(sonar_application_name,sonar_application_branch)
            if branch_info:
                delete_application_branch(sonar_application_name,sonar_application_branch)

            successful_project_list = []
            for s_project in sonar_projects:
                result = check_sonar_branch_existance(s_project, branch)
                if result == True:
                    successful_project_list.append(s_project)

            project_branches = [branch] * len(successful_project_list)
            create_branch_status = create_application_branch(sonar_application_name, sonar_application_branch, successful_project_list, project_branches)
            if create_branch_status:
                print("successfully created a branch")
                application_content = Constants.SONAR_URL+ "/code?branch=" + sonar_application_branch + "&id=" + sonar_application_name
                print("Sonar application result available at %s" % application_content)

        # Sonar Analysis on branch
        sonar_status, sonar_projects = execute_sonar_analysis(product_org_list,product,test_type,"",branch,branch,project_date,cc_build_number,False,False,githubobj,jenkins_obj,ut_branch,ft_branch,st_branch,ut_coverage_trigger,False)
        if sonar_status:
            print("Sonar branch analysis is successful for %s" % branch)

        # update sonar table
        ref_branch_version = version_info_obj.get_branch_version(product_org_list[0],reference_branch)
        print("ref_branch_version is %s" % ref_branch_version)
        sonar_db_update(feature_id,None,dbo,"UT",ref_branch_version, product_name)

        job_name = "Developer_Jobs/sonar_report"
        parameter = {"PRODUCT" : product_name,
                     "QA_BUILD" : "",
                     "RELEASE_VERSION" : ref_branch_version,
                     "BRANCH_NAME" : branch,
                     "EMAIL_SUBJECT" : "5G Feature Sonar Report",
                     "EMAIL_TO" : args.mail_to,
                     "FEATURE_ID" : feature_id
                     }
        print("Execute Sonar report: %s" % job_name)
        print(parameter)
        jenkins_obj.trigger_build_params(job_name,parameter)
        sys.exit(0)
    else:
        if not ut_branch:
            if product == 'ccg':
                organization = "mobile-cnat-cn"
            elif product == 'ulb':
                if args.organization:
                    organization = args.organization
                else:
                    print ("Organization parameter is required for ulb")
                    sys.exit(1)
            else:
                organization = "mobile-cnat-%s"%product
            parent_branch,reference_branch = get_parent_branch(branch,organization,dbo)
            ut_branch = parent_branch
            print ("UT branch picked as per DB entry : %s" % parent_branch)

        if not reference_branch:
            if product == 'ccg':
                organization = "mobile-cnat-cn"
            else:
                organization = "mobile-cnat-%s"%product
            parent_branch,reference_branch = get_parent_branch(branch,organization,dbo)
            print ("Reference branch picked as per DB entry : %s" % reference_branch)

        if not sonarapp_bname:
            sonarapp_bname = parent_branch
            print ("Sonar application branch name picked as per DB entry : %s" % sonarapp_bname)

        if not base_version:
            base_version = version_info_obj.get_branch_point(product_name,reference_branch)
            print("Using %s as Base_QA_BUILD"%base_version)
        if test_type =='all':
            test_type_list = ['FT','CC','ST']
        else:
            test_type_list = [test_type]
        if ((product in ['smf']) and (test_type in ['FT','CC','all'])):
            if ft_branch !="":
                smf_cc_location = "%s/%s/%s/smf_latest/FT/logs/"%(CC_NFS_MOUNT,ft_branch,product)
                pgw_cc_location = "%s/%s/%s/smf_latest/FT/cnpgw/"%(CC_NFS_MOUNT,ft_branch,product)
        # For SMF check whether both SMF and IOT(PGW) cc reports are vailable or not
            else:
                smf_cc_location = "%s/%s/%s/%s/FT/logs/"%(CC_NFS_MOUNT,branch,product,cc_build_number)
                pgw_cc_location = "%s/%s/%s/%s/FT/cnpgw/"%(CC_NFS_MOUNT,branch,product,cc_build_number)
            print(smf_cc_location)
            if os.path.exists(smf_cc_location):
                print("SMF CC report is available at %s"%smf_cc_location)
                # check pgw cc reports
                if os.path.exists(pgw_cc_location):
                    print("PGW CC report is available at %s"%pgw_cc_location)
                else:
                    print("PGW CC report not available at %s"%pgw_cc_location)
                    sys.exit(1)
            else:
                print("SMF CC report not available at %s"%smf_cc_location)
                sys.exit(1)
            
        for items in test_type_list:
            sonar_branch = None
            if args.sonar_branch:
                sonar_branch = args.sonar_branch
            elif items == "UT":
                sonar_branch = branch
            elif items in ['FT','CC','ST']:
                sonar_branch = items + "-" + branch
            print("Sonar branch %s and reference branch %s and test_type %s" % (sonar_branch,reference_branch,items))
            if items == "UT":
                ut_coverage_trigger = True
            else:
                ut_coverage_trigger = None

            # Base Sonar Analysis
            if test_type == 'CC' and scheduled_run:
                sonar_status, sonar_projects = execute_sonar_analysis(product_org_list,product,items,reference_branch,sonar_branch,parent_branch,project_date,base_version,True,True,githubobj,jenkins_obj,ut_branch,ft_branch,st_branch,ut_coverage_trigger,False)
            else:
                sonar_status, sonar_projects = execute_sonar_analysis(product_org_list,product,items,reference_branch,sonar_branch,branch,project_date,base_version,True,True,githubobj,jenkins_obj,ut_branch,ft_branch,st_branch,ut_coverage_trigger,False)
            if sonar_status:
                print("Sonar analysis is successful for %s" %base_version)
                print("value of push_old_data %s" % push_old_data)
                if push_old_data:
                    sys.exit(1)
            else:
                print("Sonar analysis is failed for %s" %base_version)
                print("value of push_old_data %s" % push_old_data)
                if push_old_data:
                    sys.exit(1)

        print("Wait for 5 mins to get all branches on sonarqube")
        time.sleep(300)

        project_list=[]
        project_branches = []
        product_org_list_temp = product_org_list[:]
        if 'mobile-cnat-golang-lib' in product_org_list_temp:
            product_org_list_temp.remove('mobile-cnat-golang-lib')
        if product == 'smf' and not args.organization:
            product_org_list_temp.append('mobile-cnat-sgw')
        if product == 'sgw' and not args.organization:
            product_org_list_temp.append('mobile-cnat-smf')
        if sonarapp_bname != '':
            for organization in product_org_list_temp:
                print("=" * 50)
                print(organization)
                print("=" * 50)
                application = organization + '-application'
                if product == 'ulb':
                    application = 'lbs-application'
                elif product == 'cnvpc':
                    application = 'mobile-cnat-cnvpc-application'
                print ("Sonar application to be updated %s" % application)

                for entries in Constants.sonar_application_mapping[application]:
                    for branches,repo in entries.items():
                        for items in test_type_list:
                            if product == 'smf':
                                pdt_exclude = 'sgw'
                                app_repo_exclude = 'mobile-cnat-'+ pdt_exclude + '-application'
                            elif product == 'sgw':
                                pdt_exclude = 'smf'
                                app_repo_exclude = 'mobile-cnat-'+ pdt_exclude + '-application'
                            else:
                                pdt_exclude = 'sgw'
                                app_repo_exclude = 'mobile-cnat-'+ pdt_exclude + '-application'
                            if re.match('app_repo',branches) and not args.organization and (application != app_repo_exclude or product == pdt_exclude):
                                sonar_application_branch = items + "_"+ sonarapp_bname
                                project_list = repo
                                if args.sonar_branch:
                                    project_branches = [args.sonar_branch] * len(repo)
                                elif items == 'UT':
                                    project_branches = [branch] * len(repo)
                                else:
                                    branch_name = items + "-" + branch
                                    project_branches = [branch_name] * len(repo)
                            if re.match('app_repo',branches) and args.organization:
                                sonar_application_branch = items + "_"+ sonarapp_bname
                                project_list = repo
                                if args.sonar_branch:
                                    project_branches = [args.sonar_branch] * len(repo)
                                elif items == 'UT':
                                    project_branches = [branch] * len(repo)
                                else:
                                    branch_name = items+ "-" + branch
                                    project_branches = [branch_name] * len(repo)

                            if re.match('common_repo',branches) and not args.organization:
                                sonar_application_branch = items +"_CL_"+ sonarapp_bname
                                project_list = repo
                                if args.sonar_branch:
                                    project_branches = [args.sonar_branch] * len(repo)
                                elif items == 'UT':
                                    project_branches = [branch] * len(repo)
                                else:
                                    branch_name = items + "-" + branch
                                    project_branches = [branch_name] * len(repo)

                            if (type(project_list) == list and project_list != [] and project_branches != []):
                                project_list=",".join(project_list)
                                print ("Project list %s" % project_list)
                                branch_info = get_branch_info_application(application,sonar_application_branch)
                                if not branch_info:
                                    print("%s branch doesn't exists in sonar application" % sonar_application_branch)
                                    print("Creating application branch %s:%s"%(application,sonar_application_branch))
                                    create_branch_status = create_application_branch(application, sonar_application_branch, project_list.split(","), project_branches)
                                    if create_branch_status:
                                        print("successfully created a branch")
                                        application_content = Constants.SONAR_URL+ "/code?branch=" + sonar_application_branch + "&id=" + application
                                        print("Sonar application result available at %s" % application_content)
                                else:
                                    app_project_list = branch_info['application']['projects']
                                    selected_project_list = []
                                    selected_project_branch = []
                                    for project_info in app_project_list:
                                        if project_info['selected'] == 'True':
                                            selected_project_list.append(project_info['key'])
                                            selected_project_branch.append(project_info['branch'])

                                    for proj in project_list.split(","):
                                        selected_project_list.append(proj)

                                    for proj_br in project_branches:
                                        selected_project_branch.append(proj_br)

                                    print("project")
                                    print(selected_project_list)

                                    print("branch")
                                    print(selected_project_branch)

                                    update_branch_status = update_application_branch(application, sonar_application_branch, selected_project_list, selected_project_branch)
                                    if update_branch_status:
                                        print("successfully updated")
                                        application_content = Constants.SONAR_URL+ "/code?branch=" + sonar_application_branch + "&id=" + application
                                        print("Sonar application result available at %s" % application_content)

        print("Product organization list %s" % product_org_list)
        for items in test_type_list:
            sonar_branch = None
            if args.sonar_branch:
                sonar_branch = args.sonar_branch
            elif items == "UT":
                sonar_branch = branch
            elif items in ['FT','CC','ST']:
                sonar_branch = items + "-" + branch
            print("Sonar branch %s and reference branch %s and test_type %s" % (sonar_branch,reference_branch,items))

            reference_branch = ''
            
            if items == "UT":
                ut_coverage_trigger = True
            else:
                ut_coverage_trigger = None
            # Sonar Analysis on branch
            if items == "CC" and scheduled_run:
                for key,value in Constants.st_branch.items():
                    if key == parent_branch:
                        st_branch = value
                        print("ST branch chosen:%s" % st_branch)
                sonar_status, sonar_projects = execute_sonar_analysis(product_org_list,product,items,reference_branch,sonar_branch,parent_branch,project_date,"",False,False,githubobj,jenkins_obj,ut_branch,branch,st_branch,ut_coverage_trigger,False)
            else:
                sonar_status, sonar_projects = execute_sonar_analysis(product_org_list,product,items,reference_branch,sonar_branch,branch,project_date,cc_build_number,False,False,githubobj,jenkins_obj,ut_branch,ft_branch,st_branch,ut_coverage_trigger,False)
            if sonar_status:
                print("Sonar analysis is successful for %s" %cc_build_number)
            else:
                print("Sonar analysis is failed for %s" %cc_build_number)


    print("Sonar application result available at %s" % application_content)

if __name__ == "__main__":
    sys.exit(main())
