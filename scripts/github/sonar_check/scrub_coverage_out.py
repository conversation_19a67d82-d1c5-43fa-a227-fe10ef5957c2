# Flow of script
import argparse
import os
import re
import sys

def analyze_go_file(file_path):
    # Patterns to search for
    comment_pattern = re.compile(r'^\s*//')  # Line starts with optional whitespace followed by '//'
    func_pattern = re.compile(r'^\s*func\s+')  # Line starts with optional whitespace followed by 'func' and at least one space
    case_pattern = re.compile(r'^\s*case\s+')  # Line starts with optional whitespace followed by 'func' and at least one space
    empty_line_pattern = re.compile(r'^\s*$')
    #brace_pattern = re.compile(r'\s*[{}]\s*')  # Line contains '{' or '}' with optional surrounding whitespace
    no_code_lines = set()
    # Open the .go file and read line by line
    with open(file_path, 'r') as file:
        for line_number, line in enumerate(file, start=1):
            if comment_pattern.search(line):
                no_code_lines.add(line_number)
            if func_pattern.search(line):
                no_code_lines.add(line_number)
            if case_pattern.search(line):
                no_code_lines.add(line_number)
            if empty_line_pattern.search(line):
                no_code_lines.add(line_number)
    file.close()
    return(sorted(no_code_lines))

def parse_coverage_line(line):
    # Regex pattern to match file path and coverage information
    coverage_lines = []
    coverage_info = line.split(":")[1]
    start_line_num = int(coverage_info.split(",")[0].split(".")[0])
    end_line_num = int(coverage_info.split(",")[1].split(".")[0])
    lines_considered = coverage_info.split(" ")[-2]
    is_lines_covered = coverage_info.split(" ")[-1]
    for i in range(start_line_num, end_line_num):
        coverage_lines.append(i)
    coverage_lines.append(end_line_num)
    return coverage_lines,is_lines_covered


def create_subsets(original_list, to_remove):
    # Step 1: Filter out the elements that need to be removed
    filtered_list = [x for x in original_list if x not in to_remove]

    # Step 2: Determine the subsets
    subsets = []
    start_index = 0
    while start_index < len(filtered_list):
        # Find the end index for the current subset
        end_index = start_index
        while end_index + 1 < len(filtered_list) and filtered_list[end_index + 1] - filtered_list[end_index] == 1:
            end_index += 1

        # Add the subset to the list
        subsets.append(filtered_list[start_index:end_index + 1])

        # Move to the next subset
        start_index = end_index + 1

    return subsets


def are_elements_in_list(small_list, large_list):
    for elem in small_list:
        if elem in large_list:
            return True


def find_end_columns(filename, line_number):
    with open(filename, 'r') as f:
        content = f.readlines()
    f.close()
    line = content[line_number - 1]
    end_col = len(line)
    return end_col


def write_to_file(content, filename):
    """
    write the content to the file
    """
    print("Writing to file : %s" % filename)
    if type(content) is not list:
        content = content.encode('ascii', 'ignore').decode()
    if os.path.isfile(filename):
        os.remove(filename)
    with open(filename, 'w') as f:
        if type(content) is list:  # If list, write it to file, line by line
            f.write("\n".join(content))
        else:
            f.write(content)
    f.close()

def reformat_coverage_out(clone_location, coverage_input_file,coverage_output_file):
    print("clone_location : %s" % clone_location)
    if not os.path.isdir(clone_location):
        print("Clone loc is not valid")
        sys.exit(1)

    print("coverage_input_file : %s" % coverage_input_file)
    with open(coverage_input_file, 'r') as f:
        coverage_input_content = f.read().splitlines()
    f.close()
    print("coverage_input_content: %s" %coverage_input_content)
    coverage_output_content = []
    dic_no_code = {}
    # copy mode of the input coverage file to output file
    coverage_output_content.append(coverage_input_content[0])
    # read the input coverage file from line 2
    for line in coverage_input_content[1:]:
        # Read the file name
        file_name=line.split(":")[0]
        if file_name.startswith("wwwin-github.cisco.com"):
            #file_name = file_name.split(".git")[0].split("/")[-1] + file_name.split(".git")[1]
            file_name = file_name.split(".git")[1]
        # Get the file path
        file_path = clone_location + "/" + file_name
        print ("file_path: %s" % file_path)
        if not os.path.isfile(file_path):
            print("File doesn't exists : %s" % file_path)
            continue
        # Get the list of lines which doesn't have a code, instead have commented lines
        if file_path not in dic_no_code:
            dic_no_code[file_path] = analyze_go_file(file_path)

        no_code_lines = dic_no_code[file_path]

        # parse the current line to get the coverage lines which are in the file
        coverage_lines, is_lines_covered =  parse_coverage_line(line)
        if are_elements_in_list(no_code_lines, coverage_lines):
            subsets = create_subsets(coverage_lines,no_code_lines)
            print(is_lines_covered)
            print(subsets)
            if subsets:
                # Sample line : "smf-service/collisioncallback/collisionAppCallback.go:34.10,34.100 1 1"
                for sub in subsets:
                    with open(file_path, 'r') as f:
                        file_content = f.readlines()
                    f.close()
                    # Find the start index & col
                    start_index = int(sub[0])
                    start_line = file_content[start_index -1]
                    print("Line at start index : %s" % start_line)
                    stripped_line = start_line.lstrip()
                    first_char_index = start_line.find(stripped_line[0])
                    start_col = first_char_index + 1
                    end_index = int(sub[-1])
                    end_line = file_content[end_index -1 ]
                    print("Line at end index : %s" % end_line)
                    end_col = len(end_line)
                    lines_to_cover= len(sub)
                    new_line = "%s:%s.%s,%s.%s %s %s" % (file_name,start_index,start_col,end_index, end_col,lines_to_cover,is_lines_covered)
                    coverage_output_content.append(new_line)
                    print("New line is : %s" % new_line)
        else:
            coverage_output_content.append(line)

        print("coverage_output file : %s" % coverage_output_file)
        write_to_file(coverage_output_content, coverage_output_file)


def main(**kwargs):

    parser = argparse.ArgumentParser()
    parser.add_argument('-loc', '--clone_location', required=True, help="pr_url")
    parser.add_argument('-ci', '--coverage_input_file', required=False, help="cdets_list")
    parser.add_argument('-co', '--coverage_output_file', required=False, help="cdets_list")
    args = parser.parse_args()
    reformat_coverage_out(args.clone_location,args.coverage_input_file,args.coverage_output_file )




if __name__ == "__main__":
    sys.exit(main())


