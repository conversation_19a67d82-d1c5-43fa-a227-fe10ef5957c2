'''
Flow of the script
- Find the date on which QA builds were created. -> use already existing function
- Find all the PRs merged between two QA builds dates.
- If feature ID is not given:
- query cn_repo_stats for PRs merged between two dates & also get info on org/repo/CDETS.
- Create a Map of [Feature_ID -> PR1, PR2, PR6 (sort it according to date)]
- If feature ID is given:
- query cn_repo_stats for PRs merged between two dates for given feature & also get info on org/repo/CDETS
- Feature_ID -> PR1, PR2, PR6
- create base html report
- For every feature ID
- For each repo
- Get PR info from GITHUB
-  Find the first PR which got merged
- Use the base of the first pr which got merged & create a branch sonar-feature_id
- Push the base commit code to sonar branch sonar-feature_id
- Remove jenkinsfile.smi
- For every PR
- Find the PR patch URL
- Apply patch of sonar-feature_id
- <hope no conflicts>
- Copy UT /  FT / ST coverage files from main to workspace
- In .out file if a file is mentioned which
- Run sonar on sonar-feature_id
- Have a list of sonar_links
- Loop through sonar links
- Find out loc , cc data
- Add to report
- <PERSON><PERSON> footer for the report
    - Send an email
'''


import sys
import argparse
import os
from datetime import datetime
import requests
import subprocess
import shutil
import json
import re
import time
from requests.auth import HTTPBasicAuth
from yattag import Doc

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

from GithubApi import GithubApi
import Constants as Constants
from git_libs import git_push
from sonar_utils import get_branch_info_application, create_application_branch, update_application_branch, check_code_coverage_support, get_app_sonar_matrix, update_gitignore, get_sonar_project,delete_application_branch, check_sonar_branch_existance, sonar_db_update, delete_sonar_branch
from datetime import datetime
from db import CPSOracle
from common import write_to_file, run, sendMail
from cnjenkins import CNJenkins
DB_INI=BASE_DIR+"/libs/db.ini"

def get_pr_merged_between_dates(start_date, end_date, dbo,feature_id=""):
    '''
    AI : Kirti
        For given start & end date return list of features merged.
        # Arrange the data into a dic like this
         ['feature_id_1' : ['org_1/repo_1' : ['pr_url_1','pr_url_2',''pr_url_3'],
                    'org_1/repo_2' : ['pr_url_1','pr_url_2',''pr_url_3']
                    ],
          'feature_id_2' : ['org_2/repo_1' : ['pr_url_1','pr_url_2',''pr_url_3'],
                            'org_2/repo_2' : ['pr_url_1','pr_url_2',''pr_url_3']
                            ]
         ]
    '''
    # Expect start_date & end_date to follow "YYYY-MM-DD' format
    # Convert dates into epoch
    # Query the cn_repo_stats table for the start & end_date
    # process the output to form per feature data

    if feature_id.startswith("FEAT"):
        query="select distinct FEATURE_IDS,ORG_NAME,REPO_NAME,ASSOCIATED_PR,commit_date_on_branch,commit_id from cn_repo_stats where ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') > TO_DATE('" + start_date + "', 'YYYY-MM-DD') ) and ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') < TO_DATE('" + end_date + "', 'YYYY-MM-DD') ) and FEATURE_IDS not in 'NA' and BRANCH='main' ORDER BY FEATURE_IDS";
    elif feature_id.startswith("PMOB"):
        query="select distinct EPIC_IDS,ORG_NAME,REPO_NAME,ASSOCIATED_PR,commit_date_on_branch,commit_id from cn_repo_stats where ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') > TO_DATE('" + start_date + "', 'YYYY-MM-DD') ) and ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') < TO_DATE('" + end_date + "', 'YYYY-MM-DD') ) and EPIC_IDS not in 'NA' and BRANCH='main' ORDER BY EPIC_IDS";
    else:
        print("Invalid feature id")
        return None
    print(query)
    db_data = dbo.run_query(query)
    if db_data:
        feature_repo_dic = {}
        # Extract distinct Feature IDs -- key
        for data in db_data:
            if feature_id.startswith("FEAT"):
                for f in data['FEATURE_IDS'].split(","):
                    feature_repo_dic[f]={}
            else:
                for f in data['EPIC_IDS'].split(","):
                    feature_repo_dic[f]={}

        print(feature_repo_dic)
        feature_keys = feature_repo_dic.keys()
        process_feature_keys = []
        if feature_id:
            for feature in feature_id.split(","):
                if feature in feature_keys:
                    process_feature_keys.append(feature)
                else:
                    print("Given feature ID not found for given release")
                    return None
        else:
            print("List of feature merged: %s" % feature_keys)
            process_feature_keys =  feature_keys

        if not process_feature_keys:
            print("No feature merged for given release")
            return None

        print("process_feature_keys : %s" % process_feature_keys)
        # Iterate over data to get value for that Feature ID and append it
        for feature in process_feature_keys:
            feat_dic={}
            for data in db_data:
                if feature_id.startswith("FEAT"):
                    if feature in data['FEATURE_IDS']:
                        # add only the repos for which code coverage is supported.
                        if check_code_coverage_support(data['ORG_NAME'],data['REPO_NAME']):
                            feat_dic_key = data['ORG_NAME']+"/"+data['REPO_NAME']
                            if feat_dic_key not in feat_dic :
                                pr_list = []
                            else:
                                pr_list = feat_dic[feat_dic_key]
                            if not data['ASSOCIATED_PR']:
                                print("PR Number : %s is missing for commit : %s. Plz contact Releng Team" % (data['ASSOCIATED_PR'], data['COMMIT_ID']))
                                sys.exit(1)
                            pr_list.append(data['ASSOCIATED_PR'])
                            pr_set = set(pr_list)
                            feat_dic[feat_dic_key] = list(pr_set)
                else:
                    if feature in data['EPIC_IDS']:
                        # add only the repos for which code coverage is supported.
                        if check_code_coverage_support(data['ORG_NAME'],data['REPO_NAME']):
                            feat_dic_key = data['ORG_NAME']+"/"+data['REPO_NAME']
                            if feat_dic_key not in feat_dic :
                                pr_list = []
                            else:
                                pr_list = feat_dic[feat_dic_key]
                            if not data['ASSOCIATED_PR']:
                                print("PR Number : %s is missing for commit : %s. Plz contact Releng Team" % (data['ASSOCIATED_PR'], data['COMMIT_ID']))
                                sys.exit(1)
                            pr_list.append(data['ASSOCIATED_PR'])
                            pr_set = set(pr_list)
                            feat_dic[feat_dic_key] = list(pr_set)
            feature_repo_dic[feature]=feat_dic

        final_feature_dic = {}
        for feature, pr_dic in feature_repo_dic.items():
            if pr_dic:
                final_feature_dic[feature] = pr_dic

        return final_feature_dic


def sort_pr_list(org,repo,pr_list, githubobj):
    print("Sorting PR list as per merged time")
    pr_merged_at_dict = {}
    sorted_pr_list = []
    for pr_number in pr_list:
        print("Getting PR merge time for %s" % pr_number)
        merge_timestamp = None
        if pr_number:
            merge_timestamp = githubobj.get_pr_merge_time(org,repo,pr_number)
        if merge_timestamp:
            pr_merged_at_dict[pr_number] = merge_timestamp
        else:
            print("WARNING:Merge time could not be found")
    if pr_merged_at_dict:
        print("pr_merged_at_dict")
        print(pr_merged_at_dict)
        sorted_pr_dict = dict(sorted(pr_merged_at_dict.items(), key=lambda item: item[1]))  
        sorted_pr_list = list(sorted_pr_dict.keys())
        print("sorted_pr_dict")
        print(sorted_pr_dict)
        print("sorted_pr_list")
        print(sorted_pr_list)

    if sorted_pr_list:
        base_sha = githubobj.find_pr_base_sha(org, repo, sorted_pr_list[0])
        if base_sha and sorted_pr_list:
            return base_sha, sorted_pr_list

def run_ut_branch(jenkins_obj,org_repo_pr_dic, branch, project_date, project_version):
    job_name_number_dic = {}
    job_name_number_list = []
    unsuccessful_repos = {}
    job_name = "Developer_Jobs/code_coverage_branch_admin"
    for org_repo, pr_list in  org_repo_pr_dic.items():
        org = org_repo.split("/")[0]
        repo = org_repo.split("/")[1]
        ### Need to trigger UT run for given org/repo/branch"
        parameter = {"ORGANIZATION" : org,
                     "REPO" : repo,
                     "BRANCH_NAME" : branch,
                     "PROJECT_DATE" : project_date
                     }
        print("Trigger build for %s" % parameter)
        build_url = jenkins_obj.trigger_build_params(job_name,parameter)
        if build_url != "Aborted":
            build_number = build_url.split('/')[-2]
            print("Build Triggered : %s" % build_url)
            job_name_number_dic[job_name +":" + build_number] = repo
            job_name_number_list.append(job_name +":" + build_number)
    print("Following builds will be monitored")
    print(job_name_number_list)

    job_name_status_list = jenkins_obj.monitor_multiple_builds_list(job_name_number_list, timeout=None, sleep=100)

    if job_name_status_list:
        for job_info in job_name_status_list:
            job_name = job_info.split(":")[0]
            build_number = job_info.split(":")[1]
            build_status = job_info.split(":")[2]
            if build_status not in ['SUCCESS']:
                failed_repo = job_name_number_dic[job_name +":" + build_number]
                unsuccessful_repos[failed_repo] = job_name +":" + build_number
    else:
        print("Issue in monitoring the jobs triggered on %s" % job_name)

    if unsuccessful_repos:
        print("UT run for project version %s unsuccessful for following repos" % project_version)
        print(unsuccessful_repos)
    return unsuccessful_repos

def create_cc_branch(org, repo, base_sha, feature_id, githubobj,author_cec_id):
    '''
    Steps :
    1. Create a clone
    2. Create a feature branch from base sha
    3. remove Jenkinsfile.smi from feature branch
    return :
    '''
    # clone repo
    workspace = os.environ["WORKSPACE"]
    repo_url = "https://" + Constants.USERNAME + ":" + Constants.FULL_TOKEN + "@wwwin-github.cisco.com/" + org + "/" + repo + ".git"
    clone_dir= workspace+ "/" + repo
    
    # delete repo clone if it exists in workspace
    if os.path.exists(clone_dir):
        shutil.rmtree(clone_dir)

    clone_command = "git clone %s %s" % (repo_url.strip(), clone_dir.strip())
    print("Cloning repo: %s" % clone_command)
    status = os.system(clone_command)

    if status != 0:
        print("git clone failed")
        return None

    os.chdir(clone_dir)
    # create new branch with name as feature id and checkout to that branch
    feature_branch = "dev-cc-" + str(feature_id)
    # Check if the branch exist.
    # if exist -> delete the branch
    branch_status = githubobj.check_branch_existance(org, repo, feature_branch)
    if branch_status:
        # delete the branch
        command = "git push origin --delete %s" % feature_branch
        try:
            subprocess.check_output(command, shell=True)
            print("delete the branch  %s" % feature_branch)
        except Exception as e:
            print("Error: %s"%e)
            return None

    command = "git checkout -b %s %s"%(feature_branch,base_sha)
    try:
        subprocess.check_output(command, shell=True)
        print("Successfully created and checked out branch %s"%feature_branch) 
    except Exception as e:
        print("Error: %s"%e)
        return None

    cmd = "git config --global user.name \"%s\"" % author_cec_id
    subprocess.check_output(cmd, shell=True)
    cmd = "git config --global user.email %<EMAIL>" % author_cec_id
    subprocess.check_output(cmd, shell=True)

    #  Delete Jenkinsfile.smi and update .gitignore
    if os.path.isfile("Jenkinsfile.smi"):
        os.remove("Jenkinsfile.smi")
        update_gitignore()
        git_status = git_push(feature_branch, "Removing Jenkinsfile.smi")
        if git_status == True:
            print("Deleted Jenkinsfile.smi successfully.")
        return True


def apply_pr_patch(org, repo, feature_branch, pr_list, githubobj):
    workspace = os.environ["WORKSPACE"]
    clone_dir= workspace+ "/" + repo

    # delete repo clone if it exists in workspace
    if not os.path.exists(clone_dir):
        repo_url = "https://" + Constants.USERNAME + ":" + Constants.FULL_TOKEN + "@wwwin-github.cisco.com/" + org + "/" + repo + ".git"
        clone_command = "git clone %s %s" % (repo_url.strip(), clone_dir.strip())
        print("Cloning repo: %s" % clone_command)
        status = os.system(clone_command)
        if status != 0:
            print("git clone failed")
            return None

    os.chdir(clone_dir)
    command = "git checkout %s"%(feature_branch)
    try:
        subprocess.check_output(command, shell=True)
        print("Successfully checked out branch %s"%feature_branch)
    except Exception as e:
        print("Error: %s"%e)
        return None

    cmd = "git status"
    subprocess.check_output(cmd, shell=True)

    print("Applying PR patch")

    # Process all the PR's and apply patch
    apply_failed_pr_list = []
    prs_in_conflict = {}
    try:
        subprocess.run(["git", "reset", "--hard"])
    except Exception as e:
        print("Failed to reset workspace: %s"%e)    

    for pr_number in pr_list:
        '''
        command = "git pull origin %s" % feature_branch
        try:
            subprocess.check_output(command, shell=True)
            print("Pulled patch changes from %s" % feature_branch)
        except subprocess.CalledProcessError as e:
            print("Error: %s"%e)                
        '''
        #patch_url = "%s%s/%s/pulls/%s.diff"%(Constants.GITHUB_WEB_URL,org,repo,pr_number)
        pull_request_diff_url = "https://%s:%<EMAIL>/api/v3/repos/%s/%s/pulls/%s"%(Constants.USERNAME,Constants.FULL_TOKEN,org,repo,pr_number)
        output_file = pr_number + ".txt"
        try:
            subprocess.run(
                [
                    "curl",
                    "-H", "Accept: application/vnd.github.v3.diff",
                    "-o", output_file,
                    pull_request_diff_url
                ],
                check=True,
            )
            print("Pull request diff file downloaded successfully.")
        except Exception as e:
            print("Error in downloading patch file from github: %s"%e)
            continue
        
        #command = "git apply %s --whitespace=fix "%output_file
        # --check doesn't work
        #command = "git apply --check -v --3way --ignore-space-change --whitespace=fix %s"%output_file
        #command = "git apply -v --3way --ignore-space-change --whitespace=fix %s"%output_file

        try:
            #subprocess.check_output(command, shell=True)
            files_in_pr = len(githubobj.get_pr_file_list(org,repo,pr_number))
            print("Number of files in the PR %s" % files_in_pr)
            pr_link = "https://wwwin-github.cisco.com/%s/%s/pull/%s" % (org, repo, pr_number)
            apply_command = "git apply -v --3way --ignore-space-change --whitespace=fix %s"% output_file
            print(apply_command)
            _,log_git_apply,_ = run(apply_command)
            print("Output of git apply command")
            print(log_git_apply)

            # Analyse error log of git apply command & if file is missing in index pick from main branch
            for line in log_git_apply:
                # error: images/smf_service/src/smf-service/procedures/4g/dedbearer/utilroutines_test.go: does not exist in index
                if re.search ("does not exist in index", line):
                    missing_file = line.split(":")[1]
                    print("Checking out the file from main branch")
                    checkout_command = "git checkout origin/main %s" % missing_file
                    subprocess.check_output(checkout_command, shell=True)
                    run(apply_command)

            # Take the changes of PR & solve the conflicts
            list_command = "git ls-files -u  | cut -f 2 | sort -u"
            files_in_conflict,_,_ = run(list_command)
            print("files in conflict files_in_conflict : %s" % files_in_conflict )
            prs_in_conflict[pr_link] = files_in_conflict
            for file in files_in_conflict:
                print("Taking the changes of PR to resolve merge conflict")
                checkout_cmd = "git checkout --theirs %s" % file
                subprocess.check_output(checkout_cmd, shell=True)
            
            subprocess.check_output("rm %s" % output_file, shell=True)
            #update_gitignore(clone_dir)
            commit_message = "Applied patch : pr_number %s" % pr_number
            update_gitignore()
            push_status = git_push(feature_branch, commit_message)
            if push_status == True:
                print("Patch applied and changes pushed for PR #%s on branch %s"%(pr_number, feature_branch))
                log_git_log,_,_ = run("git log -1 --name-only")
                # first 4 are commit meta data
                files_committed = len(log_git_log) - 4
                if files_committed >= files_in_pr:
                    print("Applied a patch for a PR: #%s "%pr_number)
                else:
                    print("Patching failed for PR#%s"%pr_number)
                    apply_failed_pr_list.append(pr_link)
            else:
                print("Failed to commit & push for PR#%s"%pr_number)

        except Exception as e:
            print("Failed to  apply a patch for a PR: #%s "%pr_number)
            print("%s" % e)
            # skipping git apply reject
            #command = "git apply --reject %s"%output_file
            #print(command)
            #try:
            #    subprocess.check_output(command, shell=True)
            #    print("Successfully applied a patch for a PR: #%s "%pr_number)
            #except Exception as e:
            #    print("Error: %s"%e)
            '''
                command = "git pull origin %s" % feature_branch
                try:
                    subprocess.check_output(command, shell=True)
                    print("Pulled patch changes from %s" % feature_branch)
                except Exception as e:
                    print("Error: %s"%e)
            '''

    if prs_in_conflict:
        print("***********************")
        print("PRs which had conflict but resolved")
        print(prs_in_conflict)

    if apply_failed_pr_list:
        print("***********************")
        print("Failed to apply patch for below PR's")
        for pr_link in apply_failed_pr_list:
            print(pr_link)
        print("Please manually apply these patches and run sonar analysis")
        return None, apply_failed_pr_list

    return prs_in_conflict,apply_failed_pr_list


def trigger_monitor_jenkins_job(jenkins_obj,job_name,parameter):
    job_name_number_list = []
    build_url = jenkins_obj.trigger_build_params(job_name,parameter)
    if build_url != "Aborted":
        build_number = build_url.split('/')[-2]
        job_name_number_list.append(job_name+":"+build_number)

    print("Following builds will be monitored")
    print(job_name_number_list)
    jenkins_obj.monitor_multiple_builds_list(job_name_number_list, timeout=None, sleep=100)
    return job_name_number_list


def create_sonar_app_branch(branch_name, sonar_projects, sonar_application_name, sonar_application_branch):
    print("Following projects needs to be used : %s" % sonar_projects)
    application_content = None
    branch_info = get_branch_info_application(sonar_application_name,sonar_application_branch)
    if branch_info:
        delete_application_branch(sonar_application_name,sonar_application_branch)

    successful_project_list = []
    for s_project in sonar_projects:
        result = check_sonar_branch_existance(s_project, branch_name)
        if result == True:
            successful_project_list.append(s_project)

    project_branches = [branch_name] * len(successful_project_list)
    create_branch_status = create_application_branch(sonar_application_name, sonar_application_branch, successful_project_list, project_branches)
    if create_branch_status:
        print("successfully created a branch")
        application_content = Constants.SONAR_URL+ "/code?branch=" + sonar_application_branch + "&id=" + sonar_application_name
        print("Sonar application result available at %s" % application_content)
    return application_content


def generate_feature_pr_report(org_repo_pr_dic,feature_id,feature_branch,apply_failed_pr_list):
    # HTML EMAIL TEMPLATE
    # Create HTML
    doc, tag, text = Doc().tagtext()
    doc.asis('<!DOCTYPE html>')
    email_heading = "Here is a list of PRs that were merged as a part of %s" % feature_id
    with tag('html'):
        doc.asis('<style>')
        text('th {border:1px solid white;border-collapse:collapse;}')
        doc.asis('</style>')

        with tag('body', style="font-family:Verdana; font-size:12px"):
            with tag('p',style="font-family:Verdana; font-size:12px"):
                text("Hi All")
                doc.asis('<br> <br>')
                with tag('b'):
                    text(email_heading)
                doc.asis('<br>')
            with tag('table', style="font-family:Verdana; font-size:12px"):
                length = 65
                colspan = 2

                with tag('th', style="background-color:#6082B6;color:white"):
                    text("PR Link")
                with tag('th', style="background-color:#6082B6;color:white"):
                    text("Patching status")
                for org_repo, pr_list in  org_repo_pr_dic.items():
                    org = org_repo.split("/")[0]
                    repo = org_repo.split("/")[1]
                    print("github_org name : %s" % repo)
                    with tag('tr'):
                        with tag('td',colspan=colspan):
                            for i in range(0,length):
                                text("=")
                    with tag('tr'):
                        with tag('td', colspan=colspan, style="text-align:center"):
                            with tag('a', href='https://wwwin-github.cisco.com/%s/%s/commits/%s' % (org, repo, feature_branch)):
                                text("%s : %s" % ( repo, feature_branch))
                    with tag('tr'):
                        with tag('td',colspan=colspan):
                            for i in range(0,length):
                                text("=")
                    for pr_num in pr_list:
                        pr_url = "https://wwwin-github.cisco.com/%s/%s/pull/%s" % (org,repo,pr_num)
                        with tag('tr'):
                            with tag ('td',style="text-align:center"):
                                with tag('a', href='%s' % (pr_url)):
                                    text(pr_url)
                            with tag('td',style="text-align:center"):
                                if pr_url in apply_failed_pr_list:
                                    text("Failed")
                                else:
                                    text("Success")

        with tag('p', style="font-family:Verdana; font-size:12px"):
            doc.asis('<br>')
            text("Thanks,")
            doc.asis('<br>')
            text("Releng Team")

        result = doc.getvalue()
        pr_fname = "pr_list_%s.html" % feature_id
        write_to_file(result, pr_fname)
        upload_feature_pr_html(pr_fname)
        return result


def upload_feature_pr_html(pr_fname):
    artifactory_featue_pr_list_path = "%s/%s" % (Constants.MERGED_FEATURE_ARTIFACTORY,pr_fname)
    command = 'jfrog rt upload {} "{}" --flat=false --recursive=false --url={} --user={} --password={}'.format(pr_fname, artifactory_featue_pr_list_path, Constants.ARTIFACTORY_URL, Constants.ARTIFACTORY_USER, Constants.ARTIFACTORY_PASSWORD)
    try:
        run(command)
        print("Successfully uploaded json report : %s" % pr_fname)
        return True
    except Exception as e:
        print("Upload failed for %s" % pr_fname)
        print("ERROR %s" % e)
        return None

def get_test_file_loc(repo_path, branch, base_branch='main'):
    """
    Calculate the number of new lines added in test files (*_test.go, *.test.go) 
    in a feature branch compared to the base branch.
    
    Args:
        repo_path (str): Path to the git repository
        branch (str): Feature branch name
        base_branch (str): Base branch to compare against (default: 'main')
        
    Returns:
        int: Number of new lines added in test files
    """
    if not os.path.exists(repo_path):
        print(f"Repository path does not exist: {repo_path}")
        return 0
        
    # Change to the repository directory
    current_dir = os.getcwd()
    os.chdir(repo_path)
    
    try:
        # Make sure we have the latest code
        subprocess.run(["git", "fetch", "--all"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Get the list of test files that were changed
        diff_cmd = f"git diff --name-only {base_branch}...{branch}"
        result = subprocess.run(diff_cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        changed_files = result.stdout.decode('utf-8').splitlines()
        
        # Filter for test files (*_test.go or *.test.go)
        test_files = [f for f in changed_files if f.endswith('_test.go') or f.endswith('.test.go')]
        
        if not test_files:
            return 0
            
        # Get the number of lines added for each test file
        total_lines_added = 0
        for test_file in test_files:
            # Use git diff to get the number of lines added
            diff_stat_cmd = f"git diff {base_branch}...{branch} -- {test_file}"
            diff_output = subprocess.run(diff_stat_cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            diff_lines = diff_output.stdout.decode('utf-8').splitlines()
            
            # Count added lines (lines starting with '+' but not '+++')
            added_lines = sum(1 for line in diff_lines if line.startswith('+') and not line.startswith('+++'))
            total_lines_added += added_lines
            
        # Ensure we return an integer value
        return int(total_lines_added)
    
    except subprocess.CalledProcessError as e:
        print(f"Error executing git command: {e}")
        return 0
    except Exception as e:
        print(f"Unexpected error in get_test_file_loc: {type(e).__name__}: {e}")
        return 0
    finally:
        # Return to the original directory
        os.chdir(current_dir)

def update_sonar_db_with_test_loc(feature_id, sonar_app_name, sonar_app_branch, ut_loc, dbo):
    """
    Update the FeatureCoverage table with UT_LOC data for a specific feature.
    
    Args:
        feature_id (str): Feature ID
        sonar_app_name (str): Sonar application name
        sonar_app_branch (str): Sonar application branch
        ut_loc (int): Number of lines added in test files
        dbo: Database connection object
    """
    # Ensure ut_loc is a valid integer first
    try:
        ut_loc = int(ut_loc)
    except (TypeError, ValueError):
        print(f"Invalid ut_loc value: {ut_loc}, defaulting to 0")
        ut_loc = 0
    
    # Convert to string for the database API
    # The update_rows method expects string values in the data dictionary
    table_name = "FEATURE_COVERAGE"
    data = {"UT_LOC": str(ut_loc)}  # Convert to string as expected by update_rows
    conditions = {
        "FEAT_ID": feature_id,
        "SONAR_APP_NAME": sonar_app_name,
        "SONAR_APP_BRANCH": sonar_app_branch
    }
    
    try:
        # Use update_rows method which properly handles UPDATE operations
        result = dbo.update_rows(table_name, data, conditions)
        if result == 0:
            print(f"Successfully updated UT_LOC for {feature_id} in {sonar_app_name}/{sonar_app_branch}: {ut_loc}")
            return True
        else:
            print(f"Failed to update UT_LOC in database, return code: {result}")
            return False
    except Exception as e:
        print(f"Error updating UT_LOC in database: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Generate Sonar report for a feature')
    parser.add_argument('-d', '--db_name', required=True, help="database name")
    parser.add_argument('-p', '--product', required=True, help="product name")
    parser.add_argument('-f', '--feature_id', required=False, help="feature id")
    parser.add_argument('-r', '--release_version', required=True, help="release version")
    parser.add_argument('-s', '--start_date', required=False, help="start date")
    parser.add_argument('-e', '--end_date', required=False, help="end date")
    parser.add_argument('-a', '--author_cec_id', required=True, help="author cec id")
    parser.add_argument('-m', '--mail_to', required=True, help="mail to field")

    args = parser.parse_args()
    db_env = args.db_name
    mail_to = args.mail_to
    product = args.product
    if args.feature_id:
        input_feature_id = args.feature_id
    else: 
        input_feature_id = ""    

    release_version = args.release_version

    if args.start_date:
        start_date = args.start_date
    else:
        start_date = Constants.release_start_date[release_version]

    print("Stat date for %s is %s" % (release_version, start_date))
    if args.end_date:
        end_date = args.end_date
    else:
        end_date = datetime.today().strftime('%Y-%m-%d')

    author_cec_id = args.author_cec_id

    # establish connection with DB
    try:
        dbo=CPSOracle(DB_INI, db_env)
    except Exception as e:
        sys.exit("ERROR: Failed to open db connection: %s\n\n" % e)

    feature_not_found = []
    feature_repo_dic = get_pr_merged_between_dates(start_date, end_date, dbo, input_feature_id)
    if not feature_repo_dic:
        feature_not_found.append(input_feature_id)
        sys.exit(1)
    else:
        print("Following features were merged from %s" % start_date)
        print(feature_repo_dic)

    githubobj = GithubApi()
    jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])
    for feature_id, org_repo_pr_dic in feature_repo_dic.items():
        print ("="*50)
        print("Processing feature : %s" % feature_id)
        print("org_repo_pr_dic: %s" % org_repo_pr_dic)
        print ("="*50)
        sonar_projects = []
        org_list = []
        feature_branch = "dev-cc-" + str(feature_id)
        sorted_org_repo_pr_dic = {}
        for org_repo, pr_list in  org_repo_pr_dic.items():
            org = org_repo.split("/")[0]
            org_list.append(org)
            repo = org_repo.split("/")[1]
            base_sha, sorted_pr_list = sort_pr_list(org,repo,pr_list,githubobj)
            sorted_org_repo_pr_dic[org_repo] = sorted_pr_list
            print("sorted PR list for %s/%s is: %s \n"%(org,repo,sorted_pr_list))
            print("Base sha of the %s is: %s \n"% (feature_id, base_sha))
            print ("="*50)

            # Create dev-cc branch
            branch_status = create_cc_branch(org, repo, base_sha, feature_id, githubobj,author_cec_id)
            if branch_status:
                sonar_project = get_sonar_project(org,repo)
                sonar_projects.append(sonar_project)

                # Delete sonar branch
                result = delete_sonar_branch(sonar_project, feature_branch)
                print("Sonar branch deletion status: %s" % result)

        # Trigger UT for all the repos
        run_ut_branch(jenkins_obj,org_repo_pr_dic, branch=feature_branch, project_date=start_date, project_version="%s-baseline" % feature_id)

        # Create sonar application
        sonar_application_name = Constants.SONAR_FEATURE_APPLICATION
        sonar_application_branch = "UT-merge-%s" % feature_id
        create_sonar_app_branch(feature_branch, sonar_projects, sonar_application_name, sonar_application_branch)

        # Patch PRs
        total_ut_loc = 0 
        for org_repo, pr_list in sorted_org_repo_pr_dic.items():
            org = org_repo.split("/")[0]
            repo = org_repo.split("/")[1]
            print("Applying PR Patch..Processing %s" % org_repo)
            print("PR List: %s" % pr_list)
            print ("="*50)
            prs_in_conflict, apply_failed_pr_list = apply_pr_patch(org, repo, feature_branch, pr_list, githubobj)

            # Calculate UT_LOC for this repo
            workspace = os.environ["WORKSPACE"]
            repo_path = os.path.join(workspace, repo)
            if os.path.exists(repo_path):
                repo_ut_loc = get_test_file_loc(repo_path, feature_branch)
                print(f"UT_LOC for {org}/{repo} in branch {feature_branch}: {repo_ut_loc}")
                
                # Update UT_LOC in the Sonar table for this repo
                try:
                    # First, get the latest row ID for this org/repo/branch combination
                    query = f"SELECT MAX(ID) FROM SONAR WHERE ORGANIZATION = '{org}' AND REPOSITORY = '{repo}' AND BRANCH = '{feature_branch}'"
                    result = dbo.run_query(query)
                    
                    if result and result[0]['MAX(ID)']:
                        latest_id = result[0]['MAX(ID)']
                        
                        # Update the latest row with the UT_LOC value
                        update_data = {"NEW_UT_LOC": str(repo_ut_loc)}
                        update_conditions = {"ID": str(latest_id)}
                        
                        update_result = dbo.update_rows("SONAR", update_data, update_conditions)
                        
                        if update_result == 0:
                            print(f"Successfully updated NEW_UT_LOC for {org}/{repo} in Sonar table: {repo_ut_loc}")
                        else:
                            print(f"Failed to update NEW_UT_LOC in Sonar table, return code: {update_result}")
                    else:
                        print(f"No existing Sonar entry found for {org}/{repo} in branch {feature_branch}")
                except Exception as e:
                    print(f"Error updating NEW_UT_LOC in Sonar table: {e}")
                
                total_ut_loc += repo_ut_loc  
        
        # Update database with total UT_LOC once for the feature
        print(f"Total UT_LOC for feature {feature_id}: {total_ut_loc}")
        
        try:
            total_ut_loc = int(total_ut_loc)
            print(f"Converted total_ut_loc to integer: {total_ut_loc}, type: {type(total_ut_loc)}")
        except (TypeError, ValueError) as e:
            print(f"Error converting total_ut_loc to integer: {e}, defaulting to 0")
            total_ut_loc = 0
            
        update_sonar_db_with_test_loc(feature_id, sonar_application_name, sonar_application_branch, total_ut_loc, dbo)

        # Branch Reference
        '''
        for org in org_list:
            job_name = "CN_Throttle_Pull/change_reference"
            parameter = {"ORGANIZATION" : org,
                         "BASE_BRANCH_NAME" : "main",
                         "REL_BRANCH_NAME" : feature_branch,
                         "COMMIT_AUTHOR_CEC_ID" : author_cec_id,
                         "FILE_PATH" : "base.go.mod",
                         }
            trigger_monitor_jenkins_job(jenkins_obj,job_name,parameter)
        '''

        for org_repo, pr_list in  org_repo_pr_dic.items():
            org = org_repo.split("/")[0]
            repo = org_repo.split("/")[1]
            change_reference_script = os.path.join(BASE_DIR, "scripts", "throttle_pull", "change_branch_reference.py")
            command = [
                "python3", change_reference_script,
                "--organization", org,
                "--base_branch", "main",
                "--rel_branch", feature_branch,
                "--author_cec_id", author_cec_id,
                "--file_path", "base.go.mod",
                "--repo" , repo
            ]
            result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
            
            if result.returncode != 0:
                print(f"Error while replacing references for {org}: {result.stderr}")
            else:
                print(f"Rerefence update successful for org {org}: {result.stdout}")
        # run UT
        print("Starting UT execution:")
        run_ut_branch(jenkins_obj,org_repo_pr_dic, branch=feature_branch, project_date=end_date, project_version="%s-latest" % feature_id)
        generate_feature_pr_report(org_repo_pr_dic,feature_id,feature_branch,apply_failed_pr_list)

        # Generate sonar report
        job_name = "Developer_Jobs/sonar_report"
        parameter = {"PRODUCT" : product,
                     "BRANCH_NAME" : feature_branch,
                     "FEATURE_ID" : feature_id,
                     "FEATURE_MERGED" : "true",
                     "RELEASE_VERSION" : release_version,
                     "EMAIL_TO" : mail_to,
                     "DB_INST" : db_env,
                     "EMAIL_SUBJECT" : "5G Feature Sonar Report"
                     }
        trigger_monitor_jenkins_job(jenkins_obj,job_name,parameter)

if __name__ == "__main__":
    sys.exit(main())





