"""
 Copyright (c) 2023, Cisco Systems Inc.
 author: an<PERSON><PERSON><PERSON> that can be used to create a branch in sonar application
"""

import sys
import os
import argparse

global dbo
THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")
DB_INI=BASE_DIR+"/libs/db.ini"

from sonar_utils import create_application_branch, get_branch_info_application, update_application_branch

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('-a', '--application', required=True, help="product")
    parser.add_argument('-p', '--project_list', required=True, help="org")
    parser.add_argument('-b', '--branch', required=True, help="repo")
    parser.add_argument('-pb', '--project_branches', required=True, help="branch")

    try:
        args = parser.parse_args()
    except Exception as e:
        return e
    
    application = args.application
    project_list = args.project_list
    branch = args.branch
    project_branches = args.project_branches
    branch_info = get_branch_info_application(application,branch)
    if not branch_info:
        print("%s branch doesn't exists in sonar application" % branch)
        print("Creating application branch %s:%s"%(application,branch))
        create_branch_status = create_application_branch(application, branch, project_list.split(","), project_branches.split(","))
        if create_branch_status:
            print("successfully created a branch")
    else:
        app_project_list = branch_info['application']['projects']
        selected_project_list = []
        selected_project_branch = []
        for project_info in app_project_list:
            if project_info['selected'] == 'True':
                selected_project_list.append(project_info['key'])
                selected_project_branch.append(project_info['branch'])

        for proj in project_list.split(","):
            selected_project_list.append(proj)

        for proj_br in project_branches.split(","):
            selected_project_branch.append(proj_br)

        print("project")
        print(selected_project_list)

        print("branch")
        print(selected_project_branch)

        update_branch_status = update_application_branch(application, branch, selected_project_list, selected_project_branch)
        if update_branch_status:
            print("successfully updated")

    '''
    update_application_result = update_application(application)
    if update_application_result:
        print ("Successfully updated application")
    else:
        print("Failed to create a branch")
        sys.exit(2)
    '''

if __name__ == "__main__":
    sys.exit(main())