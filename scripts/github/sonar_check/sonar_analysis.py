
"""
 Copyright (c) 2023, Cisco Systems Inc.
 author: s<PERSON><PERSON><PERSON> that can be used to execute sonar analysis for various test types
 Flow of the script:
    1) if QA build is given as input download QA build's commit_info.json
    2) Clone repo for the QA build sha for branch name
    3) Download code coverage reports from artfactory based on test typpe
    4) Update sonar-project.properties
    5) Execute sonar analysis
"""

import sys
import os
import argparse
import time
import fnmatch
import tarfile
import datetime
import shutil
import subprocess


THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")
scrub_script=THIS_DIR + "/scrub_cc.go"

os.environ["GIT_EXEC_PATH"] = os.environ["GIT_SPECIFIC_PATH"]

from GithubApi import GithubApi
import Constants as Constants
from version_info import version_info
from sonar_utils import *
from common import *
import requests
import json
import re
from requests.auth import HTTPBasicAuth


def copy_cc_reports(org,repo,test_type,src_dir,branch,product,CC_NFS_MOUNT,build_number,ut_branch,ft_branch,st_branch):
    cc_dir = src_dir + "/" + test_type
    if not os.path.exists(cc_dir):
        os.makedirs(cc_dir)
    result = False         
    if test_type != "UT":
        try:
            if org in ['mobile-cnat-cn','mobile-cnat-golang-lib','mobile-cnat-app-infra']:
                for product in ('smf','sgw','amf'):
                    print("product name : %s" % product)
                    product_link = product + "_latest"
                    if test_type == "CC":
                        cc_location = "%s/%s/%s/%s"%(CC_NFS_MOUNT,branch,product,product_link)
                    else:
                        cc_location = "%s/%s/%s/%s/%s"%(CC_NFS_MOUNT,branch,product,product_link,test_type)
                    print ("CC location %s" % cc_location)
                    if os.path.exists(cc_location):
                        result = copy_cc_reports_data(product,repo,cc_dir,cc_location,test_type)
                    if test_type == "CC" and ft_branch !="":
                        cc_location = "%s/%s/%s/%s/FT"%(CC_NFS_MOUNT,ft_branch,product,product_link)
                        print("FT folder location for Combined Code Coverage computation: %s " % cc_location)
                        result = copy_cc_reports_data(product,repo,cc_dir,cc_location,test_type="FT")
                    # this is added to consider ST result of different branch for combined code coverage computation
                    if test_type == "CC" and st_branch !="":
                        cc_location = "%s/%s/%s/%s/ST"%(CC_NFS_MOUNT,st_branch,product,product_link)
                        print("ST folder location for Combined Code Coverage computation: %s " % cc_location)
                        result = copy_cc_reports_data(product,repo,cc_dir,cc_location,test_type="ST")
            else:
                print("product name : %s" % product)
                product_link = product + "_latest"
                if test_type == "CC":
                    cc_location = "%s/%s/%s/%s"%(CC_NFS_MOUNT,branch,product,product_link)
                else:
                    cc_location = "%s/%s/%s/%s/%s"%(CC_NFS_MOUNT,branch,product,product_link,test_type)
                print ("CC location %s" % cc_location)
                if os.path.exists(cc_location):
                    result = copy_cc_reports_data(product,repo,cc_dir,cc_location,test_type)
                if test_type == "CC" and ft_branch !="":
                    cc_location = "%s/%s/%s/%s/FT"%(CC_NFS_MOUNT,ft_branch,product,product_link)
                    print("FT folder location for Combined Code Coverage computation: %s " % cc_location)
                    result = copy_cc_reports_data(product,repo,cc_dir,cc_location,test_type="FT")
                # this is added to consider ST result of different branch for combined code coverage computation
                if test_type == "CC" and st_branch !="":
                    cc_location = "%s/%s/%s/%s/ST"%(CC_NFS_MOUNT,st_branch,product,product_link)
                    print("ST folder location for Combined Code Coverage computation: %s " % cc_location)
                    result = copy_cc_reports_data(product,repo,cc_dir,cc_location,test_type="ST")
        except Exception as e:
            print("cc report copy failed") 
            print("error log:\n %s"%e)                    

    if test_type in ['CC','UT']:
        search_path=str(org)+'/'+str(repo)+'/'+str(ut_branch)
        artifactory_download_path=aql_search(repo,search_path, '*coverage.out', sort='created', limit='1', type="file")
        if artifactory_download_path:
            print("ARTIFACTORY DOWNLOAD PATH:%s" % artifactory_download_path)
            count=1
            for paths in artifactory_download_path:
                ut_report = repo+"_"+ str(count) + "_coverage.out"
                cc_result = download_ut_report(paths,cc_dir,ut_report)
                if not cc_result :
                    print("Failed to fetch CC report %s "%ut_report)
                else:
                    print("Successfully downloaded report %s "%ut_report)
                    count+= 1
                    result = True
        else:
            print("No coverage.out files available to download from artifactory path requested")
            
        # this is to handle exceptional scenario , where CC report name is different
        '''
        if repo == "amf-common":
            report_filename = "wwwin-github.cisco.com.cov"
            if ((repo in file) and (file.endswith("coverage.out"))) or (file == report_filename):
                src_path = os.path.join(root, file)
                dest_path = os.path.join(cc_dir, file)
                if os.path.exists(dest_path):
                    counter += 1
                    filename = str(counter) + "_" + file
                    dest_path = os.path.join(cc_dir, filename)
                print("Copying %s"%src_path)
                shutil.copy(src_path, dest_path)
                result = True 
        '''     
    if result:
        return result      

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('-p', '--product', required=True, help="product")
    parser.add_argument('-o', '--org', required=True, help="org")
    parser.add_argument('-r', '--repo', required=True, help="repo")
    parser.add_argument('-b', '--branch', required=True, help="branch")
    parser.add_argument('-sb', '--sonar_branch', required=False, help="branch")
    parser.add_argument('-t','--test_type',required=True, help="test type")
    parser.add_argument('-rb','--reference_branch', required=False, help="reference_branch" )
    parser.add_argument('-ub','--ut_branch', help="UT branch" )
    parser.add_argument('-fb','--ft_branch', required=False, help="FT branch" )
    parser.add_argument('-stb','--st_branch', required=False, help="ST branch" )
    parser.add_argument('-bn','--build_number', help="project_Version" )
    parser.add_argument('-d', '--db_name', required=True, help='database name')
    parser.add_argument('-scm', '--use_scm', required=False, default='False', help = 'Use GIT for new code analysis')
    parser.add_argument('-date','--project_date', required=False, help="project_date" )
    parser.add_argument('-push_old', '--push_old_data', required=False, default='False', help = 'Push base branch data only')
    parser.add_argument('-delete', '--delete_branch', required=False, default='False', help = 'Delete sonar branch')
    parser.add_argument('-bv','--base_version', help="build number to be used as new code period start point" )
    parser.add_argument('-test', '--test_run', required=False, default='False', help = 'test code coverage run')
    try:
        args = parser.parse_args()
    except Exception as e:
        return e
    
    running_node = os.uname()[1]
    if running_node in Constants.HOS_HOSTS:
        CC_NFS_MOUNT = Constants.HOS_CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.HOS_TEST_CC_NFS_MOUNT
    else:
        CC_NFS_MOUNT = Constants.CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.TEST_CC_NFS_MOUNT
    

    product = args.product
    org = args.org
    repo = args.repo
    branch = args.branch
    test_type = args.test_type
    DBINST = args.db_name

    if args.ft_branch:
        ft_branch = args.ft_branch
    else:
        ft_branch = ""

    if args.reference_branch:
        reference_branch = args.reference_branch
    else:
         reference_branch = None   
    if (args.use_scm in ('true' , 'True', '1' )):
        use_scm = 1
    else:
        use_scm = 0   
    if (args.delete_branch in ('true' , 'True', '1' )):
        delete_branch = 1
    else:
        delete_branch = 0   
    if (args.test_run in ('true' , 'True', '1' )):
        CC_NFS_MOUNT = TEST_CC_NFS_MOUNT
    else:
        CC_NFS_MOUNT = CC_NFS_MOUNT
    if args.sonar_branch:
        sonar_branch = args.sonar_branch
    else:
        sonar_branch = test_type + "-" + branch.split("-")[1]     
    if args.build_number:
        build_number = args.build_number
    else:
        build_number = ""   
    if product in ['smf','sgw']:
        product_name = "ccg"
    else:
        product_name = product  
    if args.ut_branch:
        ut_branch = args.ut_branch
    else:
        ut_branch = "main"
    if args.st_branch:
        st_branch = args.st_branch
    else:
        st_branch = ""

    githubobj = GithubApi()
    workspace = os.environ["WORKSPACE"]
    git = os.environ["GIT_SPECIFIC_HOS"]
    repo_url = "https://" + Constants.USERNAME + ":" + Constants.FULL_TOKEN + "@wwwin-github.cisco.com/" + org + "/" + repo + ".git"
    clone_dir= workspace+ "/" + repo
    
    # clone repo
    # delete repo clone if it exists in workspace
    if os.path.exists(clone_dir):
        shutil.rmtree(clone_dir)

    try:
        clone_command = git  + " clone %s %s -b %s" % (repo_url.strip(), clone_dir.strip(), branch)
        print("clone command is %s" % clone_command)
        status = os.system(clone_command)
        print("Status : %s" % status)
    except Exception as e:
        print(e)
     

    if status != 0:
        print("git clone failed")
        sys.exit(1)
    else:    
        if build_number: 
            artifactory_path_to_promote_to, commit_info_json_url = get_commitinfo_json_url(build_number,product_name,DBINST)
            print("Downloading %s" %commit_info_json_url)
            if commit_info_json_url:
                download_status = download_commitinfo_json(build_number,commit_info_json_url)
                if not download_status:
                    print("commit_info.json download failed for build %s, please check" %build_number)
                    sys.exit(1)
            else:
                print("commit_info.json doesn't exist for build %s, please check" %build_number)
                sys.exit(1)
            sha,build_internal = githubobj.read_build_sha(org,repo,build_number)
            os.chdir(clone_dir)
            command = "git checkout %s" % sha
            print(command)
            status = os.system(command)
            if status != 0:
                print("%s checkout failed. plz contact releng" % sha)
                sys.exit(1)
        else:
            sha = githubobj.read_head_sha(org, repo, branch)

    # Check whether Dockerfile.sonar exists base branch , if not check out from main branch
    print("Finding the tree")
    tree = githubobj.get_tree(org,repo,sha)
    print("Finding the image_dir_path")
    # fix for RCM exclusion list
    #image_dir_path,images_dir,dockerfile_flist = get_image_dir_path(tree,clone_dir)
    #if not dockerfile_flist:
    if product == 'rcm':
        print("Dockerfile.sonar doesn't exist in base branch. <NAME_EMAIL>")
        print("Check if Dockerfile.sonar exist for main branch")
        main_branch_head_sha = githubobj.read_head_sha(org, repo, branch)
        tree = githubobj.get_tree(org,repo,main_branch_head_sha)
        image_dir_path,images_dir,dockerfile_flist = get_image_dir_path(tree,clone_dir)
        print("image_dir_path,images_dir,dockerfile_flist:%s,%s,%s"%(image_dir_path,images_dir,dockerfile_flist))
        os.chdir(images_dir)
        src_dir_path,src_dir,sonar_script_flist = get_src_dir_path(tree,clone_dir,image_dir_path,repo)
        sonar_properties_file = "%s/sonar-project.properties" % src_dir
        dockerfile_test = dockerfile_flist
        golint_file = images_dir + "golangci-lint.yml"
        for file in [ sonar_properties_file, dockerfile_test, golint_file ]:
            cmd_checkout_file = "git checkout origin/%s -- %s" % (branch, file)
            print(cmd_checkout_file)
            status = os.system(cmd_checkout_file)
            if status != 0:
                print("Fail to copy %s from base branch" % file)
        if not dockerfile_flist:
            print("Dockerfile.sonar doesn't exist in main branch. <NAME_EMAIL>")
            sys.exit(1)
    else:
        image_dir_path,images_dir,dockerfile_flist = get_image_dir_path(tree,clone_dir)        
        # set src_dir_path       
        print(image_dir_path)
        print("Finding the src_dir_path")
        src_dir_path,src_dir,sonar_script_flist = get_src_dir_path(tree,clone_dir,image_dir_path,repo)

    if not image_dir_path:
        print("image_dir_path not found, please check..")
        sys.exit(1)
    else:
        print("image_dir_path :" + image_dir_path)
    if not src_dir_path:
        print("src_dir_path not found, please check..")
        sys.exit(1)
    else:
        print("src_dir_path :" + src_dir_path)

    #set project date
    #use QA build date as project date if not passed as input
    if args.project_date:
        project_date = args.project_date  
    elif build_number:
        print("Set project_date as QA build date")
        config_section_name=args.db_name
        version_info_obj = version_info(config_section_name=config_section_name)
        dbo = version_info_obj.open_connection()  
        query = "select BUILD_ONLINE_START_TIME from builds where products_product_name = '%s' and build_number = '%s' "% (product_name,build_number)
        print(query)
        value = dbo.run_query(query)
        if value:
            timestamp = value[0]['BUILD_ONLINE_START_TIME']
            if timestamp:
                # convert the timestamp into date format
                project_date = datetime.datetime.fromtimestamp(int(timestamp)).strftime("%Y-%m-%d")
                print("project_date:" + project_date)
    else:
        print("Set project_date as today's date")
        now = datetime.datetime.now()
        project_date = now.strftime("%Y-%m-%dT%H:%M:%S-0700")
        print("PROJECT DATE: %s" % project_date)

    # update sonar-project.properties   
    os.chdir(images_dir)
    print("Updating sonar-project.properties")
    sonar_properties = update_sonar_properties(src_dir,images_dir,sonar_branch,build_number,use_scm,project_date)
    if project_date:
        data=[]
        data.append("")
        data.append("sonar.projectDate=%s" % project_date)
        append_to_file(data,sonar_properties)
    if org == 'mobile-cnat-chf' and repo =='rest-ep':
        data = []
        data.append("")
        data.append("sonar.projectKey=chf-rest-ep")
        data.append("sonar.projectName=chf-rest-ep")
        append_to_file(data,sonar_properties)
    if not sonar_properties:
        print("Failed to update sonar-project.properties, please check..")
        sys.exit(1)
    data.append("sonar.login=%s" % os.environ["SONAR_TOKEN"])
    data.append("sonar.qualitygate.ignoreSmallChanges=False")
    data.append("")
    append_to_file(data,sonar_properties)
    # This has been added as exception because for some repos source path is different. 
    print("src_dir:"+src_dir)
    if not os.path.exists(src_dir):
        src_dir = images_dir + "/src"
    src_dir = os.path.normpath(src_dir)
    os.chdir(src_dir)
 

    #get sonar project name
    sonar_project = get_sonar_project(org,repo)
    print("organization: %s repo: %s sonar_project: %s" %(org,repo,sonar_project))
    
    # check whether branch exist in sonar
    branch_result = check_sonar_branch_existance(sonar_project, sonar_branch)
    if branch_result and delete_branch:
        sonar_branch_delete_status = delete_sonar_branch(sonar_project, sonar_branch)
        if not sonar_branch_delete_status:
            print("Warning: Unable to delete branch in sonar")
            sys.exit(1)
        else:
            print("Successfully Deleted %s branch in sonar"%sonar_branch)  
            branch_result = ""

    # Copy code coverage reports
    if not (args.push_old_data in ('true' , 'True', '1' )):
        os.chdir(src_dir)
        cc_result = copy_cc_reports(org,repo,test_type,src_dir,branch,product,CC_NFS_MOUNT,build_number,ut_branch,ft_branch,st_branch)
        if not cc_result:
            print("code coverage report download failed..please check")
            #sys.exit(1)
        if org != 'mobile-cnat-rcm':
            cc_dir = src_dir + "/" + test_type
            command = "cp %s %s" % (scrub_script, cc_dir)
            status = os.system(command)
            os.chdir(cc_dir)
            cmd = "chmod +x scrub_cc.go"
            print(cmd)
            os.system(cmd)
            if status != 0:
                print("copy of scrub_cc script to cc dir failed")
            else:
                print("Copy command is successful : %s " % command)
            red_covfile_size(test_type,cc_dir,repo)
            stream = os.popen('go version')
            go_version = stream.read().rstrip("\n").replace("go version ","")
            print("Go version: " + go_version)
            go_command='go run scrub_cc.go -ci=CC_'+repo+'.cov -co='+repo+'-CC.cov -debug=true -loc='+src_dir+' -mod='+repo+' && find . -type f -name "*.cov" ! -name "'+repo+'-CC.cov" -delete'
            print(go_command)
            run(go_command)
            mv_command = "mv "+repo +"-CC.cov " + repo +".cov"
            print(mv_command)
            os.system(mv_command)
            rmv_command = "rm scrub_cc.go CC_"+repo +".cov"
            print(rmv_command)
            os.system(rmv_command)

    #Execute sonar analysis
    os.chdir(src_dir) 
    try:
        command = "%s -Dproject.settings=%s"%(sonar_scanner,sonar_properties)
        print(command)
    except Exception as e:
        print (e)    
    status = os.system(command)
    if status != 0:
        print("Sonar execution failed,please check..")
        sys.exit(1)

          
    '''
    # Future enhancement for new code settings : there are issues with this in the current sonarqube 
    # New code period settings
    # If base version/build is specifid , use specific_analysis method for new code calculation 
    # find out sonar analysis id for a build/version
    # update new code setting for a branch
    #If reference_branch needs to be used for new code , update settings accordingly

    if args.base_version:
        status = set_new_code_period(sonar_project,args.base_version,sonar_branch)
        if status:
            print("successfully updated new code period settings for branch")
        else:
            print("Failed to set new code period")
            sys.exit(2) 
    #update branch reference
    elif reference_branch:
        retry = 0
        status = None
        while status is None:
            status = update_branch_reference(sonar_project,sonar_branch,reference_branch)
            if status:  
                successful = True
            else:
                time.sleep(120)
                retry += 1
                if retry > 5:    
                    print("Failed to update reference branch,please check..")
                    sys.exit(1)
    ''' 

if __name__ == "__main__":
    sys.exit(main())
