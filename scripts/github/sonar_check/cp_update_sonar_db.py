
import sys
import os

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")
import Constants as Constants
from db import CPSOracle
from common import write_to_file, run, sendMail, read_file

DB_INI=BASE_DIR+"/libs/db.ini"

def upload_feature_pr_html(pr_fname):
    artifactory_featue_pr_list_path = "mobile-cnat-charts-release/merged_feature/%s" % pr_fname
    command = 'jfrog rt upload {} "{}" --flat=false --recursive=false --url={} --user={} --password={}'.format(pr_fname, artifactory_featue_pr_list_path, Constants.ARTIFACTORY_URL, Constants.ARTIFACTORY_USER, Constants.ARTIFACTORY_PASSWORD)
    try:
        print(command)
        run(command)
        print("Successfully uploaded json report : %s" % pr_fname)
        return True
    except Exception as e:
        print("Upload failed for %s" % pr_fname)
        print("ERROR %s" % e)
        return None



def main ():
    pr_fname="feature_FEAT-23538_pr_list.html"
    upload_feature_pr_html(pr_fname)
    os.remove(pr_fname)
    download_from_artifactory(pr_fname,workspace=os.environ["WORKSPACE"])
    os.chmod(os.environ["WORKSPACE"])
    run("ls")
    read_file(pr_fname)

if __name__ == "__main__":
    sys.exit(main())
