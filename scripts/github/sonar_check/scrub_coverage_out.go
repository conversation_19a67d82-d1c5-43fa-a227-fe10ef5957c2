package main

import (
	"bufio"
	"flag"
	"fmt"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

var DebugFlag bool

func Debug(format string, args ...interface{}) {
	if DebugFlag {
		fmt.Printf(format, args...)
	}
}

// struct defining the input from command line
type Args struct {
	clonePath     string
	inputCovPath  string
	outputCovPath string
	module        string
	debug         bool
}

// struct defining a comment uniquely
type Comment struct {
	StartLine int
	EndLine   int
}

func (comment Comment) Debug() {
	Debug("Start Line: %d	End Line: %d\n", comment.StartLine, comment.EndLine)
}

// commentInfo[pos] -> list of comments for the file at index pos.
// pos is nothing repo specific, storing as list to reduce execution time
// retrieving comments for a filename from map takes more time than list
var commentInfo [][]Comment

// fileContents[pos] -> list of lines in the file at index pos.
var fileContents [][]string

// stores mapping of filename and its position.
// pos = index[filename], commentInfo[pos] stores all the comments in this file
var index map[string]int

// struct defining the contents of a coverage line
type CoverageInfo struct {
	fileName           string
	startLine          int
	startCol           int
	endLine            int
	endCol             int
	numberOfStatements int
	isCovered          bool
}

func (info CoverageInfo) Debug() {
	Debug("\n")
	Debug("FileName: %s\n", info.fileName)
	Debug("StartLine: %d\n", info.startLine)
	Debug("StartCol: %d\n", info.startCol)
	Debug("EndLine: %d\n", info.endLine)
	Debug("EndCol: %d\n", info.endCol)
	Debug("Number of Statements: %d\n", info.numberOfStatements)
	Debug("isCovered: %t\n", info.isCovered)
}

// reads the flags from the command line
func ReadFlags() Args {
	var args Args
	flag.StringVar(&args.clonePath, "loc", "", "Path to the repo clone")
	flag.StringVar(&args.inputCovPath, "ci", "", "Input File")
	flag.StringVar(&args.outputCovPath, "co", "", "Output File")
	flag.StringVar(&args.module, "mod", "", "Module")
	flag.BoolVar(&args.debug, "debug", false, "Debug Flag")
	flag.Parse()

	fmt.Printf("Input parameters:\n")
	fmt.Printf("loc (Path to the repo clone): %s\n", args.clonePath)
	fmt.Printf("ci (Input File): %s\n", args.inputCovPath)
	fmt.Printf("co (Output File): %s\n", args.outputCovPath)
	fmt.Printf("mod (Module): %s\n", args.module)
	fmt.Printf("Debug Flag: %t\n", args.debug)

	return args
}

func GetModule(path string) string {
	filePath := filepath.Join(path, "base.go.mod")
	file, err := os.Open(filePath)

	if err != nil {
		Debug("base.go.mod not found in the clone path: %s\n", path)
		os.Exit(1)
	}

	defer file.Close()
	Debug("\nGetting module from base.go.mod at path: %s\n", filePath)

	lines := []string{}
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	err = scanner.Err()

	if err != nil {
		Debug("Error Reading base.go.mod file!\n")
		os.Exit(1)
	}

	content := strings.Split(lines[0], " ")
	module := strings.TrimSpace(content[1])
	Debug("Module: %s\n", module)

	return module
}

// checks the arguments passed are valid
func CheckInputValidations(args *Args) {
	if args.clonePath == "" || args.inputCovPath == "" {
		fmt.Printf("Clone Path, Input Coverage File and module are required!\n")
		os.Exit(1)
	}

	fileInfo, err := os.Stat(args.clonePath)

	if err != nil {
		fmt.Printf("Invalid Clone Path!\n")
		os.Exit(1)
	}

	if !fileInfo.IsDir() {
		fmt.Printf("Clone Path should be a directory and not a file!\n")
		os.Exit(1)
	}

	fileInfo, err = os.Stat(args.inputCovPath)

	if err != nil {
		fmt.Printf("Invalid Input Coverage Path!\n")
		os.Exit(1)
	}

	if fileInfo.IsDir() {
		fmt.Printf("Input Coverage Path should be a file and not a Directory!\n")
		os.Exit(1)
	}

	if args.outputCovPath == "" {
		args.outputCovPath = "coverage.out"
	}

	if args.module == "" {
		args.module = GetModule(args.clonePath)
	}

	if args.debug {
		DebugFlag = true
	} else {
		DebugFlag = false
	}
}

// extracts the coverage info from a coverage line
func ExtractCoverageInfo(line string) CoverageInfo {
	info := CoverageInfo{}

	temp := strings.Split(line, ":")
	info.fileName = temp[0]

	numbers := strings.Split(temp[1], " ")

	if numbers[len(numbers)-1] == "0" {
		info.isCovered = false
	} else {
		info.isCovered = true
	}

	statements, err := strconv.ParseInt(numbers[1], 10, 32)

	if err != nil {
		Debug("Error converting number of statements to integer: %s\n", err.Error())
		os.Exit(1)
	}

	info.numberOfStatements = int(statements)

	limits := strings.Split(numbers[0], ",")
	startInfo := strings.Split(limits[0], ".")
	endInfo := strings.Split(limits[1], ".")

	lineStart, err := strconv.ParseInt(startInfo[0], 10, 32)

	if err != nil {
		Debug("Error converting start line to integer: %s\n", err.Error())
		os.Exit(1)
	}

	colStart, err := strconv.ParseInt(startInfo[1], 10, 32)

	if err != nil {
		Debug("Error converting start col to integer: %s\n", err.Error())
		os.Exit(1)
	}

	lineEnd, err := strconv.ParseInt(endInfo[0], 10, 32)

	if err != nil {
		Debug("Error converting end line to integer: %s\n", err.Error())
		os.Exit(1)
	}

	colEnd, err := strconv.ParseInt(endInfo[1], 10, 32)

	if err != nil {
		Debug("Error converting end col to integer: %s\n", err.Error())
		os.Exit(1)
	}

	info.startLine = int(lineStart)
	info.startCol = int(colStart)
	info.endLine = int(lineEnd)
	info.endCol = int(colEnd)

	return info
}

// Extracts all the comments in the file at filepath
func GetCommentsListForTheFile(filePath string, pos int) []Comment {
	fs := token.NewFileSet()
	node, err := parser.ParseFile(fs, filePath, nil, parser.ParseComments)

	if err != nil {
		Debug("Error Parsing the file: %s\nError: %s\n", filePath, err.Error())
		os.Exit(1)
	}

	commentList := []Comment{}

	for _, comments := range node.Comments {
		for _, com := range comments.List {
			start := fs.Position(com.Pos()).Line
			end := fs.Position(com.End()).Line
			text := com.Text

			if start == end && strings.TrimSpace(fileContents[pos][start-1]) != text {
				continue
			}

			commentList = append(commentList, Comment{
				StartLine: start,
				EndLine:   end,
			})
		}
	}

	Debug("\nThere are %d comments in the file: %s\n", len(commentList), filePath)
	for i, comment := range commentList {
		Debug("Index: %d	", i)
		comment.Debug()
	}

	return commentList
}

// returns all the file content line by line as a list of string.
// each string in the list is a line of the file
func GetFileContents(filePath string) []string {
	file, _ := os.Open(filePath)
	defer file.Close()

	lines := []string{}
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	err := scanner.Err()

	if err != nil {
		Debug("Error Reading Input coverage file!\n")
		os.Exit(1)
	}

	return lines
}

// commentInfo[pos] -> list of comments present in the file at index pos.
// start and end are the starting and ending index in this list of comments
// which lies in our current coverage range of statements
func GetCommentsToIgnore(pos int, covInfo CoverageInfo) (start, end int) {
	Debug("\nGetting comments to ignore\n")

	len := len(commentInfo[pos])
	low := 0
	high := len - 1
	start = -1

	for low <= high {
		mid := (low + high) / 2
		if covInfo.startLine <= commentInfo[pos][mid].StartLine {
			start = mid
			high = mid - 1
		} else {
			low = mid + 1
		}
	}

	low = 0
	high = len - 1
	end = -1

	for low <= high {
		mid := (low + high) / 2
		if commentInfo[pos][mid].EndLine <= covInfo.endLine {
			end = mid
			low = mid + 1
		} else {
			high = mid - 1
		}
	}

	if start > end {
		start = -1
		end = -1
	}

	Debug("Got the positions of comments to be ignored!\n")
	Debug("Start Pos: %d	End Pos: %d\n", start, end)
	return start, end
}

// returns the content to be written in the coverage file,
// based on the coverage information passed
func GetLineToAdd(covInfo CoverageInfo) string {
	hit := 0
	if covInfo.isCovered {
		hit = 1
	}

	line := fmt.Sprintf(
		"%s:%d.%d,%d.%d %d %d",
		covInfo.fileName,
		covInfo.startLine,
		covInfo.startCol,
		covInfo.endLine,
		covInfo.endCol,
		covInfo.numberOfStatements,
		hit,
	)

	return line
}

// returns the list of lines to be written in the output coverage file
// for the current coverage information
func GetCoverageLines(covInfo CoverageInfo, pos, startInd, endInd int) []string {
	Debug("\nGetting the coverage lines to be written in new coverage.out\n")

	lines := []string{}
	curCovInfo := covInfo

	if startInd == -1 || endInd == -1 {
		Debug("No Comments in the current Coverage!\n")
		lineToAdd := GetLineToAdd(curCovInfo)

		Debug("Coverage line to write:\n%s\n", lineToAdd)
		lines = append(lines, lineToAdd)
		return lines
	}

	for i := startInd; i <= endInd; i++ {
		left := commentInfo[pos][i].StartLine
		right := commentInfo[pos][i].EndLine

		if left == curCovInfo.startLine {
			curCovInfo = covInfo
			curCovInfo.startLine = right + 1
			curCovInfo.startCol = 1
			continue
		}

		if left <= curCovInfo.endLine {
			endLine := left - 1
			endCol := len(fileContents[pos][endLine])

			curCovInfo.endLine = endLine
			curCovInfo.endCol = endCol
			curCovInfo.numberOfStatements = curCovInfo.endLine - curCovInfo.startLine + 1

			lineToAdd := GetLineToAdd(curCovInfo)
			Debug("Coverage line to write:\n%s\n", lineToAdd)
			lines = append(lines, lineToAdd)

			curCovInfo = covInfo
			curCovInfo.startLine = right + 1
			curCovInfo.startCol = 1
		}
	}

	curCovInfo.numberOfStatements = curCovInfo.endLine - curCovInfo.startLine + 1
	lineToAdd := GetLineToAdd(curCovInfo)
	Debug("Coverage line to write:\n%s\n", lineToAdd)
	lines = append(lines, lineToAdd)

	return lines
}

// Function to generate the code coverage ignoring the comments
func GenerateCoverageFile(args *Args) {
	outputCovFile, err := os.Create(args.outputCovPath)
	defer outputCovFile.Close()

	if err != nil {
		Debug("Unable to create/open output coverage file!\n")
		os.Exit(1)
	}

	inputCovFile, _ := os.Open(args.inputCovPath)
	defer inputCovFile.Close()

	covLines := []string{}
	scanner := bufio.NewScanner(inputCovFile)

	for scanner.Scan() {
		covLines = append(covLines, scanner.Text())
	}

	err = scanner.Err()

	if err != nil {
		Debug("Error Reading Input coverage file!\n")
		os.Exit(1)
	}

	fmt.Fprintln(outputCovFile, covLines[0])

	for covLineNum, lineContent := range covLines {
		if covLineNum == 0 || len(lineContent) == 0 {
			continue
		}

		Debug("\nActual Coverage Content: %s\n", lineContent)

		covInfo := ExtractCoverageInfo(lineContent)
		covInfo.Debug()

		pos, ok := index[covInfo.fileName]

		if !ok {
			index[covInfo.fileName] = len(commentInfo)
			pos = len(commentInfo)

			// covInfo.fileName => "<module>/procedures/pdurelease/Types.go"
			// clonePath => "./../smf-service/images/smf_service/src/smf-service/"
			// we remove the module part from the filename, and concatenate it
			// at the end of clonePath to get the pathToFile

			// moduleLen is the length of module
			// moduleLen + 1 => slash "/"
			// curFilePath will be everything after that slash

			moduleLen := len(args.module)
			curFilePath := covInfo.fileName[moduleLen+1:]
			pathToFile := filepath.Join(args.clonePath, curFilePath)

			Debug("File Path (from cov): %s\n", curFilePath)
			Debug("Path to file (from clone): %s\n", pathToFile)

			fileContentsToAdd := GetFileContents(pathToFile)
			fileContents = append(fileContents, fileContentsToAdd)

			commentInfoToAdd := GetCommentsListForTheFile(pathToFile, pos)
			commentInfo = append(commentInfo, commentInfoToAdd)
		}

		startInd, endInd := GetCommentsToIgnore(pos, covInfo)
		linesToWrite := GetCoverageLines(covInfo, pos, startInd, endInd)

		for _, line := range linesToWrite {
			fmt.Fprintln(outputCovFile, line)
		}
	}
}

func main() {
	startTime := time.Now()
	fmt.Printf("Inside the script: scrub_coverage_out.go\n\n")

	index = make(map[string]int, 0)
	fileContents = make([][]string, 0)
	commentInfo = make([][]Comment, 0)

	args := ReadFlags()
	CheckInputValidations(&args)
	GenerateCoverageFile(&args)

	duration := time.Since(startTime)
	fmt.Printf("\nTime taken: %v\n", duration)
}
