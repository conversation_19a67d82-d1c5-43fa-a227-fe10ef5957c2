
'''
Copyright (c) 2025, Cisco Systems Inc.
@author: <PERSON><PERSON><PERSON>(skunji<PERSON>)
'''


import argparse
import os
import sys


THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

from common import read_file, write_to_file, sendMail
from datetime import datetime, timedelta
import cdets_libs
from Utils import *
from cnjenkins import CNJenkins
import Constants as Constants
from version_info import version_info
from db import CPSOracle
DB_INI=BASE_DIR+"/libs/db.ini"

def main():

    parser = argparse.ArgumentParser(description='Trigger feature report')
    parser.add_argument('-sd', '--start_date', required=False, help="enter the start date in YYYY-MM-DD format")
    parser.add_argument('-r', '--release_version', required=True, default ="2023.04.0", help="enter the release version")
    parser.add_argument('-ed', '--end_date', required=False, help="enter the end date in YYYY-MM-DD format")
    parser.add_argument('-db', '--db_name', required=False,default="5g-prod", help="pick the db instance, dev or prod")
    parser.add_argument('-p', '--product', required=True, help="product")
    parser.add_argument('-m', '--mail_to', required=True, help="mail to field")
    parser.add_argument('-w', '--weekly_run', required=False, default=False, action='store_true', help="weekly feature code coverage execution")
    
    try:
        args = parser.parse_args()
    except Exception as e:
        return e
    db_env = args.db_name
    mail_to = args.mail_to
    product = args.product  

    release_version = args.release_version

    weekly_run = args.weekly_run
       
    if args.start_date:
        start_date = args.start_date
    else:
        if weekly_run:
            start_date = (datetime.today() - timedelta(days=7)).strftime('%Y-%m-%d')
            print("Start date for weekly run is %s" % (start_date))
        else:    
            start_date = Constants.release_start_date[release_version]
            print("Start date for %s is %s" % (release_version, start_date))
    if args.end_date:
        end_date = args.end_date
    else:
        end_date = datetime.today().strftime('%Y-%m-%d')
        
    if product == "rcm":
        DEV_BRANCH = "v21.28.ux"
    else:
        DEV_BRANCH = "main"
    # establish connection with DB
    try:
        dbo=CPSOracle(DB_INI, db_env)
    except Exception as e:
        sys.exit("ERROR: Failed to open db connection: %s\n\n" % e)
    jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])    

    query="select distinct FEATURE_IDS from cn_repo_stats where ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') > TO_DATE('" + start_date + "', 'YYYY-MM-DD') ) and ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') < TO_DATE('" + end_date + "', 'YYYY-MM-DD') ) and FEATURE_IDS not in 'NA' and BRANCH='main' ORDER BY FEATURE_IDS";
    feature_list = dbo.run_query(query) 
    print(feature_list) 
    #query="select distinct EPIC_IDS from cn_repo_stats where ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') > TO_DATE('" + start_date + "', 'YYYY-MM-DD') ) and ( TO_DATE(commit_date_on_branch, 'YYYY-MM-DD') < TO_DATE('" + end_date + "', 'YYYY-MM-DD') ) and FEATURE_IDS not in 'NA' and BRANCH='main' ORDER BY FEATURE_IDS";
    #epic_list = dbo.run_query(query)   
    #print(epic_list) 
    #create one list from  feature_list and epic_list
    job_name_number_list = []
    if feature_list:
        for feature in feature_list:
            jira_id = feature['FEATURE_IDS']
            # find the current version of main branch
            table = "BRANCHES"
            product_org_list = Utils.get_product_orgs(product, "0")
            conditions = { "PRODUCTS_ORGANIZATION" : product_org_list[0],
                        "BRANCH_NAME" : DEV_BRANCH}
            print(conditions)

            version_info_obj = version_info(config_section_name=db_env)
            dbo = version_info_obj.open_connection()

            return_value = dbo.run_select_query(table, conditions)
            print(return_value)
            main_version = return_value[0]["BRANCH_VERSION"]
            # Trigger feature level coverage report
            job_name = "Code_Coverage/feature_code_coverage"

            parameter = {"PRODUCT" : product,
                        "FEATURE_ID" : jira_id,
                        "MAIN_VERSION" : main_version,
                        "MAIL_TO" : "<EMAIL>",
                        "COMMIT_AUTHOR_CEC_ID" : "cn_releng",
                        "START_DATE" : start_date,
                        "END_DATE" : end_date}
            build_url = jenkins_obj.trigger_build_params(job_name,parameter)
            if build_url != "Aborted":
                build_number = build_url.split('/')[-2]
                job_name_number_list.append(job_name+":"+build_number)

        if job_name_number_list:
            print("Jobs triggered")
            print(job_name_number_list)
    
if __name__ == "__main__":
    sys.exit(main())
