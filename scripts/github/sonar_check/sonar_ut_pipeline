pipeline {
    agent { label 'build' }
    options {
        timeout(time: 2, unit: 'HOURS')
    }
    stages {
	    stage('Clean workspace') {
		    steps {
			    sh 'rm -rf ${WORKSPACE}/*.html'
				sh 'rm -rf ${WORKSPACE}/*.tar.gz'
				sh 'rm -rf ${WORKSPACE}/*.properties'
				buildDescription '$ORGANIZATION'
				addShortText background: 'yellow', border: 1, text: "$BRANCH_NAME"
			}
		}
	    stage('Sonar scanner with reference branch') {
            steps {
	            script {
	                def tasks = [:]
	                def repoList = params.REPO_LIST.split(',')
	                for (int i = 0; i < repoList.size(); ++i)
	                {
	                    def index = i
	                    tasks [i] = {
					        node ('build')
					        {
					            try {
					                job_info = build job: 'Developer_Jobs/code_coverage_branch_admin', propagate: true,
					                parameters: [string(name: 'REPO', value: "${repoList[index]}"),
					                             string(name: 'ORGANIZ<PERSON><PERSON>', value: "$ORGANIZATION"),
                                                 string(name: 'BR<PERSON>CH_NAME', value: "$BRANCH_NAME"),
                                                 string(name: 'PROJECT_DATE', value: "$PROJECT_DATE"),
                                                 string(name: 'REFERENCE_BRANCH', value: "$REFERENCE_BRANCH"),
					                             string(name: 'RELENG_BRANCH_NAME', value: "$RELENG_BRANCH_NAME")					                             ]
					            }
					            catch (err) {
					                println(err)
                                    if ("${err}".startsWith('org.jenkinsci.plugins.workflow.steps.FlowInterruptedException')) {
                                        currentBuild.result = 'ABORTED'
                                    }
                                    else {
                                        currentBuild.result = 'FAILURE'
                                    }
					            }
					        }
					    }
	                }
	                // Create a dictionary of jobs & then run that in parallel.
                    parallel tasks
                }
		    }
        }
   }
}
