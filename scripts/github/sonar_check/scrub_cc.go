//go:build skipfile
// +build skipfile

package main

import (
	"bufio"
	"flag"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

var DebugFlag bool

func Debug(format string, args ...interface{}) {
	if DebugFlag {
		fmt.Printf(format, args...)
	}
}

// struct defining the input from command line
type Args struct {
	clonePath     string
	inputCovPath  string
	outputCovPath string
	module        string
	debug         bool
}

// struct defining lines to ignore
type IgnoreInfo struct {
	StartLine int
	EndLine   int
}

func (ignore IgnoreInfo) Debug() {
	Debug("Start Line: %d	End Line: %d\n", ignore.StartLine, ignore.EndLine)
}

// ignoreInfo[pos] -> list of ignore lines for the file at index pos.
// pos is nothing repo specific, storing as list to reduce execution time
// retrieving ignore info for a filename from map takes more time than list
var ignoreInfo [][]IgnoreInfo

// fileContents[pos] -> list of lines in the file at index pos.
var fileContents [][]string

// stores mapping of filename and its position.
// pos = index[filename], ignoreInfo[pos] stores all the ignore lines in this file
var index map[string]int

// struct defining the contents of a coverage line
type CoverageInfo struct {
	fileName           string
	startLine          int
	startCol           int
	endLine            int
	endCol             int
	numberOfStatements int
	isCovered          bool
}

func (info CoverageInfo) Debug() {
	Debug("\n")
	Debug("FileName: %s\n", info.fileName)
	Debug("StartLine: %d\n", info.startLine)
	Debug("StartCol: %d\n", info.startCol)
	Debug("EndLine: %d\n", info.endLine)
	Debug("EndCol: %d\n", info.endCol)
	Debug("Number of Statements: %d\n", info.numberOfStatements)
	Debug("isCovered: %t\n", info.isCovered)
}

// reads the flags from the command line
func ReadFlags() Args {
	var args Args
	flag.StringVar(&args.clonePath, "loc", "", "Path to the repo clone")
	flag.StringVar(&args.inputCovPath, "ci", "", "Input File")
	flag.StringVar(&args.outputCovPath, "co", "", "Output File")
	flag.StringVar(&args.module, "mod", "", "Module")
	flag.BoolVar(&args.debug, "debug", false, "Debug Flag")
	flag.Parse()

	fmt.Printf("Input parameters:\n")
	fmt.Printf("loc (Path to the repo clone): %s\n", args.clonePath)
	fmt.Printf("ci (Input File): %s\n", args.inputCovPath)
	fmt.Printf("co (Output File): %s\n", args.outputCovPath)
	fmt.Printf("mod (Module): %s\n", args.module)
	fmt.Printf("Debug Flag: %t\n\n", args.debug)

	return args
}

func GetModule(path string) string {
	filePath := filepath.Join(path, "base.go.mod")
	file, err := os.Open(filePath)  // #nosec

	if err != nil {
		Debug("base.go.mod not found in the clone path: %s\n", path)
		os.Exit(1)
	}

	defer file.Close()
	Debug("\nGetting module from base.go.mod at path: %s\n", filePath)

	lines := []string{}
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	err = scanner.Err()

	if err != nil {
		Debug("Error Reading base.go.mod file!\n")
		os.Exit(1)
	}

	content := strings.Split(lines[0], " ")
	module := strings.TrimSpace(content[1])
	Debug("Module: %s\n", module)

	return module
}

// checks the arguments passed are valid
func CheckInputValidations(args *Args) {
	if args.clonePath == "" || args.inputCovPath == "" {
		fmt.Printf("Clone Path, Input Coverage File and module are required!\n")
		os.Exit(1)
	}

	fileInfo, err := os.Stat(args.clonePath)

	if err != nil {
		fmt.Printf("Invalid Clone Path!\n")
		os.Exit(1)
	}

	if !fileInfo.IsDir() {
		fmt.Printf("Clone Path should be a directory and not a file!\n")
		os.Exit(1)
	}

	fileInfo, err = os.Stat(args.inputCovPath)

	if err != nil {
		fmt.Printf("Invalid Input Coverage Path!\n")
		os.Exit(1)
	}

	if fileInfo.IsDir() {
		fmt.Printf("Input Coverage Path should be a file and not a Directory!\n")
		os.Exit(1)
	}

	if args.outputCovPath == "" {
		args.outputCovPath = "coverage.out"
	}

	if args.module == "" {
		args.module = GetModule(args.clonePath)
	}

	if args.debug {
		DebugFlag = true
	} else {
		DebugFlag = false
	}
}

// extracts the coverage info from a coverage line
func ExtractCoverageInfo(line string) CoverageInfo {
	info := CoverageInfo{}

	temp := strings.Split(line, ":")
	info.fileName = temp[0]

	numbers := strings.Split(temp[1], " ")

	if numbers[len(numbers)-1] == "0" {
		info.isCovered = false
	} else {
		info.isCovered = true
	}

	statements, err := strconv.ParseInt(numbers[1], 10, 32)

	if err != nil {
		Debug("Error converting number of statements to integer: %s\n", err.Error())
		os.Exit(1)
	}

	info.numberOfStatements = int(statements)

	limits := strings.Split(numbers[0], ",")
	startInfo := strings.Split(limits[0], ".")
	endInfo := strings.Split(limits[1], ".")

	lineStart, err := strconv.ParseInt(startInfo[0], 10, 32)

	if err != nil {
		Debug("Error converting start line to integer: %s\n", err.Error())
		os.Exit(1)
	}

	colStart, err := strconv.ParseInt(startInfo[1], 10, 32)

	if err != nil {
		Debug("Error converting start col to integer: %s\n", err.Error())
		os.Exit(1)
	}

	lineEnd, err := strconv.ParseInt(endInfo[0], 10, 32)

	if err != nil {
		Debug("Error converting end line to integer: %s\n", err.Error())
		os.Exit(1)
	}

	colEnd, err := strconv.ParseInt(endInfo[1], 10, 32)

	if err != nil {
		Debug("Error converting end col to integer: %s\n", err.Error())
		os.Exit(1)
	}

	info.startLine = int(lineStart)
	info.startCol = int(colStart)
	info.endLine = int(lineEnd)
	info.endCol = int(colEnd)

	return info
}

type Visitor struct {
	fset *token.FileSet
	pos  int
}

func (v *Visitor) Visit(n ast.Node) ast.Visitor {
	if n == nil {
		return nil
	}

	switch d := n.(type) {
	case *ast.BlockStmt:
		linePos := v.fset.Position(d.End()).Line
		if strings.TrimSpace(fileContents[v.pos][linePos-1]) == "}" {
			ignoreInfo[v.pos] = append(ignoreInfo[v.pos], IgnoreInfo{
				StartLine: linePos,
				EndLine:   linePos,
			})
		}
	case *ast.FuncDecl:
		start := v.fset.Position(d.Pos()).Line
		end := v.fset.Position(d.Body.Pos()).Line
		ignoreInfo[v.pos] = append(ignoreInfo[v.pos], IgnoreInfo{
			StartLine: start,
			EndLine:   end,
		})
	// case *ast.SwitchStmt:
	// 	start := v.fset.Position(d.Pos()).Line
	// 	end := v.fset.Position(d.Body.Pos()).Line
	// 	ignoreInfo[v.pos] = append(ignoreInfo[v.pos], IgnoreInfo{
	// 		StartLine: start,
	// 		EndLine:   end,
	// 	})
	case *ast.CaseClause:
		start := v.fset.Position(d.Pos()).Line
		end := v.fset.Position(d.Colon).Line
		ignoreInfo[v.pos] = append(ignoreInfo[v.pos], IgnoreInfo{
			StartLine: start,
			EndLine:   end,
		})
	case *ast.IfStmt:
		if d.Else == nil {
			break
		}

		elseLine := v.fset.Position(d.Else.Pos()).Line
		elseLineContent := strings.ReplaceAll(fileContents[v.pos][elseLine-1], " ", "")
		match := strings.TrimSpace(elseLineContent)

		if match == "}else{" {
			ignoreInfo[v.pos] = append(ignoreInfo[v.pos], IgnoreInfo{
				StartLine: elseLine,
				EndLine:   elseLine,
			})
		}
	}

	return v
}

// Extracts all the skips in the file at filepath
func GetSkipListForFile(filePath string, pos int) {
	fs := token.NewFileSet()
	node, err := parser.ParseFile(fs, filePath, nil, parser.ParseComments)

	if err != nil {
		Debug("Error Parsing the file: %s\nError: %s\n", filePath, err.Error())
		os.Exit(1)
	}

	v := Visitor{
		fset: fs,
		pos:  pos,
	}

	ast.Walk(&v, node)

	for _, comments := range node.Comments {
		for _, com := range comments.List {
			start := fs.Position(com.Pos()).Line
			end := fs.Position(com.End()).Line
			text := com.Text

			if start == end && strings.TrimSpace(fileContents[pos][start-1]) != text {
				continue
			}

			startContent := strings.TrimSpace(fileContents[pos][start-1])
			endContent := strings.TrimSpace(fileContents[pos][end-1])

			if start != end && !strings.HasPrefix(startContent, "/*") && !strings.HasPrefix(startContent, "}") {
				start += 1
			}

			if start != end && !strings.HasSuffix(endContent, "*/") {
				end -= 1
			}

			if start > end {
				continue
			}

			ignoreInfo[pos] = append(ignoreInfo[pos], IgnoreInfo{
				StartLine: start,
				EndLine:   end,
			})
		}
	}

	sort.SliceStable(ignoreInfo[pos], func(i, j int) bool {
		return ignoreInfo[pos][i].StartLine < ignoreInfo[pos][j].StartLine
	})

	Debug("\nThere are %d skips in the file: %s\n", len(ignoreInfo[pos]), filePath)

	for i, skip := range ignoreInfo[pos] {
		Debug("Index: %d	", i)
		skip.Debug()
	}
}

// returns all the file content line by line as a list of string.
// each string in the list is a line of the file
func GetFileContents(filePath string) []string {
	file, _ := os.Open(filePath) // #nosec
	defer file.Close()

	lines := []string{}
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	err := scanner.Err()

	if err != nil {
		Debug("Error Reading Input coverage file!\n")
		os.Exit(1)
	}

	return lines
}

// ignoreInfo[pos] -> list of ignore info present in the file at index pos.
// start and end are the starting and ending index in this list of ignore info
// which lies in our current coverage range of statements
func GetLinesToIgnore(pos int, covInfo CoverageInfo) (start, end int) {
	Debug("\nGetting lines to ignore\n")

	len := len(ignoreInfo[pos])
	low := 0
	high := len - 1
	start = -1

	for low <= high {
		mid := (low + high) / 2
		if covInfo.startLine <= ignoreInfo[pos][mid].StartLine {
			start = mid
			high = mid - 1
		} else {
			low = mid + 1
		}
	}

	low = 0
	high = len - 1
	end = -1

	for low <= high {
		mid := (low + high) / 2
		if ignoreInfo[pos][mid].EndLine <= covInfo.endLine {
			end = mid
			low = mid + 1
		} else {
			high = mid - 1
		}
	}

	if end+1 < len && ignoreInfo[pos][end+1].StartLine <= covInfo.endLine && covInfo.endLine <= ignoreInfo[pos][end+1].EndLine {
		end++
	}

	if start-1 >= 0 && ignoreInfo[pos][start-1].StartLine <= covInfo.startLine && covInfo.startLine <= ignoreInfo[pos][start-1].EndLine {
		start--
	}

	if start > end {
		start = -1
		end = -1
	}

	Debug("Got the positions of lines to be ignored!\n")
	Debug("Start Pos: %d	End Pos: %d\n", start, end)
	return start, end
}

// returns the content to be written in the coverage file,
// based on the coverage information passed
func GetLineToAdd(covInfo CoverageInfo) string {
	hit := 0
	if covInfo.isCovered {
		hit = 1
	}

	line := fmt.Sprintf(
		"%s:%d.%d,%d.%d %d %d",
		covInfo.fileName,
		covInfo.startLine,
		covInfo.startCol,
		covInfo.endLine,
		covInfo.endCol,
		covInfo.numberOfStatements,
		hit,
	)

	return line
}

// returns the list of lines to be written in the output coverage file
// for the current coverage information
func GetCoverageLines(covInfo CoverageInfo, pos, startInd, endInd int) []string {
	Debug("\nGetting the coverage lines to be written in new coverage.out\n")

	lines := []string{}
	curCovInfo := covInfo

	if startInd == -1 || endInd == -1 {
		Debug("No ignore in the current Coverage!\n")
		lineToAdd := GetLineToAdd(curCovInfo)

		Debug("Coverage line to write:\n%s\n", lineToAdd)
		lines = append(lines, lineToAdd)
		return lines
	}

	for i := startInd; i <= endInd; i++ {
		skipStart := ignoreInfo[pos][i].StartLine
		skipEnd := ignoreInfo[pos][i].EndLine

		if skipStart == curCovInfo.startLine {
			curCovInfo = covInfo
			curCovInfo.startLine = skipEnd + 1
			curCovInfo.startCol = 1
			continue
		}

		if skipStart <= curCovInfo.endLine {
			curCovInfo.endLine = skipStart - 1
			curCovInfo.endCol = len(fileContents[pos][curCovInfo.endLine-1]) + 1

			if curCovInfo.startLine > curCovInfo.endLine {
				continue
			}

			curCovInfo.numberOfStatements = curCovInfo.endLine - curCovInfo.startLine + 1

			if len(fileContents[pos][curCovInfo.startLine-1]) == curCovInfo.startCol {
				curCovInfo.numberOfStatements -= 1
			}

			lineToAdd := GetLineToAdd(curCovInfo)
			Debug("Coverage line to write:\n%s\n", lineToAdd)
			lines = append(lines, lineToAdd)

			curCovInfo = covInfo
			curCovInfo.startLine = skipEnd + 1
			curCovInfo.startCol = 1
		}
	}

	if curCovInfo.startLine <= curCovInfo.endLine {
		curCovInfo.numberOfStatements = curCovInfo.endLine - curCovInfo.startLine + 1
		lineToAdd := GetLineToAdd(curCovInfo)
		Debug("Coverage line to write:\n%s\n", lineToAdd)
		lines = append(lines, lineToAdd)
	}

	Debug("\nDone writting to new coverage.out\n")
	return lines
}

// Function to generate the code coverage with ignoring lines
func GenerateCoverageFile(args *Args) {
	fmt.Printf("Module used: %s\n\n", args.module)

	outputCovFile, err := os.Create(args.outputCovPath)

	if err != nil {
		Debug("Unable to create/open output coverage file!\n")
		os.Exit(1)
	}

	defer outputCovFile.Close()

	inputCovFile, _ := os.Open(args.inputCovPath)
	defer inputCovFile.Close()

	covLines := []string{}
	scanner := bufio.NewScanner(inputCovFile)

	for scanner.Scan() {
		covLines = append(covLines, scanner.Text())
	}

	err = scanner.Err()

	if err != nil {
		Debug("Error Reading Input coverage file!\n")
		os.Exit(1)
	}

	fmt.Fprintln(outputCovFile, covLines[0])

	for covLineNum, lineContent := range covLines {
		if covLineNum == 0 || len(lineContent) == 0 {
			continue
		}

		covInfo := ExtractCoverageInfo(lineContent)

		if !strings.HasPrefix(covInfo.fileName, args.module) {
			continue
		}

		Debug("----------------------------------------------------------------------------\n\n")
		Debug("\nActual Coverage Content: %s\n", lineContent)
		covInfo.Debug()

		pos, ok := index[covInfo.fileName]

		if !ok {
			// covInfo.fileName => "<module>/procedures/pdurelease/Types.go"
			// clonePath => "./../smf-service/images/smf_service/src/smf-service/"
			// we remove the module part from the filename, and concatenate it
			// at the end of clonePath to get the pathToFile

			// moduleLen is the length of module
			// moduleLen + 1 => slash "/"
			// curFilePath will be everything after that slash

			moduleLen := len(args.module)
			curFilePath := covInfo.fileName[moduleLen+1:]
			pathToFile := filepath.Join(args.clonePath, curFilePath)

			Debug("File Path (from cov): %s\n", curFilePath)
			Debug("Path to file (from clone): %s\n", pathToFile)

			if _, err := os.Stat(pathToFile); err != nil {
				Debug("Error with path to file (from clone): %s", err.Error())
				continue
			}

			index[covInfo.fileName] = len(ignoreInfo)
			pos = len(ignoreInfo)

			fileContentsToAdd := GetFileContents(pathToFile)
			fileContents = append(fileContents, fileContentsToAdd)

			ignoreInfo = append(ignoreInfo, []IgnoreInfo{})
			GetSkipListForFile(pathToFile, pos)
		}

		startInd, endInd := GetLinesToIgnore(pos, covInfo)
		linesToWrite := GetCoverageLines(covInfo, pos, startInd, endInd)

		for _, line := range linesToWrite {
			fmt.Fprintln(outputCovFile, line)
		}
	}
}

func GetCoverageCount(path string, beforeScript bool) {
	inputCovFile, _ := os.Open(path) // #nosec
	defer inputCovFile.Close()

	covLines := []string{}
	scanner := bufio.NewScanner(inputCovFile)

	for scanner.Scan() {
		covLines = append(covLines, scanner.Text())
	}

	err := scanner.Err()

	if err != nil {
		Debug("Error Reading coverage file!\n")
		os.Exit(1)
	}

	count := 0
	green := 0
	red := 0

	for ind, line := range covLines {
		if ind == 0 {
			continue
		}

		info := ExtractCoverageInfo(line)
		count += info.numberOfStatements

		if info.isCovered {
			green += info.numberOfStatements
		} else {
			red += info.numberOfStatements
		}
	}

	if beforeScript {
		fmt.Printf("Number of covered lines before script run: %d\t", count)
	} else {
		fmt.Printf("Number of covered lines after script run: %d\t", count)
	}

	fmt.Printf("Green: %d\tRed: %d\n", green, red)
}

func main() {
	startTime := time.Now()
	fmt.Printf("Inside the scrubbing script\n\n")

	index = make(map[string]int, 0)
	fileContents = make([][]string, 0)
	ignoreInfo = make([][]IgnoreInfo, 0)

	args := ReadFlags()
	CheckInputValidations(&args)
	GenerateCoverageFile(&args)

	GetCoverageCount(args.inputCovPath, true)
	GetCoverageCount(args.outputCovPath, false)

	duration := time.Since(startTime)
	fmt.Printf("\nTime taken for scrubbing: %v\n", duration)
}