"""
 Copyright (c) 2023, Cisco Systems Inc.
 author: skunjir
 This job is to trigger UT code coverage upload for all repos for a given product and branch
# Flow of the script
# input : product  & branch name as a parameter
# Flow of Script
# For a given product -> get the list of github orgs
# 1. Check if this org & branch is supported for sonar
# 2. If yes, get the list of repos which are supported
# 3. Loop through the list of repos
# 4. Trigger upload_code_coverage_report job for all supported repos os a product
"""

import sys
import os
import argparse

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

sonar_checks_ini = BASE_DIR + "/libs/sonar_checks.ini"

from GithubApi import GithubApi
import Constants as Constants
from sonar_utils import list_supported_repos_branches
from Utils import *
from cnjenkins import CNJenkins

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('-p', '--product', required=True, help="product")
    parser.add_argument('-b', '--branch', required=True, help="branch")
    parser.add_argument('-org', '--organization', help="organization")
    parser.add_argument('-bn', '--build_number', help = 'QA build number')
    args = parser.parse_args()

    # read inputs
    product = args.product
    branch = args.branch
    test_type = "UT"
    qa_build_number = args.build_number 
    job_name = "Code_Coverage/upload_code_coverage_report"

    githubobj = GithubApi()
    jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])

    # get list of org which makes a product.
    product_org_list = []
    job_name_number_list = []
    if product in ['smf','pgw','sgw']:
        product_name = "ccg"
    else:
        product_name = product    

    if args.organization:
        product_org_list.append(args.organization)
    else:    
        product_org_list = Utils.get_product_orgs(product_name, "0")

    for organization in product_org_list:  
        print("=" * 50)
        print(organization)
        print("=" * 50)
        supported_repos_list = list_supported_repos_branches(organization)
        print("Supported repos list:%s"%supported_repos_list)
        if not supported_repos_list:
            print("%s org is not supported for sonar" % organization)
        # If branch is suported, loop through the list of repos supported.
        for repo in supported_repos_list:
            print("=" * 50)
            print("Trigger upload_code_coverage_report job")
            parameter = {"PRODUCT" : product,
                        "ORGANIZATION" : organization,
                        "REPO" : repo,
                        "TEST_TYPE" : test_type,
                        "QA_BUILD_NUMBER" : qa_build_number,
                        "BRANCH" : branch}
            queue_id = jenkins_obj.build_job(job_name,parameter)
            if not queue_id:
                print("Build got aborted : %s" % queue_id)
            else:
                print("Waiting for Build to start.... Jenkins Queue ID %s" % queue_id)
                build_url = None
                while build_url is None:
                    queue_detail = jenkins_obj.server.get_queue_item(queue_id)
                    if queue_detail:
                        exes = queue_detail.get('executable')
                        if exes:
                            build_url = exes.get('url')
                            if build_url:
                                build_number = build_url.split('/')[-2]
                                print("Build Triggered : %s" % build_url)
                                job_name_number_list.append(job_name+":"+build_number)
                                break
                            else:
                                time.sleep(10)
                    else:
                        build_url = "Aborted"   

    print("Following builds will be monitored")
    print(job_name_number_list)

    if job_name_number_list:
        unsuccessful_builds = []
        job_name_status_list = jenkins_obj.monitor_multiple_builds_list(job_name_number_list, timeout=1800)
        if job_name_status_list:
            for job_info in job_name_status_list:
                job_name = job_info.split(":")[0]
                build_number = job_info.split(":")[1]
                build_status = job_info.split(":")[2]
                if build_status not in ['SUCCESS']:
                    unsuccessful_builds.append(job_name+build_number)
        else:
            print("Issue in monitoring the jobs triggered on %s" % job_name)
            sys.exit(1)

        if unsuccessful_builds:
            print("Following builds are unsuccessful")
            print(unsuccessful_builds)
            sys.exit(1)

if __name__ == "__main__":
    sys.exit(main())