# author  : <PERSON><PERSON><PERSON>
# Flow of the script
# input : product, branch name & mail_to as a parameter
# This job will be schedule to tirgger independently on weekly/daily basis
# Flow of Script
# 1. For a given product/branch -> get the latest QA build.
# 2. For the latest/given QA build -> download commit_info.json -> find the list of repo which are not internal
# 3. Fetch the information about each repo / head sha from the Sonar table
# 4. Create a report using that information

import os
import configparser
from yattag import Doc
import argparse
import sys
import time
import shutil

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")
from db import CPSOracle
DB_INI=BASE_DIR+"/libs/db.ini"
sonar_checks_ini = BASE_DIR + "/libs/sonar_checks.ini"

from sonar_utils import  *
from GithubApi import GithubApi
from common import write_to_file, sendMail, get_commitinfo_json_url,download_commitinfo_json,get_product_orglist,run
from version_info import version_info
import Constants as Constants
from cnjenkins import CNJenkins

def get_feature_details(dbo, feature_id,app_branch_name, release_version):
    table = "FEATURE_COVERAGE"
    conditions = { "RELEASE_VERSION" : release_version,
                   "FEAT_ID" : feature_id,
                   "SONAR_APP_BRANCH" : app_branch_name}
    print("FEATURE COVERAGE")
    data = dbo.run_select_query(table, conditions)
    if data:
        return data[0]

def download_from_artifactory(feature_id):
    pr_fname = "pr_list_%s.html" % (feature_id)
    workspace = os.environ["WORKSPACE"]
    artifactory_download_dir = "%s/%s" % (Constants.MERGED_FEATURE_ARTIFACTORY,pr_fname)
    download_cmd="jfrog rt dl --include-dirs=true --threads=5 --user=%s --password=%s --url=%s %s %s/" % (os.environ["ARTIFACTORY_USER"],os.environ["ARTIFACTORY_PASSWORD"],Constants.ARTIFACTORY_URL, artifactory_download_dir, workspace)
    print(download_cmd)
    output, error, status = run(download_cmd)
    if not status:
        print(output)
        return True
    else:
        print("%s file doesn't exist in artifactory" % pr_fname)
        return None

def main ():

    parser = argparse.ArgumentParser()
    parser.add_argument('-p', '--product', required=True, help="product")
    parser.add_argument('-q', '--qa_build', required=False, help="qa build")
    parser.add_argument('-b', '--branch', required=True, default ="main", help="branch")
    parser.add_argument('-f', '--feature_id', required=False, help="branch")
    parser.add_argument('-fm', '--feature_merged', action='store_true', required=False, help="if feature is merged select this")
    parser.add_argument('-r', '--release_version', required=False, help="branch")
    parser.add_argument('-a', '--archive_report', action='store_true')
    parser.add_argument('-db', '--db_env', required=True, help="pick the db instance, dev or prod")

    args = parser.parse_args()
    product = args.product
    qa_build = args.qa_build
    branch = args.branch
    feature_id = args.feature_id
    release_version = args.release_version
    feature_merged = args.feature_merged
    db_env = args.db_env
    archive_report = args.archive_report

    githubobj = GithubApi()
    table = "sonar"
    email_heading = "Sonar Report for %s" % product
    config = configparser.ConfigParser()
    config.read(os.path.realpath(sonar_checks_ini))

    # establish connection with DB
    try:
        version_info_obj = version_info(config_section_name=db_env)
        dbo = version_info_obj.open_connection()
    except Exception as e:
        sys.exit("ERROR: Failed to open db connection: %s\n\n" % e)

    if feature_id and not release_version:
        print("release version is mandatory when feature id is given")

    if feature_id and release_version:
        app_branch_name = "UT-%s" % (feature_id)
        if feature_merged:
            app_branch_name = "UT-merge-%s" % (feature_id)
        sonar_db_update(feature_id,feature_merged,dbo,"UT",release_version,product)
        feature_sonar_data = get_feature_details(dbo, feature_id,app_branch_name, release_version)
        if feature_sonar_data:
            print("Feature details exist in DB")
            print(feature_sonar_data)

            if feature_merged:
                print("Download from artifactory")
                download_from_artifactory(feature_id)
        else:
            print("Feature details are missing in DB")
            sys.exit(1)

    # Check if QA build exist for branch given
    latest_build_number = None
    try:
        latest_build_number = version_info_obj.get_last_passed_qa_build(product,branch)
    except Exception as e:
        print("Error: Unable to get latest build number for product %s" % product)

    # Get the latest build number for main
    latest_main_build_number = version_info_obj.get_last_passed_qa_build(product,"main")
    # if branch given for getting the report doesn't have a QA build use latest main commit_info.json to get
    # repos which are internal to the product
    if not latest_build_number:
        if latest_main_build_number:
            print("using latest main build number to get org info: %s" % latest_main_build_number)
        else:
            print("main branch doesn't exist for the product")
            sys.exit(1)

    if qa_build:
        if qa_build == "latest":
            artifactory_build = latest_build_number
            # If QA build is given as latest but QA build doesn't exist
            # then mark qa_build as None & use main QA build to get list of repos which are internal
            if not latest_build_number:
                qa_build = None
                artifactory_build = latest_main_build_number
        else:
            artifactory_build = qa_build
        email_heading += ": %s : %s" % (branch,artifactory_build)
    else:
        artifactory_build = latest_main_build_number
        email_heading += ": %s" % branch

    if feature_id:
        email_heading = "Feature level Sonar Report for %s" % release_version

    # Read commit info from artifactory.
    try:
        artifactory_path_to_promote_to,commit_info_json_url = get_commitinfo_json_url(artifactory_build,product,db_env)
        if commit_info_json_url:
            download_status = download_commitinfo_json(artifactory_build,commit_info_json_url)
            if not download_status:
                print("commit_info.json download failed for build %s, please check" %artifactory_build)
                sys.exit(1)
        else:
            print("commit_info.json doesn't exist for build %s, please check" %artifactory_build)
            sys.exit(1)
    except Exception as e:
        print("Error: Failed to download commit_info.json for %s" % artifactory_build)
        sys.exit(1)

    jsonfile = "%s/%s_commit_info.json"%(os.getcwd(),artifactory_build)
    print("commit_info.json path: " + jsonfile)
    if os.path.exists(jsonfile):
        with open(jsonfile, "r") as f:
            data = json.load(f) 
        commit_info_data = json.dumps(data)    
        print(commit_info_data)    
        f.close()    
    else:
        print("Error: Failed to load commit_info.json for %s" % artifactory_build)
        sys.exit(1)

    # Get org list from commit info.json
    organization_list = get_product_orglist(jsonfile, product)
    jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])
    job_name = "Developer_Jobs/code_coverage_branch_admin"
    job_name_number_list = []
    sonar_repo_dic = {}
    branch_org_list = []

    # Get the data before creating the html
    # Trigger job if data is not present in DB
    for organization in organization_list:
        data = None
        branch_in_org = None
        supported_repo = config.get(organization, 'supported_repos').split(",")
        print("Supported repos : %s" % supported_repo)
        if supported_repo != ['']:
            print("Finding information for the supported repos")
        # Trigger code coverage job
            for repo in supported_repo:
                if repo not in commit_info_data:
                    print("repo %s not found in commit_info.json" % repo)
                    continue

                # Find the commit sha for which we need to send the report.
                _, internal = githubobj.read_build_sha(organization, repo, artifactory_build)
                if internal == "yes" and product != "ulb":
                    continue
                if qa_build:
                    commit_sha, internal = githubobj.read_build_sha(organization, repo, artifactory_build)
                    if not commit_sha:
                        print("Check why information about %s repo is missing in commit info json file" % repo)
                        sys.exit(1)
                    else:
                        branch_in_org = True
                else:
                    commit_sha = githubobj.read_head_sha(organization, repo, branch)
                    if not commit_sha:
                        print("Commit Info can not be found for %s/%s/%s. Branch does not exists" %(organization, repo, branch))
                    else:
                        branch_in_org = True
                if commit_sha:
                    #find the info about org / repo / head sha in DB
                    conditions = { "ORGANIZATION" : organization,
                                   "REPOSITORY" : repo,
                                   "BRANCH" : branch,
                                   "HEAD_SHA" : commit_sha}
                    return_value = dbo.run_select_query(table, conditions)
                    print("return_value %s" % return_value)
                    if return_value:
                        data = return_value[-1]
                    else:
                        # Trigger jenkins job for the repos for which information doesn't exisit in DB.
                        parameter = {"ORGANIZATION" : organization,
                                     "REPO" : repo,
                                     "BRANCH_NAME" : branch,
                                     "COMMIT_SHA" : commit_sha}
                        print("Trigger build for %s" % parameter)
                        queue_id = jenkins_obj.build_job(job_name,parameter)
                        if not queue_id:
                            print("Build got aborted : %s" % queue_id)
                            print("Check on the job %s" % job_name)
                            sys.exit(1)
                        else:
                            print("Waiting for Build to start.... Jenkins Queue ID %s" % queue_id)
                            build_url = None
                            while build_url is None:
                                queue_detail = jenkins_obj.server.get_queue_item(queue_id)
                                #pprint(queue_detail)
                                if queue_detail:
                                    exes = queue_detail.get('executable')
                                    if exes:
                                        build_url = exes.get('url')
                                        if build_url:
                                            build_number = build_url.split('/')[-2]
                                            print("Build Triggered : %s" % build_url)
                                            job_name_number_list.append(job_name+":"+build_number)
                                            break
                                        else:
                                            time.sleep(10)
                                else:
                                    build_url = "Aborted"
                    key = organization + "/" + repo + "/" + commit_sha
                    sonar_repo_dic[key] = data
        if branch_in_org:
            branch_org_list.append(organization)

    # Wait for jenkins build to finish before creating report
    # Jenkins job which is triggered will push information to DB which will then be used for creating report.
    print("Following builds will be monitored")
    print(job_name_number_list)

    if job_name_number_list:
        unsuccessful_builds = []
        job_name_status_list = jenkins_obj.monitor_multiple_builds_list(job_name_number_list, timeout=7200)
        if job_name_status_list:
            for job_info in job_name_status_list:
                job_name = job_info.split(":")[0]
                build_number = job_info.split(":")[1]
                build_status = job_info.split(":")[2]
                if build_status not in ['SUCCESS']:
                    unsuccessful_builds.append(job_name+build_number)
        else:
            print("Issue in monitoring the jobs triggered on %s" % job_name)
            sys.exit(1)

        if unsuccessful_builds:
            print("Following builds are unsuccessful")
            print(unsuccessful_builds)
            print("Can not proceed with report generation")
            sys.exit(1)

    # Collect the data for the missing repos from DB
    for key, data in sonar_repo_dic.items():
        if not data:
            organization = key.split("/")[0]
            repo = key.split("/")[1]
            commit_sha = key.split("/")[2]
            conditions = { "ORGANIZATION" : organization,
                           "REPOSITORY" : repo,
                           "BRANCH" : branch,
                           "HEAD_SHA" : commit_sha}
            return_value = dbo.run_select_query(table, conditions)
            print("return_value %s" % return_value)
            if return_value:
                sonar_repo_dic[key] = return_value[0]
            else:
                sonar_repo_dic[key] = None

    product_branch = product+"-"+branch
    if product_branch in Constants.SONAR_BRANCH_REFERENCE_DIC:
        sonar_branch_reference = Constants.SONAR_BRANCH_REFERENCE_DIC[product_branch]
    else:
        sonar_branch_reference = None

    # HTML EMAIL TEMPLATE
    # Create HTML
    doc, tag, text = Doc().tagtext()
    doc.asis('<!DOCTYPE html>')
    with tag('html'):
        doc.asis('<style>')
        #text('.myTable { background-color:#eee;border-collapse:collapse; }')
        #text('.myTable th { background-color:#000;color:white;width:50%; }')
        #text('.myTable td, .myTable th { padding:5px;border:1px solid #000; }')
        text('th {border:1px solid white;border-collapse:collapse;}')
        doc.asis('</style>')

        with tag('body', style="font-family:Verdana; font-size:12px"):
            with tag('p',style="font-family:Verdana; font-size:12px"):
                text("Hi All")
                doc.asis('<br> <br>')
                with tag('b'):
                    text(email_heading)
                doc.asis('<br>')
                if not feature_id:
                    if sonar_branch_reference:
                        with tag('p'):
                            text("Baseline for New Code on sonar is %s" % sonar_branch_reference)
                    else:
                        with tag('p'):
                            text("Baseline for New Code on sonar is calculated by comparing first & latest sonar scan on branch")

            with tag('table', style="font-family:Verdana; font-size:12px"):
                length = 120
                colspan = 17
                if feature_id:
                    length = 85
                    colspan = 12

                    with tag('tr'):
                        with tag('td',colspan=colspan):
                            for i in range(0,length):
                                text("=")
                    with tag('tr'):
                        feature_sonar_link = Constants.SONAR_URL + "/dashboard?branch=" + app_branch_name + "&id=mobile-cnat-feature-application"
                        with tag('th', colspan=colspan, style="text-align:center"):
                            with tag('a',href="%s/%s" % (Constants.JIRA_BROWSE_URL,feature_id)):
                                text("%s" % feature_id)
                            if feature_sonar_data["UT_NEW"]:
                                text(" : " )
                                with tag('a', href='%s' % feature_sonar_link):
                                    text(str(feature_sonar_data["UT_NEW"]) + "%")

                for organization in branch_org_list:
                    print("org name : %s" % organization)
                    organization_sonar_link="https://engci-sonar-sjc.cisco.com/sonar/dashboard?branch=main&id=" + organization + "-application"
                    print(organization_sonar_link)

                    with tag('tr'):
                        with tag('td',colspan=colspan):
                            for i in range(0,length):
                                text("=")
                    with tag('tr'):
                        with tag('th', colspan=colspan, style="text-align:center"):
                            #with tag('a', href='%s' % organization_sonar_link):
                            text("%s" % organization)
                    with tag('tr'):
                        with tag('td',colspan=colspan):
                            for i in range(0,length):
                                text("=")
                    with tag('tr'):
                        with tag('th',style="background-color:#6082B6;color:white"):
                            text("Sonar Project")
                            with tag('th', colspan=5,style="background-color:#6082B6;color:white"):
                                text("Unit Tests")
                        with tag('th', colspan=6,style="background-color:#6082B6;color:white"):
                            text("On New Code")
                        if not feature_id:
                            with tag('th', colspan=6,style="background-color:#6082B6;color:white"):
                                text("On Overall Code")
                    with tag('tr'):
                        with tag('th'):
                            text("")
                        with tag('th'):
                            text("Pass")
                        with tag('th'):
                            text("Fail")
                        with tag('th'):
                            text("Error")
                        with tag('th'):
                            text("Total")
                        with tag('th'):
                                text("Test Report")
                        with tag('th'):
                            text("UT Coverage")
                        with tag('th'):
                            text("SA")
                        with tag('th'):
                            text("GoSec")
                        with tag('th'):
                            text("Woke")
                        with tag('th'):
                            text("LTC")
                        with tag('th'):
                            text("Uncovered lines")
                        if not feature_id:
                            with tag('th'):
                                text("UT Coverage")
                            with tag('th'):
                                text("SA")
                            with tag('th'):
                                text("GoSec")
                            with tag('th'):
                                text("Woke")
                            with tag('th'):
                                text("LTC")
                            with tag('th'):
                                text("Uncovered lines")
                    org_total_tc = 0
                    org_pass_tc = 0
                    org_fail_tc = 0
                    org_error_tc = 0
                    org_sa = 0
                    org_vul = 0
                    org_cs = 0
                    org_new_sa = 0
                    org_new_vul = 0
                    org_new_cs = 0
                    org_loc = 0
                    org_new_loc = 0
                    org_new_uncovered = 0
                    org_uncovered = 0
                    for key,data in sonar_repo_dic.items():
                        org = key.split("/")[0]
                        repo = key.split("/")[1]
                        if org == organization:
                            # Check if the repo is internal or external
                            _, internal = githubobj.read_build_sha(org, repo, artifactory_build)
                            if internal == "yes" and product != "ulb":
                                continue
                            sonar_project = get_sonar_project(org, repo)
                            proj_sonar_url = Constants.SONAR_URL + "/dashboard?id=" + sonar_project + "&branch=" + branch
                            print(proj_sonar_url)
                            if data:
                                for key, value in data.items():
                                    data[key] = str(value).strip()
                                with tag('tr'):
                                    with tag('td',style="text-align:center"):
                                        with tag('a', href='%s' % proj_sonar_url.strip() ):
                                            text(repo.strip())
                                    with tag('td',style="text-align:center"):
                                        text(data["UT_PASSED"].strip())
                                    if int(data["UT_FAILED"].strip()) > 0:
                                        with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                                            #with tag('a',href='%s' % data["TEST_REPORT"], style="color:white"):
                                            text(data["UT_FAILED"].strip())
                                    else:
                                        with tag('td',style="text-align:center"):
                                            text(data["UT_FAILED"].strip())
                                    if int(data["UT_ERROR"].strip()) > 0:
                                        with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                                            #console_url = data["TEST_REPORT"].replace("testReport","console")
                                            #with tag('a',href='%s' % console_url, style="color:white"):
                                            text(data["UT_ERROR"].strip())
                                    else:
                                        with tag('td',style="text-align:center"):
                                            text(data["UT_ERROR"].strip())

                                    # Total
                                    with tag('td',style="text-align:center"):
                                        text(int(data["UT_PASSED"]) + int(data["UT_FAILED"]))

                                    # Jenkins Job
                                    if int(data["UT_ERROR"].strip()) > 0 or int(data["UT_FAILED"].strip()) > 0:
                                        with tag('td',style="text-align:center"):
                                            with tag('a',href='%s' % data["TEST_REPORT"]):
                                                text("link")
                                    else:
                                        with tag('td',style="text-align:center"):
                                            text(" ")

                                    if data["NEW_UT_CC"] == "None" or not data["NEW_UT_CC"]:
                                        with tag('td',style="text-align:center"):
                                            text("-")
                                    else:
                                        if int(float(data["NEW_UT_CC"])) >= 90:
                                            with tag('td',style="text-align:center;background-color:Green;color:white"):
                                                text(data["NEW_UT_CC"] + "%")
                                        elif int(float(data["NEW_UT_CC"])) >= 50:
                                            with tag('td',style="text-align:center;background-color:Orange;color:white"):
                                                text(data["NEW_UT_CC"] + "%")
                                        else:
                                            with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                                                text(data["NEW_UT_CC"] + "%")
                                    if data["NEW_SA_BUGS"] == "None" or not data["NEW_SA_BUGS"]:
                                        with tag('td',style="text-align:center"):
                                            text("-")
                                    else:
                                        if int(data["NEW_SA_BUGS"]) > 0:
                                            with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                                                text(data["NEW_SA_BUGS"])
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["NEW_SA_BUGS"])
                                        org_new_sa += int(data["NEW_SA_BUGS"])
                                    # Vul
                                    if data["NEW_VULNERABILITY"] == "None" or not data["NEW_VULNERABILITY"]:
                                        with tag('td',style="text-align:center"):
                                            text("-")
                                    else:
                                        if int(data["NEW_VULNERABILITY"]) > 0:
                                            with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                                                text(data["NEW_VULNERABILITY"])
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["NEW_VULNERABILITY"])
                                        org_new_vul += int(data["NEW_VULNERABILITY"])
                                    # CS
                                    if data["NEW_CODE_SMELLS"] == "None" or not data["NEW_CODE_SMELLS"]:
                                        with tag('td',style="text-align:center"):
                                            text("-")
                                    else:
                                        if int(data["NEW_CODE_SMELLS"]) > 0:
                                            with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                                                text(data["NEW_CODE_SMELLS"])
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["NEW_CODE_SMELLS"])
                                        org_new_cs += int(data["NEW_CODE_SMELLS"])
                                    # LOC
                                    if data["NEW_LOC"] == "None" or not data["NEW_LOC"]:
                                        with tag('td',style="text-align:center"):
                                            text("-")
                                    else:
                                        with tag('td',style="text-align:center"):
                                            text(data["NEW_LOC"])
                                        org_new_loc += int(data["NEW_LOC"])
                                    # New uncovered
                                    if data["NEW_UNCOVERED_LINES"] == "None" or not data["NEW_UNCOVERED_LINES"]:
                                        with tag('td',style="text-align:center"):
                                            text("-")
                                    else:
                                        with tag('td',style="text-align:center"):
                                            text(data["NEW_UNCOVERED_LINES"])
                                        org_new_uncovered += int(data["NEW_UNCOVERED_LINES"])

                                    if not feature_id:
                                        if data["UT_CC"] == "None" or not data["UT_CC"]:
                                            with tag('td',style="text-align:center"):
                                                text("-")
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["UT_CC"] + "%")
                                        with tag('td',style="text-align:center"):
                                            text(data["SA_BUGS"])
                                            org_sa += int(data["SA_BUGS"])
                                        if data["VULNERABILITY"] == "None" or not data["VULNERABILITY"]:
                                            with tag('td',style="text-align:center"):
                                                text("-")
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["VULNERABILITY"])
                                                org_vul += int(data["VULNERABILITY"])
                                        if data["CODE_SMELLS"] == "None" or not data["CODE_SMELLS"]:
                                            with tag('td',style="text-align:center"):
                                                text("-")
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["CODE_SMELLS"])
                                                org_cs += int(data["CODE_SMELLS"])
                                        # LOC
                                        if data["LOC"] == "None" or not data["LOC"]:
                                            with tag('td',style="text-align:center"):
                                                text("-")
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["LOC"])
                                            org_loc += int(data["LOC"])

                                        if data["UNCOVERED_LINES"] == "None" or not data["UNCOVERED_LINES"]:
                                            with tag('td',style="text-align:center"):
                                                text("-")
                                        else:
                                            with tag('td',style="text-align:center"):
                                                text(data["UNCOVERED_LINES"])
                                                org_uncovered += int(data["UNCOVERED_LINES"])


                                        org_total_tc += int(data["UT_PASSED"]) + int(data["UT_FAILED"])
                                        org_pass_tc += int(data["UT_PASSED"])
                                        org_fail_tc += int(data["UT_FAILED"])
                                        org_error_tc += int(data["UT_ERROR"])
                            else:
                                 with tag('tr'):
                                    with tag('td',style="text-align:center"):
                                        with tag('a', href='%s' % proj_sonar_url.strip() ):
                                            text(repo)
                                    with tag('td',style="text-align:center",colspan=6):
                                        text("info not found in DB. Check sonar link")

                    with tag('tr'):
                        with tag('th',style="text-align: center"):
                            text("Total")
                        with tag('th', style="text-align: center"):
                            text(str(org_pass_tc))
                        with tag('th', style="text-align: center"):
                            text(str(org_fail_tc))
                        with tag('th', style="text-align: center"):
                            text(str(org_error_tc))
                        with tag('th', style="text-align: center"):
                            text(str(org_total_tc))
                        with tag('th',style="text-align: center"):
                            text("-")
                        with tag('th',style="text-align: center"):
                            text("-")
                        with tag('th',style="text-align: center"):
                            text(str(org_new_sa))
                        with tag('th',style="text-align: center"):
                            text(str(org_new_vul))
                        with tag('th',style="text-align: center"):
                            text(str(org_new_cs))
                        with tag('th',style="text-align: center"):
                            text(str(org_new_loc))
                        with tag('th',style="text-align: center"):
                            text(str(org_new_uncovered))
                        if not feature_id:
                            with tag('th',style="text-align: center"):
                                text("-")
                            with tag('th',style="text-align: center"):
                                text(str(org_sa))
                            with tag('th',style="text-align: center"):
                                text(str(org_vul))
                            with tag('th',style="text-align: center"):
                                text(str(org_cs))
                            with tag('th',style="text-align: center"):
                                text(str(org_loc))
                            with tag('th',style="text-align: center"):
                                text(str(org_uncovered))
                with tag('tr'):
                    with tag('td',colspan=colspan):
                        for i in range(0,length):
                            text("=")
            #with tag('p', style="font-family:Verdana; font-size:12px"):
            #    for i in range(0,length):
            #        text("=")
            #    doc.asis('<br>')
            with tag('p', style="font-family:Verdana; font-size:12px"):
                doc.asis('<br>')
                text("Thanks,")
                doc.asis('<br>')
                text("Releng Team")
                doc.asis('<br> <br>')
                if feature_id:
                    if feature_merged:
                        with tag('b'):
                            text("Please find attach list of PRs which are considered for feature %s" % feature_id)
                        doc.asis('<br>')

                    with tag('b'):
                        text("Test Report links will be available only for 5 days")
                    doc.asis('<br>')
                with tag('b'):
                    text("Sonar Utilities on Releng Dashboard")
                doc.asis('<br>')
                with tag('a', href='https://cn-rel-dash-lnx.cisco.com/sonarreport/%s' % product ):
                    text("Generate Sonar Report")
                text(" | ")
                with tag('a', href='https://cn-rel-dash-lnx.cisco.com/sonarrrun/%s' % product ):
                    text("Execute Sonar Run")
                text(" | ")
                with tag('a', href='https://cn-rel-dash-lnx.cisco.com/sonarstats/%s' % product ):
                    text("Sonar Stats")
                doc.asis('<br> <br>')
                with tag('b'):
                    text("Legends for UT Code Coverage %")
                with tag('table', style="font-family:Verdana; font-size:12px"):
                    with tag('tr'):
                        with tag('td',style="text-align:center;background-color:Green;color:white"):
                            text(">= 90%")
                    with tag('tr'):
                        with tag('td',style="text-align:center;background-color:Orange;color:white"):
                            text("< 90%, >= 50%")
                    with tag('tr'):
                        with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                            text("> 50%")
                doc.asis('<br>')
                with tag('b'):
                    text("Legend for UT Tests Failure/Error")
                with tag('table', style="font-family:Verdana; font-size:12px"):
                    with tag('tr'):
                        with tag('td',style="text-align:center;background-color:Tomato;color:white"):
                            text(" > 0")

    result = doc.getvalue()
    filename = "email_template.html"
    # Added this to ignore any unicode characters.
    result = result.encode('ascii', 'ignore').decode()
    print (result)
    write_to_file(result, filename)

    if archive_report:
        if qa_build:
            if not artifactory_build:
                print("QA build for the branch doesn't exists")
                sys.exit(2)
            else:
                if db_env == '5g-dev':
                    mount = Constants.TEST_NFS_MOUNT
                    mount_link = Constants.TEST_ARCHIVE_LINK
                else:
                    mount = Constants.NFS_MOUNT
                    mount_link = Constants.ARCHIVE_LINK

                db_table = "builds"
                # Find the branch_version
                end_build_list=artifactory_build.split('.')
                end_build_list.pop()
                release_dir='.'.join(end_build_list)
                sonar_report_fname = "sonar_report_%s" % artifactory_build
                write_to_file(result, sonar_report_fname)
                archive_dir = mount+"/"+release_dir+"/"+product.lower()+"/"+artifactory_build+"/"
                db_sonar_report=mount_link+release_dir+"/"+product.lower()+"/"+artifactory_build+"/"+sonar_report_fname
                try:
                    if not os.path.isdir(archive_dir) :
                        #Default umask in jenkins is 700 which is too restrictive
                        mask = 2
                        old_mask = os.umask(mask)
                        os.makedirs(archive_dir, mode=0o2775, exist_ok=True)
                        os.umask(old_mask)
                    if os.path.exists(sonar_report_fname):
                        shutil.copy(sonar_report_fname, archive_dir)
                        os.chmod(os.path.join(archive_dir, sonar_report_fname), 0o644)
                        print("Successfully copied changes report ", sonar_report_fname, " to build dir : ", archive_dir)
                    else:
                        print(sonar_report_fname, ": file does not exist")
                except Exception as e:
                    sys.exit("ERROR: Unable to copy changes report to build dir. Error: %s\n\n" % e)

                # Update DB for CHANGES REPORT link- This is per product
                try:
                    dbo=CPSOracle(DB_INI, db_env)
                    data={"SONAR_REPORT":db_sonar_report}
                    condition={"build_number":artifactory_build,"products_product_name":product}
                    if condition:
                        db_status=dbo.update_rows(db_table, data, condition)
                        if db_status == 1:
                            print("ERROR: Unable to update DB.")
                except Exception as e:
                    sys.exit("ERROR: Unable to update DB. Error: %s\n\n" % e)

if __name__ == "__main__":
    sys.exit(main())