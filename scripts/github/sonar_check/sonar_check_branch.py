# author  : <PERSON><PERSON><PERSON>
# Flow of the script
# input :  PR info as parameter instead of payload.
# Check if unit test is required
#   - Unit test needs to be executed only if changes are done in .go files & not in _test
# If unit test is required.
#   - Clone the repo
#   - run docker command
#   - check sonar status
#   - if sonar status is green -> stamp clean on github for unit-test

import sys
import os
import argparse
import datetime

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

sonar_checks_ini = BASE_DIR + "/libs/sonar_checks.ini"

from GithubApi import GithubApi
import Constants as Constants
from sonar_utils import *
from vault import VaultSecret
from common import *

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('-org', '--organization', required=True, help="organization")
    parser.add_argument('-repo', '--repo', required=False, help="repo")
    parser.add_argument('-branch', '--branch', required=False, help="branch")
    parser.add_argument('-t','--triggered_by', required=False, help="branch")
    parser.add_argument('-rb','--reference_branch', required=False, help="reference_branch" )
    parser.add_argument('-pd','--product', required=False, help="product")
    parser.add_argument('-rqb','--reference_qa_build', required=False, help="reference_qa_build" )
    parser.add_argument('-p','--project_date', required=False, help="project_date" )
    parser.add_argument('-cp','--copy_from_main', action='store_true', required=False, help="if you want to copy files from main branch" )
    args = parser.parse_args()
    organization = args.organization
    repo = args.repo
    branch = args.branch
    triggered_by = args.triggered_by
    reference_branch = args.reference_branch
    reference_qa_build = args.reference_qa_build
    project_date = args.project_date
    githubobj = GithubApi()

    workspace = os.environ["WORKSPACE"]
    repo_url = "https://" + Constants.USERNAME + ":" + Constants.FULL_TOKEN + "@wwwin-github.cisco.com/" + organization + "/" + repo + ".git"
    clone_dir= workspace+ "/" + repo
    main_sha = githubobj.read_head_sha(organization, repo, 'main')
    head_sha = githubobj.read_head_sha(organization, repo, branch)
    tree = githubobj.get_tree(organization,repo,head_sha)
    image_dir_path,images_dir, dockerfile_flist= get_image_dir_path(tree,clone_dir)
    copy_from_main = args.copy_from_main
    if not dockerfile_flist:
        print("Dockerfile.sonar is not found on %s branch")
        tree_main = githubobj.get_tree(organization,repo,main_sha)
        image_dir_path,images_dir, dockerfile_flist = get_image_dir_path(tree_main,clone_dir)
        if not dockerfile_flist:
            print("Dockerfile.sonar is not found on main branch")
            sys.exit(1)
        else:
            copy_from_main=True
    pr_url = None
    if args.reference_branch or args.reference_qa_build:
        #get sonar project name
        sonar_project = get_sonar_project(organization,repo)
        sonar_branch_existence = check_sonar_branch_existance(sonar_project,branch)
        sonar_branch_delete_status = delete_sonar_branch(sonar_project, branch)
        if sonar_branch_existence:
            if not sonar_branch_delete_status:
                print("Warning: Unable to delete branch in sonar")
                sys.exit(1)
            else:
                print("Successfully Deleted %s branch in sonar"%branch)
                branch_result = ""
    if not reference_branch and not reference_qa_build:
        reference_branch = head_sha

    # download commit_info.json
    # find the sha ID
    if reference_qa_build:
        artifactory_path_to_promote_to, commit_info_json_url = get_commitinfo_json_url(reference_qa_build,args.product,"5g-prod")
        print("Downloading %s" %commit_info_json_url)
        if commit_info_json_url:
            download_status = download_commitinfo_json(reference_qa_build,commit_info_json_url)
            if not download_status:
                print("commit_info.json download failed for build %s, please check" %reference_qa_build)
                sys.exit(1)
        else:
            print("commit_info.json doesn't exist for build %s, please check" %reference_qa_build)
            sys.exit(1)

        reference_branch,build_internal = githubobj.read_build_sha(organization,repo,reference_qa_build)
        config_section_name="5g-prod"
        version_info_obj = version_info(config_section_name=config_section_name)
        dbo = version_info_obj.open_connection()
        query = "select BUILD_ONLINE_START_TIME from builds where products_product_name = '%s' and build_number = '%s' "% (args.product,reference_qa_build)
        print(query)
        value = dbo.run_query(query)
        if value:
            timestamp = value[0]['BUILD_ONLINE_START_TIME']
            if timestamp:
                # convert the timestamp into date format
                project_date = datetime.datetime.fromtimestamp(int(timestamp)).strftime("%Y-%m-%d")
                print("project_date:" + project_date)

    # get src dir
    print("images_dir : %s" % images_dir)
    print("image_dir_path : %s" % image_dir_path)
    print("repo : %s" % repo)
    src_dir_path,src_dir,sonar_script_flist = get_src_dir_path(tree,clone_dir,image_dir_path,repo)

    #Attempt to log into Vault and obtain a response wrapper token to fetch the dockerhub credentials
    role_id, secret_id = None, None
    if "DOCKER_ROLE_ID" in os.environ and os.environ.get('DOCKER_ROLE_ID') != "role_id":
        role_id = os.environ.get('DOCKER_ROLE_ID')
    if "DOCKER_SECRET_ID" in os.environ and os.environ.get('DOCKER_SECRET_ID') != "secret_id":
        secret_id = os.environ.get('DOCKER_SECRET_ID')
    if not role_id or not secret_id:
        sys.exit("ERROR:  Need DOCKER_ROLE_ID & DOCKER_SECRET_ID in environment variable to fetch dockerhub credentials from Vault")
    print("Attempting to log into Vault")
    vaultSecretObj = VaultSecret(role_id=role_id, secret_id=secret_id)

    # Step 1: Create a token
    wrapper_token = vaultSecretObj.get_vault_token(role_id, secret_id)

    # Step 2: Unwrap the token
    unwrapped_token = vaultSecretObj.unwrap_token(wrapper_token)

    # Step 3: Look up the token for Validation
    if vaultSecretObj.validate_token(unwrapped_token):
        print("Token validation successful")
        os.environ['VAULT_TOKEN'] = unwrapped_token
    else:
        sys.exit("ERROR: Invalid token")

    result = execute_test_on_branch(workspace,images_dir,src_dir,src_dir_path,repo_url,clone_dir,organization,repo, branch,reference_branch,pr_url,copy_from_main,triggered_by,project_date)
    sys.exit(result)

if __name__ == "__main__":
    sys.exit(main())
