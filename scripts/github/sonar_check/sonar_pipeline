pipeline {
    agent { label 'build' }
    options {
        timeout(time: 2, unit: 'HOURS')
    }
    stages {
	    stage('Clean workspace') {
		    steps {
			    sh 'rm -rf ${WORKSPACE}/*.html'
				sh 'rm -rf ${WORKSPACE}/*.tar.gz'
				sh 'rm -rf ${WORKSPACE}/*.properties'
				buildDescription '$ORGANIZATION'
				addShortText background: 'yellow', border: 1, text: "$BRANCH_NAME"
			}
		}
		stage('Trigger Sonar Run') {
            steps {
	            script {
	                def tasks = [:]
	                def repoList = params.REPO_LIST.split(',')
	                for (int i = 0; i < repoList.size(); ++i)
	                {
	                    def index = i
	                    tasks [i] = {
					        node
					        {
					            try {
					                job_info = build job: 'Developer_Jobs/code_coverage_branch', propagate: true,
					                parameters: [string(name: 'REPO', value: "${repoList[index]}"),
					                             string(name: 'ORGANIZATION', value: "$ORGANIZATION"),
                                                 string(name: '<PERSON><PERSON><PERSON>_NAME', value: "$BRANCH_NAME"),
					                             string(name: '<PERSON><PERSON>EN<PERSON>_BRANCH_NAME', value: "$RELENG_BRANCH_NAME"),
					                             string(name: 'REFERENCE_BRANCH', value: "REFERENCE_BRANCH"),
					                             string(name: 'PROJECT_DATE', value: "PROJECT_DATE"),
					                             string(name: 'TRIGGERED_BY', value: "TRIGGERED_BY")
					                             ]
					            }
					            catch (err) {
					                println(err)
                                    if ("${err}".startsWith('org.jenkinsci.plugins.workflow.steps.FlowInterruptedException')) {
                                        currentBuild.result = 'ABORTED'
                                    }
                                    else {
                                        currentBuild.result = 'FAILURE'
                                    }
					            }
					        }
					    }
	                }
	                // Create a dictionary of jobs & then run that in parallel.
                    parallel tasks
                }
		    }
        }
   }
}
