"""
 Copyright (c) 2023, Cisco Systems Inc.
 author: sku<PERSON><PERSON> that can be used to :
	1. Sync code changes of main branch to dev/code coverage branch

"""

import argparse
import sys
import os
import re
import shutil


THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

from Constants import *
from Utils import *
from GithubApi import Gith<PERSON><PERSON><PERSON>
from common import *
from git_libs import do_git_clone
###############
#GLOBAL Variables
###############
cwd=os.getcwd()
githubobj = Github<PERSON><PERSON>()
report_fail_list = []
report_skip_list = []
################

# Algorithm

# Step 1 : Read Inputs
# Step 2 : Get All orgs for a product and then get component list for each org
# Step 3 : Iterate all components to do following tasks :
# Step 4 : Checkout destination branch
# Step 6 : Merge QA build sha on destination branch



###########################################
# Branch Sync: Merge main to CC branch
###########################################
def sync_branch(org, component_list, branch, build_number, dryrun):
    global report_fail_list 
    global report_skip_list
    report_dict = {}
    githubobj = GithubApi()
    
    for repo_name in component_list:
        print ("*************************************")
        print (repo_name)
        print ("*************************************")
        repo = repo_name.rsplit('/',1)[-1]
        os.chdir(cwd)
        build_sha,build_internal = githubobj.read_build_sha(org,repo,build_number)
        print("build sha : %s" %build_sha)

        if build_sha == None:
            print("Branch: " + branch + " not available for the repo. Hence skipping merge of repo: " + repo)
            report_skip_list.append(repo)
        else:
            
            repo_url = "https://%s:%<EMAIL>/%s/%s.git"%(USERNAME,FULL_TOKEN,org,repo)
            clone_dir= cwd+ "/" + repo
            if os.path.exists(clone_dir):
                shutil.rmtree(clone_dir)
            clone_command = "git clone %s %s -b %s" % (repo_url.strip(), clone_dir.strip(), branch)
            print(clone_command)  
            status = os.system(clone_command)  
            #status = do_git_clone(repo_url, repo, branch)
            if status != 0:
                print("repo clone failed, please check..") 
                report_fail_list.append(repo)
                continue
            else:
                os.chdir(clone_dir)

            try:
                if dryrun:
                    cmd = "git merge --no-commit --no-ff --stat " + build_sha
                    print ("cmd="+cmd)
                    merge_stat =  subprocess.getoutput(cmd)
                    print ("Merge dry-run status on repo {}: ".format(repo)  + merge_stat)
                    cmd = "git status"
                    print ("cmd="+cmd)
                    stat =  subprocess.getoutput(cmd)
                    print(stat)
                else: 
                    cmd = 'git merge ' + build_sha + ' --strategy-option theirs'
                    #cmd = 'git merge ' + build_sha
                    print ("Executing: "+cmd)
                    git_merge_cmd = subprocess.getoutput(cmd)    
                    print ("Merge status on repo {}: ".format(repo)  + git_merge_cmd)   
                    cmd = 'git add -A '
                    print ("Executing: "+cmd)
                    git_add_cmd = subprocess.getoutput(cmd)
                    cmd = "git push origin " + branch
                    print ("Executing: "+cmd)
                    push_cmd = subprocess.getoutput(cmd)
                    print ("Executing: "+push_cmd) 
            except Exception as e:
                report_fail_list.append(repo)
    return(report_dict)


#
# MAIN
#
def main():
    parser = argparse.ArgumentParser()
    required = parser.add_argument_group('required arguments')
	# Mandatory parameters
    parser.add_argument('-p', '--product', required=True, help="product")
    required.add_argument('-d', '--db_name', required=True, help='database name')
    required.add_argument('-br', '--branch', required=False, default="main", help = 'Branch Name (Default:main)')
    parser.add_argument('-bn', '--build_number', help="QA build number")
    parser.add_argument('-dr', '--dryrun', required=False, default='True', help = 'Run the merge in dry run mode(without actual git push)')

    args = parser.parse_args()
    DBINST = args.db_name
    build_number = args.build_number
    branch = args.branch
    product = args.product
    if (args.dryrun in ('true' , 'True', '1' )):
        dryrun = 1
        print ("Setting dry-run mode")
    else:
        dryrun = 0
    print ("Processing Product = "+product+" and build number = " + build_number)


	# Download commit_info json file for a QA build
    try:
        print("Processing build : %s" % build_number)
        artifactory_path_to_promote_to, commit_info_json_url = get_commitinfo_json_url(build_number,product,DBINST)
        print("Downloading %s" %commit_info_json_url)
        if commit_info_json_url:
            download_status = download_commitinfo_json(build_number,commit_info_json_url)
            if not download_status:
                print("commit_info.json download failed for build %s, please check" %build_number)
                sys.exit(1)
        else:
            print("commit_info.json doesn't exist for build %s, please check" %build_number)
            sys.exit(1)
    except Exception as e:
        print("Error: Failed to download commit_info.json for %s" % build_number)
        print(e)
        sys.exit(1)

    jsonfile = "%s/%s_commit_info.json"%(os.getcwd(),build_number)
    print("commit_info.json path: " + jsonfile)
    product_org_list = get_product_orglist(jsonfile, product)

    print("Product org list to process: ", product_org_list)
    print("Processing Organization list: %s" % (', '.join(product_org_list)))
    if product_org_list:
        try:
            fname="organization_list.txt"
            fp = open(fname,"a", encoding='utf-8')
            content = "ORGANIZATION=%s" %(','.join(product_org_list))
            fp.write(content)
            fp.write("\nBASE_BRANCH_NAME=main\nREL_BRANCH_NAME=%s"%branch)
            fp.close()
        except Exception as e:
            sys.exit("ERROR: Failed to dump list of orgs into file. Error: %s\n\n" % e)

    for product_org in product_org_list:
        report_dict = {}
        branch_component_list = GithubApi.list_all_repos(product_org)
        if branch_component_list == 0:
            print("ERROR: Unable to list all repos at this moment for org: ", product_org)
            sys.exit("ERROR: Unable to list all repos at this moment for org:%s \n\n" %(product_org))
        print ("*************************************")
        print ("Merging QA build sha to CC branch...")
        print ("*************************************")

        report_dict=sync_branch(product_org,branch_component_list,branch,build_number,dryrun)

    if report_fail_list:
        print("Merge failed for below repos:")
        print("*************************************")
        print(report_fail_list)
        sys.exit(1)        
    
if __name__ == "__main__":
	sys.exit(main())
