"""
 Copyright (c) 2023, Cisco Systems Inc.
 author: s<PERSON><PERSON><PERSON> that can be used to :
    1. Accept the code coverage report as input zip
    2. Extract code coverage report 
    3. Copy the reports for NFS location
    4. Update report permissions to 755(775-> temporary permission) once the reports upload is done
"""

import sys
import os

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

import Constants as Constants
import argparse
from common import run
import shutil
import zipfile
import tarfile
import fnmatch
from sonar_utils import *

#copy code coverage reports to NFS location
def copy_cc_report_zip(archive_dir,cc_report): 
    mask = 2
    old_mask = os.umask(mask)
    if os.path.exists(archive_dir):
        shutil.rmtree(archive_dir)
        print("Deleted %s"%archive_dir)
    
    os.makedirs(archive_dir, mode=0o2775)    
    os.chdir(archive_dir)   

    print("Copying %s to %s"%(cc_report,archive_dir))
    try:      
        shutil.copy(cc_report, archive_dir)
    except Exception as e:
        print("Failed to copy code coverage report %s" %e)
        sys.exit(1)    

    print("Extracting code coverage reports to %s" %archive_dir)
    os.chdir(archive_dir) 
    try:     
        for path, dirs, files in os.walk("."):
            if "cc_report.zip" in files:
                # If the file is found, unzip it
                filepath = os.path.join(path, "cc_report.zip")
                print(filepath)
                with zipfile.ZipFile(filepath, "r") as zip_ref:
                    zip_ref.extractall(path)
                print("code coverage reports have been extracted..")           
    
        for path, dirs, files in os.walk("."):
            for filename in (fnmatch.filter(files, '*.tgz') or fnmatch.filter(files, '*.tar.gz')):
                tgz_path = os.path.join(path, filename)
                with tarfile.open(tgz_path, 'r:gz') as tar_obj:
                    tar_obj.extractall(path)
                print("Extracted" + tgz_path)  
        print("\nSuccessfully copied and extraced coverage reports to %s" %archive_dir)
    except Exception as e:
        print("Failed to copy code coverage report %s" %e)
        sys.exit(1)
    os.chmod(archive_dir,0o755)
    os.umask(old_mask)    

def main():

    parser = argparse.ArgumentParser(description='Script to upload code coverage reports to artifactory and NFS mount point')
    parser.add_argument('-p','--product', required=True, help = 'Product name')
    parser.add_argument('-b', '--branch', required=True, help='Branch name')
    parser.add_argument('-t','--test_type', required=True, help = 'Test type')
    parser.add_argument('-bn','--build_number', required=True, help = 'QA build number')
    parser.add_argument('-test', '--test_run', required=False, default='False', help = 'test code coverage run')

    try:
        args = parser.parse_args()
    except Exception as e:
        return e

    running_node = os.uname()[1]
    if running_node in Constants.HOS_HOSTS:
        CC_NFS_MOUNT = Constants.HOS_CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.HOS_TEST_CC_NFS_MOUNT
    else:
        CC_NFS_MOUNT = Constants.CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.TEST_CC_NFS_MOUNT

    branch = args.branch
    product = args.product
    test_type = args.test_type
    build_number = args.build_number
    if (args.test_run in ('true' , 'True', '1' )):
        CC_NFS_MOUNT = TEST_CC_NFS_MOUNT
    else:
        CC_NFS_MOUNT = CC_NFS_MOUNT
    workspace = os.getcwd()
    cc_report = workspace + "/cc_report.zip"
    print(cc_report)
    archive_dir = "%s/%s/%s/%s/%s"%(CC_NFS_MOUNT,branch,product,build_number,test_type)   
    copy_cc_report_zip(archive_dir,cc_report)

    # upload ivt and svi reports to SysTest location
    if test_type in ["ivt","svi"]:
        test_type = "ST"
        archive_dir = "%s/%s/%s/%s/%s/"%(CC_NFS_MOUNT,branch,product,build_number,test_type) 
        copy_cc_report_zip(archive_dir,cc_report)
    softlink_creation(branch,product,build_number,CC_NFS_MOUNT)
    os.chdir(CC_NFS_MOUNT)
    rmv_command = "find . -name '__MACOSX' -exec rm -rf {} \\;"
    print(rmv_command)
    os.system(rmv_command)
    os.chdir(TEST_CC_NFS_MOUNT)
    rmv_command = "find . -name '__MACOSX' -exec rm -rf {} \\;"
    print(rmv_command)
    os.system(rmv_command)
    stream = os.popen("find . -name '__MACOSX'")

if __name__ == "__main__":
    sys.exit(main())
