# author  : <PERSON><PERSON><PERSON>
# Flow of the script
# input :  PR info as parameter instead of payload.
# Check if unit test is required
#   - Unit test needs to be executed only if changes are done in .go files & not in _test
# If unit test is required.
#   - Clone the repo
#   - run docker command
#   - check sonar status
#   - if sonar status is green -> stamp clean on github for unit-test

import sys
import os
import argparse
import re
import configparser

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

sonar_checks_ini = BASE_DIR + "/libs/sonar_checks.ini"
woke_yaml = THIS_DIR + "/woke_rules.yaml"
scrub_script= PARENT_DIR + "/scripts/github/sonar_check/scrub_cc.go"

from GithubApi import GithubApi
import Constants as Constants
from common import read_file, run, append_to_file, write_to_file
from cdets_libs import add_file
from sonar_utils import *
import subprocess
from version_info import version_info
from product_sonar_analysis import execute_sonar_analysis
from cnjenkins import CNJenkins
from vault import VaultSecret
from datetime import datetime

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('-url', '--pr_url', required=True, help="pr_url")
    parser.add_argument('-cl', '--cdets_list', required=False, help="cdets_list")
    args = parser.parse_args()
    githubobj = GithubApi()
    

    pr_url = args.pr_url.rstrip("/")
    pr_url_info = pr_url.split("/")
    pr_organization = pr_url_info[3]
    pr_repo = pr_url_info[4]
    pr_number = pr_url_info[6]

    pr_to_branch = os.environ.get("pr_to_branch", None)
    pr_from_branch = os.environ.get("pr_from_branch", None)
    pr_head_sha = os.environ.get("pr_head_sha", None)
    pr_state = os.environ.get("pr_state", None)
    pr_owner = os.environ.get("pr_author_name", None)
    pr_merged = os.environ.get("merged_at", None)

    if None in [pr_to_branch, pr_from_branch, pr_head_sha, pr_state, pr_owner, pr_merged]:
        print("PR information is missing in the env variables")
        pr_info = githubobj.get_pr_details(pr_organization, pr_repo, pr_number)
        pr_to_branch = pr_info["pr_to_branch"]
        pr_from_branch = pr_info["pr_from_branch"]
        pr_head_sha = pr_info["pr_head_sha"]
        pr_state = pr_info["state"]
        pr_merged = pr_info["merged"]
        pr_owner = pr_info["pr_owner"]
    else:
        print("PR information is picked from the env variables ")

    print("PR to branch: %s" % pr_to_branch)
    print("PR from branch: %s" % pr_from_branch)
    print("PR head SHA: %s" % pr_head_sha)
    print("PR state: %s" % pr_state)
    print("PR author name: %s" % pr_owner)
    print("PR merged at: %s" % pr_merged)

    target_url = os.environ['BUILD_URL'] + "console"
    context = "code-coverage"
    sonar_context = "sonar-check"
    ut_context = "Unit-test"
    exit_status_on_failure = 1
    workspace = os.environ["WORKSPACE"]
    repo_url = "https://" + Constants.USERNAME + ":" + Constants.FULL_TOKEN + "@wwwin-github.cisco.com/" + pr_organization + "/" + pr_repo + ".git"
    clone_dir= workspace+ "/" + pr_repo
    artifactory_pr_download_dir = "mobile-cnat-charts-release/" + pr_organization + "/" + pr_repo + "/" + pr_number + "/"
    artifactory_pr_upload_url = Constants.QA_ARTIFACTORY + "/" + pr_organization + "/" + pr_repo + "/" + pr_number + "/"
    artifactory_file_ws_loc = workspace + "/" + pr_organization + "/" + pr_repo + "/" + pr_number
    sonar_exclusion=None
    # if PR is closed & not merged
    if pr_state == "closed" and not pr_merged:
        sys.exit(0)
    
    # skipping code coverage support check to enable SA for all branches
    #if not check_code_coverage_support(pr_organization,pr_repo,pr_to_branch):
    #    sys.exit(0)

    # Check if the files changed as a part of PR has any .go file
    # if not, docker build is not required.

    files_modified = os.environ.get("pr_file_list", None)
    if not files_modified:
        file_list = githubobj.get_pr_file_list(pr_organization, pr_repo, pr_number)
    else:
        file_list =files_modified.split(",")

    print("Files modified as a part of PR : %s" % file_list)
    go_file_list = []
    java_file_list = []
    if file_list == "only_deleted_files":
        description = "no .go file is changed as a part of pr"
        state = "success"
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, ut_context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, sonar_context)
        sys.exit(0)
        
    if file_list:
        for file in file_list:
            if file.endswith(".go"):
                go_file_list.append(file)
            if file.endswith(".java"):  
                java_file_list.append(file)
    else:
        description = "Error in fetching PR file list. <NAME_EMAIL>"
        state = "failure"
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, ut_context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, sonar_context)
        sys.exit(1)

    print("pr_state : %s" % pr_state)
    print("pr_merged : %s" % pr_merged)



    # Check if there is any go file change
    if not go_file_list and not java_file_list and pr_state != "closed":
        description = "no .go and .java file have been modified as a part of pr."
        state = "success"
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, ut_context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, sonar_context)
        sys.exit(0)

    # Run the check irrespective of the change
    #if not go_file_list and pr_state == "closed":
    #    print("Since there is no change in .go file. No need for SA run on base branch")
    #    sys.exit(0)

    # read the config file
    config = configparser.ConfigParser()
    config.read(os.path.realpath(sonar_checks_ini))
    try:
        key_name = 'repos_admin_cmd'
        repos_admin_cmd = config.get(pr_organization, key_name).split(",")
        key_name = 'cc_threshold_90'
        repos_cc_threshold_90 = config.get(pr_organization, key_name).split(",")
    except configparser.NoSectionError:
        print(f"Section '{pr_organization}' not found in the configuration file.")
        sys.exit(2)
    except configparser.NoOptionError:
        print(f"Key '{key_name}' not found in the section '{pr_organization}'.")
        sys.exit(2)
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(2)

    # get head sha of the base branch
    branch_to_head_sha = githubobj.read_head_sha(pr_organization, pr_repo, pr_to_branch)
    base_tree = githubobj.get_tree(pr_organization,pr_repo,branch_to_head_sha)
    image_dir_path,images_dir,dockerfile_flist = get_image_dir_path(base_tree,clone_dir)
    print("images_dir : %s" % images_dir)
    print("image_dir_path : %s" % image_dir_path)
    src_dir_path,src_dir,sonar_script_flist = get_src_dir_path(base_tree,clone_dir,image_dir_path,pr_repo)
    copy_from_main = True
    if not dockerfile_flist:
        print("Dockerfile.sonar doesn't exist in base branch. <NAME_EMAIL>")
        print("Check if Dockerfile.sonar exist for main branch")
        main_branch_head_sha = githubobj.read_head_sha(pr_organization, pr_repo, 'main')
        main_tree = githubobj.get_tree(pr_organization,pr_repo,main_branch_head_sha)
        image_dir_path,images_dir,dockerfile_flist=get_image_dir_path(main_tree,clone_dir)
        if not dockerfile_flist:
            print("Dockerfile.sonar doesn't exist in main branch. <NAME_EMAIL>")
            description = "Dockerfile.sonar doesn't exist in base branch or main. <NAME_EMAIL>"
            state = "failure"
            githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, context)
            githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, ut_context)
            githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, sonar_context)
            sys.exit(2)
    else:
        print("Dockerfile.sonar exits in base branch")
        copy_from_main = False

    #Attempt to log into Vault and obtain a response wrapper token to fetch the dockerhub credentials
    role_id, secret_id = None, None
    if "DOCKER_ROLE_ID" in os.environ and os.environ.get('DOCKER_ROLE_ID') != "role_id":
        role_id = os.environ.get('DOCKER_ROLE_ID')
    if "DOCKER_SECRET_ID" in os.environ and os.environ.get('DOCKER_SECRET_ID') != "secret_id":
        secret_id = os.environ.get('DOCKER_SECRET_ID')
    if not role_id or not secret_id:
        sys.exit("ERROR:  Need DOCKER_ROLE_ID & DOCKER_SECRET_ID in environment variable to fetch dockerhub credentials from Vault")
    print("Attempting to log into Vault")
    vaultSecretObj = VaultSecret(role_id=role_id, secret_id=secret_id)

    # Step 1: Create a token
    wrapper_token = vaultSecretObj.get_vault_token(role_id, secret_id)

    # Step 2: Unwrap the token
    unwrapped_token = vaultSecretObj.unwrap_token(wrapper_token)

    # Step 3: Look up the token for Validation
    if vaultSecretObj.validate_token(unwrapped_token):
        print("Token validation successful")
        os.environ['VAULT_TOKEN'] = unwrapped_token
    else:
        sys.exit("ERROR: Invalid token")

    if (pr_state == "closed" and pr_merged):
        print("PR %s got merged" % pr_url)
        exit_status_on_failure = execute_test_on_branch(workspace,images_dir,src_dir,src_dir_path,repo_url,clone_dir,pr_organization,pr_repo, pr_to_branch,branch_to_head_sha,pr_url,copy_from_main,pr_owner,project_date=None)
        # Download from artifactory coverage info of the PR which got merged.
        exit_status_on_failure = 0
        print("PR details : org: %s  pr_to_branch : %s" % (pr_organization,pr_to_branch))
        if re.match('^main$|^rel-|^v',pr_to_branch) and exit_status_on_failure == 0:
            config_section_name="5g-prod"
            version_info_obj = version_info(config_section_name=config_section_name)
            dbo = version_info_obj.open_connection()
            product=get_product_name_cc(pr_organization,version_info_obj)
            print ("UT branch picked as per DB entry : %s" % pr_to_branch)
            if pr_organization in ['mobile-cnat-cn','mobile-cnat-smf','mobile-cnat-sgw','mobile-cnat-amf','mobile-cnat-app-infra','mobile-cnat-golang-lib']:
                cc_branch = get_cc_branch_name(product,pr_organization,pr_to_branch,dbo)
            else:
                cc_branch = ""
            if cc_branch:
                sonar_branch="CC-" + cc_branch
                for key,value in Constants.st_branch.items():
                    if key == pr_to_branch and pr_organization !='mobile-cnat-amf':
                        st_branch = value
                    else:
                        st_branch = ""
                print("ST branch chosen:%s" % st_branch)
                print("Latest FT Branch for which Coverage result available: %s" % cc_branch)
            else:
                print("***Instrumented branch details not available to run combined code coverage ***Triggering combined code coverage with PR details")
                cc_branch=""
                sonar_branch="CC-" + str(pr_to_branch)
                st_branch=""
            now = datetime.now()
            project_date = now.strftime("%Y-%m-%dT%H:%M:%S-0700")
            print("PROJECT DATE: %s" % project_date)
            pr_org=list(pr_organization.split(" "))
            jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])
            job_name = "Code_Coverage/sonar_analysis"
            if pr_organization == 'mobile-cnat-rcm' and pr_to_branch =='v21.28.mx' :
                cc_branch = Constants.st_branch[pr_to_branch]
                st_branch = Constants.st_branch[pr_to_branch]
            parameter = {"PRODUCT" : product,
                         "ORGANIZATION" : pr_organization,
                         "REPO" : pr_repo,
                         "TEST_TYPE" : "CC",
                         "REFERENCE_BRANCH" : "",
                         "SONAR_BRANCH" : sonar_branch,
                         "BRANCH" : pr_to_branch,
                         "PROJECT_DATE" : project_date,
                         "CC_BUILD_NUMBER" : "",
                         "Push_old_data" : False,
                         "UT_BRANCH" : pr_to_branch,
                         "FT_BRANCH" : cc_branch,
                         "ST_BRANCH" : st_branch,
                         "Delete_Branch" : False,
                         "FEATURE_BRANCH": False,
                         "RELENG_BRANCH_NAME": os.environ["RELENG_BRANCH_NAME"]}
            print("job_name : %s" % job_name)
            print("parameter : %s" % parameter)
            build_url = jenkins_obj.trigger_build_params(job_name,parameter)
            if build_url != "Aborted":
                build_number = build_url.split('/')[-2]
                print("Build triggered to compute combined code coverage for branch %s : %s" % (pr_to_branch,build_url))
            elif not build_url:
                print("Build not triggered to compute combined code coverage")

        ret_value = download_from_artifactory(artifactory_pr_download_dir,workspace)
        if ret_value is None or not ret_value:
            exit_status_on_failure = 2         
        cdets = args.cdets_list
        if not cdets:
            cdets_list = githubobj.get_pr_cdets_list(pr_organization, pr_repo, pr_number)["valid"]
        else:
            cdets_list = cdets.split(",")
        print("List of CDETS used in PR : %s" % cdets_list)

        # Check if there is a coverage report available which needs to be added to CDETS
        coverage_file_loc = artifactory_file_ws_loc + "/coverage.html"
        if os.path.exists(coverage_file_loc):
            print("add file to cdets")
            title = "%s_%s_%s_coverage_report" % (pr_organization, pr_repo, pr_number)
            for cdets in cdets_list:
                add_file(cdets, title, coverage_file_loc, overwrite=False)
            # Delete the artifactory location
            delete_artifactory_path(artifactory_pr_upload_url)
            sys.exit(0)
        else:
            print("Coverage file couldn't be downloaded from artifactory")
            exit_status_on_failure = 2
        sys.exit(exit_status_on_failure)
    else:
        description = "Code Coverage(UT) check in progress"
        sonar_description = "Sonar check (Gosec/SA/Woke) in progress"
        ut_description = "Unit test run in progress"
        ut_state = sa_state = state = sonar_state = "pending"
        ut_target_url = os.environ['BUILD_URL']

        result = githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, context)
        if not result:
            print("ERROR : unable to update the context")
        
        ut_result = githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, ut_state, target_url, ut_description, ut_context)
        if not ut_result:
            print("ERROR : unable to update the context")
        sonar_result = githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, ut_state, target_url, sonar_description, sonar_context)
        if not sonar_result:
            print("ERROR : unable to update the context")

        package_list = []

        # clone the repo to run docker command
        clone_command = "git clone %s %s -b %s" % (repo_url.strip(), clone_dir.strip(), pr_from_branch)
        print("clone command is %s" % clone_command)
        status = os.system(clone_command)
        print("Status : %s" % status)

        sonar_state = "failure"
        if status != 0:
            print("git clone failed")
            ut_description = sonar_description = sa_description = description = "fail to clone repo. contact releng team"
            sa_state = state = "failure"
        else:
            os.chdir(clone_dir)
            sonar_properties_file = src_dir + "sonar-project.properties"
            print("sonar_properties_file : %s" % sonar_properties_file)
            if not os.path.exists(sonar_properties_file):
                sonar_properties_file = images_dir + "sonar-project.properties"
            print("sonar_properties_file : %s" % sonar_properties_file)
            dockerfile_test = images_dir + "Dockerfile.sonar"
            if pr_repo == 'ebpf-infra':
                num_of_mount,source_loc_split,target_loc_split=get_src_tgt_mnt(dockerfile_test)
            golint_file = src_dir + "golangci-lint.yml"
            if not os.path.exists(golint_file):
                golint_file = images_dir + "golangci-lint.yml"

            run_sonar_sh = src_dir + "/run_sonar.sh"
            # copy woke in the clone
            command = "cp %s %s" % (woke_yaml, src_dir)
            status = os.system(command)
            if status != 0:
                print("copy of woke to src dir failed")

            command = "cp %s %s" % (scrub_script, src_dir)
            status = os.system(command)
            os.chdir(src_dir)
            cmd = "chmod +x scrub_cc.go"
            print(cmd)
            os.system(cmd)
            if status != 0:
                print("copy of scrub_cc script to src dir failed")
            else:
                print("Copy command is successful : %s " % command)
                
            if copy_from_main:
                for file in [sonar_properties_file,dockerfile_test,golint_file, run_sonar_sh]:
                    cmd_checkout_file = "git checkout origin/main -- %s" % (file)
                    print(cmd_checkout_file)
                    status = os.system(cmd_checkout_file)
                    if status != 0:
                        print("Fail to copy %s from base branch" % file)

            sonar_properties_content = []
            sonar_properties_content.append("")
            sonar_properties_content.append("sonar.login=%s" % os.environ["SONAR_TOKEN"])
            sonar_properties_content.append("sonar.scm.disabled=True")

            # read the sonar properties
            sonar_properties_content = read_file(sonar_properties_file)
            if not sonar_properties_content:
                sa_description = description = "sonar_properties_file is missing. plz contact releng"
                sa_state = state = "failure"
            else:
                sonar_project = None
                sonar_coverage_exclusions_list,sonar_exclusion = get_code_coverage_excluded_packages(sonar_properties_file)
                for line_no,line in enumerate(sonar_properties_content):
                    if re.search("projectKey",line):
                        sonar_project=line.split("=")[1]
                        break

                if not sonar_project:
                    sa_description = description = "sonar_project is missing in sonar_properties_file. plz contact releng"
                    sa_state = state = "failure"
                else:
                    print("sonar project key is %s" % sonar_project)
                    print("=" * 50)

                    # Delete PR branch in sonar
                    sonar_branch_delete_status = delete_sonar_pr_branch(sonar_project, pr_number)
                    if not sonar_branch_delete_status:
                        print("Warning: Unable to delete PR branch in sonar")
                    else:
                        print("PR branch in sonar is deleted")

                    # find the packages which are modified as a part of pr
                    print("go files modified as a part of pr")
                    print(go_file_list)
                    print("Number of GO files changed: %s" % len(go_file_list))
                    print("Files which needs to be excluded : %s" % sonar_coverage_exclusions_list)

                    final_go_file_list = []
                    final_go_file_list.extend(go_file_list)
                    # find which all files can be excluded.
                    for go_file in go_file_list:
                        print("Checking for file : %s " % go_file)
                        for excluded_pattern in sonar_coverage_exclusions_list:
                            # *_test.go files are excluded from sonar code report but a change in it will call for Test run
                            if excluded_pattern == "**/*_test.go":
                                continue
                            excluded_pattern = excluded_pattern.replace("*","")
                            if re.search(excluded_pattern, go_file):
                                print("Found the pattern : %s in file %s" % (excluded_pattern, go_file))
                                if go_file in final_go_file_list:
                                    final_go_file_list.remove(go_file)
                    test_regex = []
                    test_regex_package =[]
                    package_list_tmp = []
                    package_list = []

                    print("=" * 50)
                    print("Files which will be considered for testing final_go_file_list : %s" % final_go_file_list)
                    for file in final_go_file_list:
                        file_name = file.split("/")[-1].split(".go")[0].split("_test")[0]
                        test_file = "Test" + file_name[0].upper() + file_name[1:] + "[0-9].*_"
                        test_regex.append(test_file)
                        content = read_file(clone_dir+"/"+file)
                        package_name = None
                        for line in content:
                            if re.search("^package",line):
                                package_name = line.split(" ")[1]
                        if not package_name:
                            print("Package name is missing for this go file : %s" % file)
                            ut_description = sa_description = description = "Package name is missing for this go file : %s" % file
                            sa_state = state = "failure"
                            githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, context)
                            githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, sa_state, target_url, ut_description, ut_context)
                            githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, sa_state, target_url, ut_description, sonar_context)
                            sys.exit(2)

                        package_dir_name = "/".join(file.split("/")[2:-1])
                        # Package name should not have src in the path
                        # example : If the file name is file="images/smf_service/src/smf-service/GenericTest/example/GenericChf_test.go"
                        # package_dir_name should be 'GenericTest/example'
                        print("package_dir_name : %s" % package_dir_name)
                        if package_dir_name.startswith("src"):
                            package_dir_name = "/".join(package_dir_name.split("/")[2:])
                            
                        # Need to harcode for cn-ipam because dir structure is different for cn-ipam.
                        # example : file name is src/ipam/oper.go
                        # package_dir_name should be 'ipam'
                        # RER # 5055
                        if pr_repo == "cn-ipam" and pr_organization == "mobile-cnat-golang-lib":
                            package_dir_name = "/".join(file.split("/")[1:-1])
                        
                        print("package_dir_name : %s" % package_dir_name)

                        if package_dir_name not in package_list_tmp:
                            print("package_dir_name : %s" % package_dir_name)
                            if package_dir_name:
                                package_list_tmp.append(package_dir_name)
                                package_list.append("./"+ package_dir_name+"/...")
                            else:
                                base_file_name = file.split("/")[-1]
                                if base_file_name == "main.go":
                                    print("main.go detected, overriding package_list with './...'")
                                    package_list = ["./..."]
                        test_file_with_package = "Test" +file_name + "_" + package_name + "_"
                        test_regex_package.append(test_file_with_package)

                    if pr_repo == "smf-service":
                        test_regex_package.append("_Cnpgw_Sanity")
                        
                    all_test_packages,all_packages = get_all_packages(src_dir,sonar_properties_file)
                    covr_packages = ",".join(all_test_packages.split(" "))
                    # Remove duplicate values from list
                    test_regex = [*set(test_regex)]
                    test_regex_package = [*set(test_regex_package)]
                    if final_go_file_list or java_file_list:
                        if  len(java_file_list)>0 and len(final_go_file_list)==0:
                            sonar_language = "java"
                        elif len(final_go_file_list)>0 and len(java_file_list)>0:
                            sonar_language = "all"
                        else:
                            sonar_language = "go" 

                        packages_str = ",".join(package_list)
                        packages_space = " ".join(package_list)
                        test_regex_str = "|".join (test_regex)
                        test_regex_package_str = "|".join(test_regex_package)

                        print("Packages modified as a part of pr : %s" % packages_str)
                        print("Argument for coverpkg command: %s" % packages_space)
                        print("Test which will be executed will be : %s" % test_regex_str)

                        # update sonar properties
                        print("updating sonar-project.properties")
                        sonar_properties_content.append("sonar.pullrequest.key=%s" % pr_number)
                        sonar_properties_content.append("sonar.pullrequest.branch=%s" % pr_from_branch)
                        sonar_properties_content.append("sonar.pullrequest.base=%s" % pr_to_branch )  
                        if sonar_exclusion is None:
                            if sonar_language == "go":
                                sonar_properties_content.append("sonar.exclusions=**/update_gosec_severity.py,**/scrub_cc.go,scrub_cc.go,**/*.java,**/*_test.go,**/*.pb.go,**/*.html,**/*.json,**/*.xml,**/*.txt,**/*.yaml,**/*.json,**/*.yml,**/test/**")
                            else:
                                sonar_properties_content.append("sonar.exclusions=**/update_gosec_severity.py,**/scrub_cc.go,scrub_cc.go,**/*_test.go,**/*.pb.go,**/*.html,**/*.json,**/*.xml,**/*.txt,**/*.yaml,**/*.json,**/*.yml,**/test/**")
                        else:
                            if sonar_language == "go":
                                sonar_properties_content.append("sonar.exclusions=**update_gosec_severity.py,**/scrub_cc.go,scrub_cc.go,**/*.java,**/*_test.go,**/*.pb.go,**/*.html,**/*.json,**/*.xml,**/*.txt,**/*.yaml,**/*.json,**/*.yml,**/test/**,%s" %sonar_exclusion)
                            else:
                                 sonar_properties_content.append("sonar.exclusions=**/update_gosec_severity.py,**/scrub_cc.go,scrub_cc.go,**/*_test.go,**/*.pb.go,**/*.html,**/*.json,**/*.xml,**/*.txt,**/*.yaml,**/*.json,**/*.yml,**/test/**,%s" %sonar_exclusion)
                               
                        append_to_file(sonar_properties_content,sonar_properties_file) 

                        print("Variables")
                        print("TEST_REGEX_PACKAGE = (?i)(%s)" % test_regex_package_str )
                        print("TEST_REGEX = (?i)(%s)" % test_regex_str)
                        print("PACKAGE_STRING = %s" % packages_str)
                        print("PACKAGE_LIST = %s" % packages_space)
                        print("executing the command")
                        image_tag = pr_repo + ":" + pr_head_sha

                        command = (
                            "docker build -t {image_tag} -f Dockerfile.sonar . --no-cache ").format(image_tag=image_tag)

                        print(command)
                        os.chdir(images_dir)
                        status = os.system(command)
                        if status != 0:
                            print("docker build failed")
                            delete_image(image_tag)
                            description = "test execution failed. plz check jenkins console."
                            sa_description = "docker build or test execution failure.check jenkins console."
                            sa_state = state = "failure"
                            exit_status_on_failure = 2
                        else:
                            # check if the repo is part of repos_admin_cmd
                            if pr_repo in repos_admin_cmd:
                                # execute docker run command
                                command = "docker run --privileged --cap-add=NET_ADMIN -v /lib/modules:/lib/modules --device /dev/net/tun:/dev/net/tun "
                                if pr_repo == 'ebpf-infra':
                                    if num_of_mount and source_loc_split and target_loc_split:
                                        for i in range(0,int(num_of_mount[0])):
                                            command += "--mount type=bind,source="
                                            command += "%s,target=%s " %(source_loc_split[i],target_loc_split[i])
                                command += "-i %s " % image_tag
                                command += " %s %s true" % (os.environ["USERNAME"],os.environ["FULL_TOKEN"])
                                command += " \"%s\"" % (packages_space)
                                command += " \"(?i)(%s)\"" % (test_regex_str)
                                command += " \"%s\"" % (all_packages)
                                command += " %s %s %s" % (os.environ["ARTIFACTORY_USER"],os.environ["ARTIFACTORY_PASSWORD"],artifactory_pr_upload_url)
                                # All test packages
                                command += " \"%s\"" % (all_test_packages)
                                command += " \"%s\"" % (packages_str)
                                command += " \"(?i)(%s)\"" % (test_regex_package_str)
                                command += " \"%s\"" % (covr_packages)
                                command += " \"%s\"" % (sonar_language)
                                print(command)
                                status = os.system(command)
                                if status != 0:
                                    print("docker run failed")
                                    description = "test execution failed. plz check jenkins console."
                                    sa_description = "docker build or test execution failure.check jenkins console."
                                    sa_state = state = "failure"
                                    exit_status_on_failure = 2
                            #Download PR coverage reports for jenkins junit tests
                            delete_image(image_tag)
                            if not download_from_artifactory(artifactory_pr_download_dir,workspace):
                                print("Download from artifactory failed")
                                sys.exit(1)

                            # Check if any tests have failed
                            result_file_path=artifactory_file_ws_loc + "/result.txt"
                            if os.path.exists(result_file_path):
                                test_result = parse_result(result_file_path)
                                if "failed" in test_result:
                                    failed_test = test_result["failed"]
                                elif "failure" in test_result:
                                    failed_test = test_result["failure"]
                                else:
                                    failed_test = 0
                                if "errors" in test_result:
                                    error_test = test_result["errors"]
                                else:
                                    error_test = 0
                                if int(failed_test) > 0:
                                    ut_description = "%s tests have failed. Plz check on details to check on test report" % test_result["failed"]
                                    ut_state = state = "failure"
                                    ut_target_url = os.environ['BUILD_URL'] + "/testReport"
                                    exit_status_on_failure = 2
                                elif int(error_test) > 0:
                                    ut_description = "%s tests have error. Plz check on console output" % test_result["errors"]
                                    ut_state = state = "failure"
                                    ut_target_url = os.environ['BUILD_URL']
                                    exit_status_on_failure = 2
                                else:
                                    ut_state="success"
                                    ut_description = "all tests have passed"
                                    ut_target_url = os.environ['BUILD_URL'] + "/testReport"
                            else:
                                ut_state="failure"
                                ut_description = "result.txt does not exist to process test result"
                                ut_target_url = os.environ['BUILD_URL'] + "/testReport"
                                exit_status_on_failure = 2
                            status, sonar_link, sa_issues, bugs, vulnerabilities, codeSmells, issues_sonar_link,cc_value = sonar_check(sonar_project, pr_from_branch, "main", pr_number, workspace)
                            if pr_repo in repos_cc_threshold_90:
                                cc_threshold = "90%"
                            else:
                                cc_threshold = "80%"
                            if status:
                                target_url = sonar_link
                                if cc_value == "NA" or sonar_language == "java":
                                    description = "New Code Coverage(UT) NA"
                                    ut_description = "Unit Test to be skipped for files changed"
                                    state = ut_state = "success"
                                else:
                                    if float(cc_value) >= float(cc_threshold.rstrip('%')):
                                        description = "New Code Coverage(UT) is %s%% ( more than threshold of %s )" % (cc_value,cc_threshold)
                                        state = "success"
                                    else:
                                        description = "New Code Coverage(UT) is %s%% ( less than threshold of %s )" % (cc_value,cc_threshold)
                                        state = "failure"
                            else:
                                if sonar_link:
                                    if sonar_language == "java": 
                                        description = "New Code Coverage(UT) NA"
                                        ut_description = "Unit Test to be skipped for files changed"
                                        state = ut_state = "success"
                                    else:    
                                        message = "Code Coverage Check Failed. [Jenkins console logs](%s/testReport/) [Sonar](%s)" % (os.environ['BUILD_URL'],sonar_link)
                                        githubobj.add_pr_comment(pr_organization, pr_repo, pr_number,message)
                                        target_url = sonar_link
                                        if cc_value == "NA":
                                            description = "New Code Coverage(UT) NA"
                                        else:
                                            description = "New Code Coverage(UT) is %s%% ( less than threshold of %s )" % (cc_value,cc_threshold)
                                            state = "failure"
                                else:
                                    sa_description = description = "Sonar Analysis failed. Plz retry <NAME_EMAIL>"
                                sa_state = "failure"
                                exit_status_on_failure = 2
                            if sa_issues == 0 or sa_issues == "0":
                                target_url = sonar_link 
                                sa_description = "Static analysis is clean"
                                sa_state = "success"
                            else:
                                print("sa_issues:" + str(sa_issues))
                                if sonar_link:
                                    target_url = issues_sonar_link
                                    sa_description = "%s new static analysis warnings found." % (sa_issues)
                                else:
                                    sa_description = "Sonar Analysis failed. Plz retry <NAME_EMAIL>"
                                sa_state = "failure"
                                exit_status_on_failure = 2
                            if int(bugs) != 0 or int(vulnerabilities) != 0 or int(codeSmells) != 0:
                                sonar_description = "Sonar check failed: Bugs:%s | Vulnerabilities:%s | Code Smells:%s" % (bugs, vulnerabilities, codeSmells)
                                sonar_state = "failure"
                            else:
                                sonar_description = "Sonar check passed: Bugs:%s | Vulnerabilities:%s | Code Smells:%s" % (bugs, vulnerabilities, codeSmells)
                                sonar_state = "success"
                    else:
                        description = "Code Coverage(UT) needs to be skipped for files changed"
                        sonar_state = sa_state = state = ut_state = "success"
                        sa_description = "Static Analysis check to be skipped for files changed"
                        ut_description = "Unit Test to be skipped for files changed"
                        sonar_description = "Sonar needs to be skipped for files changed"

        print("Code coverage details: %s, %s" % (description, state))
        print("Static Analysis details: %s, %s" % (sa_description, sa_state))
        print("Unit Test details: %s, %s" % (ut_description, ut_state))
        print("Sonar details: %s, %s" % (sonar_description, sonar_state))
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, state, target_url, description, context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, ut_state, ut_target_url, ut_description, ut_context)
        githubobj.update_pr_check(pr_organization, pr_repo, pr_head_sha, sonar_state, target_url, sonar_description, sonar_context)
        
        print("\nVerifying the files modified in the pull request with those identified as new in Sonar:")
        try:
            sonar_new_files = get_sonar_pr_file_list(pr_repo, pr_number)
        except Exception as e:
            print("An error occurred while getting the PR file list from SonarQube: %s" % e)
            sys.exit(2)
        
        sonar_exclude_list = ['**/*_test.go','**/*.pb.go','**/*.html','**/*.json','**/*.xml','**/*.txt','**/*.yaml','**/*.yml']
        if sonar_exclusion is None:
            combined_sonar_exclude_list = sonar_exclude_list
        else:
            sonar_exclusion = sonar_exclusion.split(',')
            combined_sonar_exclude_list = list(set(sonar_exclusion + sonar_exclude_list))
        
        if sonar_new_files:
            filtered_pr_new_files = [
                file for file in file_list
                if not is_file_excluded(file, combined_sonar_exclude_list)
            ]
            print("Sonar exclusion:" + str(combined_sonar_exclude_list))
            print("Files modified as a part of PR:" + str(file_list))
            print("Files modified in the PR excluding Sonar exclusions:" + str(filtered_pr_new_files))

            extra_files_in_sonar = []
            for file in sonar_new_files:
                found = False
                for pr_file in filtered_pr_new_files:
                    if file in pr_file:
                        found = True
                        break
                if not found:
                    extra_files_in_sonar.append(file)

            print("Extra files in sonar:" + str(extra_files_in_sonar))

            if not extra_files_in_sonar:
                print("\nAll files in sonar new file list are present in PR. Continuing...")
            else:
                print("\nNot all files of sonar new file list are present in PR. Triggering sonar branch run on base branch %s" %pr_to_branch)
                jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])
                job_name = "Developer_Jobs/code_coverage_branch_admin"
                parameter = {"ORGANIZATION" : pr_organization,
                             "REPO" : pr_repo,
                             "BRANCH_NAME" : pr_to_branch,
                             "RELENG_BRANCH_NAME": os.environ["RELENG_BRANCH_NAME"]}
                print("job_name : %s" % job_name)
                print("parameter : %s" % parameter)
                build_url = jenkins_obj.trigger_build_params(job_name,parameter)
                if build_url != "Aborted":
                    build_number = build_url.split('/')[-2]
                    print("Build triggered to update base branch %s in sonar: %s" % (pr_to_branch,build_url))
                    print("\nOnce the base branch's sonar analsyis is done, please retrigger PR check.")
                    message = "Few files in the new Sonar file list are not part of the PR.\
                        This discrepancy might be due to the base branch in Sonar being outdated.\
                            To resolve this, a Sonar analysis has been initiated to update the base branch [Jenkins log](%s).\
                                Once the base branch sonar analsyis is done, please retrigger PR check." %(build_url)
                    githubobj.add_pr_comment(pr_organization, pr_repo, pr_number,message)

                elif not build_url:
                    print("Failed to trigger build to update base branch in sonar %s : %s" % (pr_to_branch,build_url))
        if state == "failure" or sa_state == "failure":
            sys.exit(exit_status_on_failure)
        

if __name__ == "__main__":
    sys.exit(main())
