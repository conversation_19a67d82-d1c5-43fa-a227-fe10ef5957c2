# author  : <PERSON><PERSON><PERSON>
# Flow of the script
# input : product & branch name as a parameter
# This job will be schedule to tirgger independently every night or twice daily
# Flow of Script
# For a given product -> get the list of github orgs
# 1. Check if this org & branch is supported for sonar
# 2. If yes, get the list of repos which are supported
# 3. Loop through the list of repos
# 3.a. find the HEAD SHA of org/repo/branch from commit_info.json
# 3.b. If the HEAD SHA already exists in the DB -> get all the fields
# 3.c. If the date is same as today's date -> no action, but if the date is different -> create an entry for today's date with same data


import sys
import os
import argparse

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

sonar_checks_ini = BASE_DIR + "/libs/sonar_checks.ini"

from GithubApi import GithubApi
import Constants as Constants
from sonar_utils import check_code_coverage_support, get_image_dir_path, execute_test_on_branch,download_from_artifactory,parse_result,list_supported_repos_branches
from Utils import *
from version_info import version_info
from datetime import datetime
from cnjenkins import CNJenkins

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('-p', '--product', required=False, help="product")
    parser.add_argument('-branch', '--branch', required=False, help="branch")
    parser.add_argument('-db', '--db_env', required=True, help="pick the db instance, dev or prod")
    args = parser.parse_args()

    # read inputs
    product = args.product
    branch = args.branch
    db_env = args.db_env


    table = "sonar"
    report_date = datetime.now().date()
    job_name = "Developer_Jobs/code_coverage_branch"

    githubobj = GithubApi()
    version_info_obj = version_info(config_section_name=db_env)
    dbo = version_info_obj.open_connection()
    jenkins_obj = CNJenkins(Constants.CN_JENKINS_SERVER, os.environ["SJC_JENKINS_USERNAME"], token=os.environ["SJC_JENKINS_TOKEN"])

    # get list of org which makes a product.
    product_org_list = Utils.get_product_orgs(product, "0")
    for organization in product_org_list:
        print("=" * 50)
        print(organization)
        print("=" * 50)
        supported_repos_list, supported_branches_list = list_supported_repos_branches(organization)
        if not supported_branches_list:
            print("%s org is not supported for sonar" % organization)
            #continue
        # Check if branch is supported by the org.
        if not branch in supported_branches_list:
            print("%s branch is not supported for this %s" % (organization, branch))
            #continue
        # If branch is suported, loop through the list of repos supported.
        for repo in supported_repos_list:
            # get the head sha of the repo
            print("=" * 50)
            head_sha = githubobj.read_head_sha(organization, repo, branch)
            print("=" * 50)
            # Check if information about this head sha is already present in DB
            conditions = { "ORGANIZATION" : organization,
                           "REPOSITORY" : repo,
                           "BRANCH" : branch,
                           "HEAD_SHA" : head_sha}
            return_value = dbo.run_select_query(table, conditions)
            print(return_value)
            if return_value:
                found = None
                print("Data exists for this HEAD SHA")
                print("Checking if data exist for same date %s" % report_date)
                for data in return_value:
                    if report_date == data["REPORT_TIME"].date():
                        print("Data exist for today's date")
                        found = True
                        break
                if not found:
                    print("Updating old data with new date")
                    data = return_value[0]
                    del data["ID"]
                    data["REPORT_TIME"] = datetime.now()
                    # this is to remove keys which have None value 
                    remove_keys = []
                    for key,value in data.items():
                        if value == 0:
                            continue
                        elif not value:
                            remove_keys.append(key)
                    for key in remove_keys:
                        del data[key]
                    dbo.insert_table(table, data)
            else:
                print("Trigger code coverage build for the head sha")
                parameter = {"ORGANIZATION" : organization,
                             "REPO" : repo,
                             "BRANCH_NAME" : branch}
                build_url = jenkins_obj.build_job(job_name,parameter)
                if not build_url:
                    print("Build got aborted : %s" % build_url)
                else:
                    print("Triggered : %s" % build_url)

if __name__ == "__main__":
    sys.exit(main())