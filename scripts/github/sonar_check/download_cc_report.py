"""
 Copyright (c) 2023, Cisco Systems Inc.
 author: s<PERSON><PERSON><PERSON> that can be used to :
    1. Download code coverage reports from automation jenkins NFS location or artifactory(for UT)
    2. Extract the coverage reports (if it is in archived format)
    3. Update report permissions to 755(775-> temporary permission) once the reports upload is done

    Future enhancement:
    1. Upload code coverage reports to artifactory 

    
"""

import sys
import os

THIS_FILE = __file__
THIS_DIR = os.path.abspath(os.path.dirname(THIS_FILE))
PARENT_DIR = os.path.abspath(os.path.dirname(THIS_DIR))
GRAND_PARENT_DIR = os.path.abspath(os.path.dirname(PARENT_DIR))
BASE_DIR = os.path.abspath(os.path.dirname(GRAND_PARENT_DIR))
sys.path.append(BASE_DIR)
sys.path.append(BASE_DIR + "/libs")

import Constants as Constants
import argparse
from common import run
import shutil
import zipfile
import tarfile
import fnmatch
from sonar_utils import *
from version_info import version_info
from libs.common import compare_sha_local_and_artifactory, calculate_sha256, calculate_sha_artifactory

'''
# This is a future enhancement.
# This function uploads code coverage reports to artifactory 

def upload_cc_report(artifactory_upload_path,cc_report):
    try:
        print("\nUploading code coverage reports to artifactory..")
        command = '/auto/mitg-sw/tools/bin/jfrog rt upload {} "{}" --flat=false --recursive=false --url={} --user={} --password={}'.format(cc_report, artifactory_upload_path, Constants.ARTIFACTORY_URL, Constants.ARTIFACTORY_USER, Constants.ARTIFACTORY_PASSWORD)
        outLog, errLog,status = run(command)
        print("Outlog : %s" % outLog)
        if status == 0:
            print("\nSuccessfully uploaded cc report : %s/%s" % (Constants.ARTIFACTORY_URL,artifactory_upload_path))
        else:
            print("Error Log : %s" % errLog)
    except Exception as e:
        print("cc report upload failed for %s" % cc_report)
        print("exception is %s" % e)
        sys.exit(1)
'''

def download_cc_report(archive_dir,report_path,report_path_2):
    print("Downloading code coverage reports:\n")
    mask = 2
    old_mask = os.umask(mask)
    if not os.path.exists(archive_dir):
        os.makedirs(archive_dir, mode=0o2775)    
    os.chdir(archive_dir)   
    try:
        command = "wget -q -r -np -nH " + report_path
        print(command)
        return_code = os.system(command)
        command_2 = "wget -q -r -np -nH " + report_path_2
        print(command_2)
        return_code_2 = os.system(command_2)
        if return_code == 0 or return_code_2 ==0 :
            print("Reports downloaded successfully.")
        else:
            print("Error downloading code coverage reports." + return_code)
            if os.path.exists(archive_dir):
                print("Removing archiving directory: %s as download of code coverage report failed" % archive_dir)
                shutil.rmtree(archive_dir)
            sys.exit(1)
    except Exception as e:
        print("An error occurred while downloading code coverage reports:%s" %e)
        if os.path.exists(archive_dir):
            print("Removing archiving directory: %s as download of code coverage report failed" % archive_dir)
            shutil.rmtree(archive_dir)
        sys.exit(1)
 
    print("Extracting code coverage reports to %s"%(archive_dir))        
    try:
        for path, dirs, files in os.walk("."):
            for filename in (fnmatch.filter(files, '*.tgz') or fnmatch.filter(files, '*.tar.gz')):
                tgz_path = os.path.join(path, filename)
                with tarfile.open(tgz_path, 'r:gz') as tar_obj:
                    tar_obj.extractall(path)
                print("Extracted" + tgz_path)  
        print("\nSuccessfully copied and extraced coverage reports to %s" %(archive_dir))
    except Exception as e:
        print("Failed to copy code coverage report %s" %e)
        sys.exit(1)

    print("Delete unwanted backup folders")
    for root, dirs, files in os.walk(archive_dir):
        if "backup" in dirs:
            backup_path = os.path.join(root, "backup")
            try:
                shutil.rmtree(backup_path)  # Delete the folder and its contents
                print("Deleted folder: %s" %backup_path)
            except Exception as e:
                print("Error deleting folder %s:%s "%(backup_path,e))    
    os.chmod(archive_dir,0o755)
    os.umask(old_mask)

def download_ut_report(artifactory_download_path,archive_dir,ut_report):
    mask = 2
    old_mask = os.umask(mask)
    if not os.path.exists(archive_dir):
        os.makedirs(archive_dir, mode=0o2775) 
    print("Downloading ut code coverage report:" + artifactory_download_path)
    sha256sumartifactory = calculate_sha_artifactory(Constants.ARTIFACTORY_URL + '/' + artifactory_download_path, "sha256")
    print(f"SHA256 checksum on artifactory for UT Report {artifactory_download_path} is {sha256sumartifactory}")
    command="jfrog rt dl --flat=true --user=%s --password=%s --url=%s %s %s/%s" % (Constants.ARTIFACTORY_USER,Constants.ARTIFACTORY_PASSWORD,Constants.ARTIFACTORY_URL,artifactory_download_path,archive_dir,ut_report)
    print(command)
    try:
        outLog, errLog,status = run(command)
        print("Outlog : %s" % outLog)
        if status != 0:
            print("Failed to download ut coverage report. Error Log :\n %s" % errLog)
        print("\nSuccessfully downloaded UT code coverage report")
        sha256local = calculate_sha256(archive_dir+'/'+ut_report)
        print(f"SHA256 checksum on local for UT Report {archive_dir}/{ut_report} is {sha256local}")
        result = compare_sha_local_and_artifactory(sha256local, sha256sumartifactory)
        if result == 0:
            print(f"SHA256 checksum on artifactory and calculated are equal for UT Report {artifactory_download_path} which is {sha256local}")
        else:
            print(f"SHA256 checksum on artifactory and calculated are different for UT Report {artifactory_download_path} local sha is {sha256local} and artifactory sha is {sha256sumartifactory}")
            sys.exit(1)
        return True 
    except Exception as e:
        print("ut report download failed")
        print("error log:\n %s"%e)
        sys.exit(1)
    os.chmod(archive_dir,0o755)
    os.umask(old_mask)    

def main():

    parser = argparse.ArgumentParser(description='Script to upload code coverage reports to artifactory and NFS mount point')
    parser.add_argument('-p','--product', required=True, help = 'Product name')
    parser.add_argument('-b', '--branch', required=True, help='Branch name')
    parser.add_argument('-t','--test_type', required=True, help = 'Test type')
    parser.add_argument('-bn','--build_number', required=True, help = 'QA build number')
    parser.add_argument('-o', '--org', default=None, help='Organization name')
    parser.add_argument('-r','--repo', default=None, help = 'Repo name')
    parser.add_argument('-d','--coverage_folder', default=None, help = 'Coverage report location')
    parser.add_argument('-db', '--db_name', default='5g-prod', help='database name')

    try:
        args = parser.parse_args()
    except Exception as e:
        return e

    running_node = os.uname()[1]
    if running_node in Constants.HOS_HOSTS:
        CC_NFS_MOUNT = Constants.HOS_CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.HOS_TEST_CC_NFS_MOUNT
    else:
        CC_NFS_MOUNT = Constants.CC_NFS_MOUNT
        TEST_CC_NFS_MOUNT = Constants.TEST_CC_NFS_MOUNT

    branch = args.branch
    product = args.product.lower()
    if product == 'cnsgw':
        product = 'sgw'
    test_type = args.test_type
    build_number = args.build_number 
    if args.coverage_folder:
        coverage_folder = args.coverage_folder
    else:
        coverage_folder = "" 
    if args.org:
        org = args.org
    else:
        org = ""
    if args.repo:
        repo = args.repo
    else:
        repo = ""                   
    workspace = os.getcwd()
    if args.db_name:
        config_section_name = args.db_name
    else:
        config_section_name = "5g-prod"
    version_info_obj = version_info(config_section_name=config_section_name)
    dbo = version_info_obj.open_connection()
    if product == 'cnpgw':
        subproduct = product
        product = 'smf'
        branch = get_cc_branch(build_number,dbo)
        archive_dir = "%s/%s/%s/%s/%s/%s/"%(CC_NFS_MOUNT,branch,product,build_number,test_type,subproduct)
    elif product == 'smf':
        branch = get_cc_branch(build_number,dbo)
        archive_dir = "%s/%s/%s/%s/%s/"%(CC_NFS_MOUNT,branch,product,build_number,test_type)
    else:
        archive_dir = "%s/%s/%s/%s/%s/"%(CC_NFS_MOUNT,branch,product,build_number,test_type)
    if test_type == "UT":
        if not (org or repo):
            print("orgnization and repo name parameters are mandatory if test_type is UT ")
            sys.exit(1)
        ut_report = repo+"_coverage.out"    
        artifactory_download_path = "mobile-cnat-charts-release/%s/%s/%s/coverage.out"%(args.org,repo,branch)
        download_ut_report(artifactory_download_path,archive_dir,ut_report)
    else:   
        if not coverage_folder:
            print("%s is required for FT/ST test types"%coverage_folder)
            sys.exit(1)
        else:    
            report_path = Constants.AUTOMATION_CC_ENDPOINT + coverage_folder
            report_path_2 = Constants.AUTOMATION_CC_ENDPOINT_2 + coverage_folder
            download_cc_report(archive_dir,report_path,report_path_2)
            softlink_creation(branch,product,build_number,mount_path=CC_NFS_MOUNT)

    #upload code coverage reports to artifactory
    #artifactory_upload_path = "mobile-cnat-charts-release/code_coverage/%s/%s/%s/%s"%(branch,product,test_type,cc_report)
    ##upload_cc_report(artifactory_upload_path,cc_report)    

if __name__ == "__main__":
    sys.exit(main())
