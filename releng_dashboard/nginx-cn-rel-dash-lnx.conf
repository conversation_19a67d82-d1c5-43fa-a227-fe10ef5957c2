events { worker_connections 1024;}

http {
    upstream cn-rel-dash-lnx.cisco.com {
        server releng_dashboard-web-1:80;
        server releng_dashboard-web-2:80;
        server releng_dashboard-web-3:80;
    }


    server {
        listen 443 ssl;
        server_name cn-rel-dash-lnx.cisco.com;
        ssl_certificate /etc/nginx/certs/cert.cer;
        ssl_certificate_key /etc/nginx/certs/pvt.key;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;

        location / {
            #include proxy_params;
            proxy_pass https://cn-rel-dash-lnx.cisco.com;
            proxy_http_version 1.1;
            proxy_set_header Connection "keep-alive";
            proxy_connect_timeout 500s;
            proxy_send_timeout 500s;
            proxy_read_timeout 500s;
            fastcgi_send_timeout 500s;
            fastcgi_read_timeout 500s;
        }
    }
}
