#!/bin/bash
# kirubs, Jul 24, 2021
# Build image if build option passed
# Run monitoring and remediate container to check whether all Dasboard web containers are up 
# Return exit code 

usage() {
	echo "Usage: $0 [build]"
	exit $1
}

DIR=`dirname $0`
if [ $# -ge 1 ]; then
	if [ $1 == '-h' ]; then
		usage 0
	fi
	if [ $1 == 'build' ]; then
		cd "${DIR}/docker-monit"
		docker build . --tag dashboard_monit:v1
		if [ $? -ne 0 ]; then
			echo "Error: Build failed"
			exit 1
		fi
	else 
		usage 1
	fi
fi

set -x
cd ${DIR}
docker run --network=releng_dashboard_default -v /var/run/docker.sock:/var/run/docker.sock -v /Users/<USER>/Box\ Sync/work/docker-monit/releng-tools/releng_dashboard/:/opt/releng_dashboard/ dashboard_monit:v1
exit $?
