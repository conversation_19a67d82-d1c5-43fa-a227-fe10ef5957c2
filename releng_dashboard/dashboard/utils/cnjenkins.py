#! /usr/bin/env python3
#
# Jenkins library for 5G Rel Eng Dashboard
#
# kirubs, February 2020, Initial Draft
#
#

'''
Library for intracting with Jenkins server.
Trigger a build, get build results, etc
'''

import jenkins
from os import environ
from pprint import pprint
import re
from dashboard.utils.common import get_content

class CNJenkins:
    def __init__(self, server_url=environ.get('JENKINS_SERVER'), username=environ.get('JENKINS_USERNAME'), token=environ.get('JENKINS_TOKEN')):

        self.server_url = server_url
        self.username = username
        try:
            self.server = jenkins.<PERSON>(server_url, username, token)
            if re.search("smi", server_url):
                self.server._session.verify = False
            user = self.server.get_whoami()
            pprint("You have logged in as %s. " % user['fullName'])
        except Exception as e:
            self.server = None
            pprint("Unable to login to Jenkins host URL : %s" % server_url)
            pprint("Error: %s" % e)


    def build_job(self, job_name, params=None):
        pprint("Calling job: %s" % job_name)
        pprint("params : %s" % params )
        try:
            if params:
                return self.server.build_job(job_name, params)
            else:
                return self.server.build_job(job_name)
        except Exception as e:
            pprint("Unable to trigger build")
            pprint("Error: %s" % e)
            return None

    def get_build_details(self, job_name, build_num, fname=None):
        '''
        Get build details
        Identify fields
        '''
        build_info = dict()
        try:
            build_data = self.server.get_build_info(job_name, int(build_num))
            #pprint ("build_data= %s" % build_data)

            for i in ['result', 'duration', 'url', 'estimatedDuration', 'timestamp','description']:
                if i in build_data:
                    data = build_data[i]
                    if data:
                        build_info[i] = data
                    else:
                        build_info[i] = None
                else:
                    build_info[i] = None
            # fetch result file from workspace
            if fname:
                fpath=build_info['url'] + "/artifact/" + fname
                r=get_content(fpath, environ.get('JENKINS_USERNAME'), environ.get('JENKINS_TOKEN'))
                build_info['fcontent'] = r
            return (build_info)
        except Exception as e:
            return None


    def get_jobs(self):
        try:
            jobs = self.server.get_jobs()
            return jobs
        except Exception as e:
            return None

if (__name__ == '__main__'):
    # Testing
    #jenkins = CNJenkins('http://localhost:8080/', 'kirubs', 'kirubs')
    #jenkins = CNJenkins('http://localhost:8080/')
    #out=jenkins.build_job('test-job1', {'arg1':'value1', 'arg2':'value2'})

    #print ("Job triggered. queue id: %s" % out)
    obj = CNJenkins('https://engci-private-sjc.cisco.com/jenkins/cn-releng/')
    #print ("Hello")
    #obj.get_last_QABuild()

# Dashboard_QA_Builds

