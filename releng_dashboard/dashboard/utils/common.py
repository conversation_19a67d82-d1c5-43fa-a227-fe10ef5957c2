import os
import re
import subprocess

def read_file(filename):
    """read a file & put in list"""
    with open(filename, 'r') as f:
        content = f.read().splitlines()
    f.close()
    return content

def get_content(url, username, password):
    index = "source.html"
    if type(username) != str or type(password) != str:
        print ("Error: username or password not string")
        return None
    username = username.lstrip()
    password = password.lstrip()
    if os.path.isfile(index):
        os.remove(index)
    if username and password:
        cmd = "curl -u %s:%s -s %s > %s" % (username, password, url, index)
    else:
        print ("Error: username or password cannot be empty")
        return None
    subprocess.call(
        cmd,
        shell=True,
        stderr=subprocess.STDOUT)
    if os.path.isfile(index):
        with open(index, 'r') as f:
            content = f.read()
        f.close()
        os.remove(index)
        print("url=%s and content= %s " % (url,content)) 
        if content:
           return content
        else:
           return None
    else:
        return None


def get_key(dict, val):
    key_list = []
    for key, value in dict.items():
        if val == value:
            key_list.append(key)
    return key_list


'''
    all_packages = soup.findAll("a")
    if all_packages:
        pkg_list = []
        while all_packages:
            item = all_packages.pop()
            temp_pkg_name = item.get_text()
            found = False
            for word in excluded_words:
                if re.search(word, temp_pkg_name):
                    found = True
            if not found:
                pkg_name = temp_pkg_name.split("/")[0]
                pkg_list.append(pkg_name)
        if pkg_list:
            return(pkg_list)
        else:
            return None

 '''

def get_mailer_user_list(mailer):
        user = ['kdhotre','skunjir','psodhia','brik','sagangir','ruluthra','ihendath']
        return user

def get_mailer_dir_list(mailer):
        user = ['atudeshp','banandya','jiabraha','mmanjuan','nchitta','sharadch','sokaki','spanemja','stirunah','subbus','vbisamal','vinv','vivekks']
        return user
