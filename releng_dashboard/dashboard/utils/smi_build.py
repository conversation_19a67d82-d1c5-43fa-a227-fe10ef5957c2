from pprint import pprint
import re
from os import environ

from dashboard.utils.cnjenkins import CNJenkins

import Constants as Constants

PRODUCT_LIST = {"cnee":"mobile-cnat-infrastructure",
                "smf":"mobile-cnat-smf",
                "pcf":"mobile-cnat-policy",
                "cpc":"mobile-cnat-cpc",
                "amf":"mobile-cnat-amf",
                "nrf":"mobile-cnat-nrf",
                "app-infra":"mobile-cnat-app-infra",
                "golang-lib":"mobile-cnat-golang-lib",
                "rcm":"mobile-cnat-rcm",
                "pats":"mobile-cnat-pats",
                "udc":"mobile-cnat-udc",
                "bsf":"mobile-cnat-bsf",
                "pgw":"mobile-cnat-pgw",
                "sgw":"mobile-cnat-sgw",
                "bng":"mobile-cnat-bng",
                "ccg":"mobile-cnat-cn",
                "cnvpc":"mobile-cnat-vpc",
                "sepp":"mobile-cnat-sepp",
                "chf":"mobile-cnat-chf",
                "lfs":"mobile-cnat-tools",
                "dvpc":"mobile-cnat-dvpc",
                "kpm":"mobile-cnat-kpm",
                "ulb":"lbs-apps",
                "lbs-libraries":"lbs-libraries",
                "cni": "cisco-network-insights"}

#from libs.GithubApi import GithubApi

def get_smi_build_details(product):
    SMI_JENKINS_USER = environ.get('SMI_JENKINS_USER')
    SMI_API_TOKEN = environ.get('SMI_JENKINS_TOKEN')
    jenkinsobj = CNJenkins(Constants.SMI_JENKINS_SERVER, SMI_JENKINS_USER, SMI_API_TOKEN)
    if jenkinsobj:
        jobs = jenkinsobj.get_jobs()
        if jobs:
            all_builds = []
            org = PRODUCT_LIST[product]
            for job in jobs:
                pattern = r"%sjob/%s" % (Constants.SMI_JENKINS_SERVER,org)
                if re.match(pattern,job["url"]):
                    if "jobs" in job:
                        for item in job["jobs"]:
                            repo = item["name"]
                            #all_repo_list=githubobj.list_all_repos_names(org)
                            #if repo in all_repo_list:
                            if "jobs" in item:
                                for sub_item in item["jobs"]:
                                    branch = sub_item["name"]
                                    if not re.search("PR", branch):
                                        job_url = "%s/job/%s/job/%s/job/%s" %(Constants.SMI_JENKINS_SERVER,org,repo,branch)
                                        build = {}
                                        build["org"] = org
                                        build["repo"] = repo
                                        build["branch"] = branch
                                        build["job_url"] = job_url
                                        status = sub_item["color"]

                                        if re.search("animation",status):
                                            build["build_status"] = "Running"
                                            build["font"] = "orange"
                                        elif re.search("blue",status):
                                            build["build_status"] = "Successful"
                                            build["font"] = "green"
                                        elif re.search("red",status):
                                            build["build_status"] = "Failed"
                                            build["font"] = "red"
                                        else:
                                            build["build_status"] = "Aborted"
                                            build["font"] = "grey"
                                        all_builds.append(build)
            return(all_builds)
        else:
            return None
    else:
        pprint("Jenkins is not reachable. Check on the server.")
        return None


if __name__ == "__main__":
    get_build_details("mobile-cnat-amf")
