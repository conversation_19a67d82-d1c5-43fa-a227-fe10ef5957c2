from flask import render_template, Blueprint, flash, redirect
from dashboard.models import Releases
from dashboard.build.models import Builds
from .. import cache
from os import environ, getcwd
home = Blueprint('home', __name__)
from git import Repo
import os
import datetime
from dashboard import oidc
from dashboard.models import SANITY_REPORTS
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
import Constants as Constants

grafana_host=os.environ["GRAFANA_HOST"]

@home.route('/home')
@oidc.require_login
def home_page():    
    tests_info,pass_percentage,result_dataset = get_test_results(Constants.onprem_product_list,'main')
    return render_template('home.html',aas_products=Constants.aas_products,result_dataset=result_dataset,branches=Constants.onprem_branch_list,selected_branch='main',tests_info=tests_info,pass_percentage=pass_percentage,grafana_host=grafana_host)

@home.route('/', methods=['GET', 'POST'])
@oidc.require_login
def landing_page():
    tests_info,pass_percentage,result_dataset = get_test_results(Constants.onprem_product_list,'main')
    return render_template('home.html',aas_products=Constants.aas_products,result_dataset=result_dataset,branches=Constants.onprem_branch_list,selected_branch='main',tests_info=tests_info,pass_percentage=pass_percentage,grafana_host=grafana_host)

@home.route('/login', methods=['GET', 'POST'])
@oidc.require_login
def user_login():
    try:
        if oidc.user_loggedin:
            tests_info,pass_percentage,result_dataset = get_test_results(Constants.onprem_product_list,'main')
            return render_template('home.html',aas_products=Constants.aas_products,result_dataset=result_dataset,branches=Constants.onprem_branch_list,selected_branch='main',tests_info=tests_info,pass_percentage=pass_percentage,grafana_host=grafana_host)
    except Exception as e:
        return redirect('/login')

@home.route('/releng')
@oidc.require_login
def releng_home():
    return render_template('releng_home.html')

@home.route('/sanity_all')
@oidc.require_login
def sanity_all():
    return render_template('product_sanity.html', product="All", grafana_host=grafana_host)

@home.route('/ivt_all')
@oidc.require_login
def ivt_all():
    return render_template('product_ivt.html', product="All", grafana_host=grafana_host)

@home.route('/loc_trend')
@oidc.require_login
def loc_trend():
    return render_template('product_loc_trend.html', product="All", grafana_host=grafana_host)

@home.route('/branch/<branch_name>',methods=['GET', 'POST'])
@oidc.require_login
def branch(branch_name):
    # future enhancement : Retrieve branch list for the selected branch from database
    # Replace this with your actual data retrieval logic
    tests_info,pass_percentage,result_dataset = get_test_results(Constants.onprem_product_list,branch_name)
    return render_template('home.html',aas_products=Constants.aas_products,result_dataset=result_dataset,branches=Constants.onprem_branch_list,selected_branch=branch_name,tests_info=tests_info,pass_percentage=pass_percentage,grafana_host=grafana_host)

@home.route('/aas_products/<aas_product>',methods=['GET', 'POST'])
@oidc.require_login
def aas_product(aas_product):
    # future enhancement : Retrieve branch list for the selected branch from database
    # Replace this with your actual data retrieval logic
    #tests_info,pass_percentage = get_aas_test_results('p5g','main')
    tests_info = query_last_builds(Constants.onprem_product_list,Constants.aas_branch)
    st_tests_info = query_aas_last_builds(aas_product)
    return render_template('aas_home.html',aas_product=aas_product.upper(),aas_products=Constants.aas_products,branches=Constants.onprem_branch_list,aas_branch=Constants.aas_branch,tests_info=tests_info,grafana_host=grafana_host,st_tests_info=st_tests_info)

@home.route('/ping')
def welcome():
    return 'Hello World! </BR>'

@home.route('/environment')
@oidc.require_login
def about():
    debug_mode = environ.get("DEBUG_MODE")
    environment = environ.get("ENVIRONMENT")
    qa_job_name = environ.get("QA_BUILD_JOB_NAME")
    devhub_job_name = environ.get("DEVHUB_BUILD_JOB_NAME")
    repo_path = getcwd()
    repo = Repo(repo_path,search_parent_directories=True)
    branch = repo.active_branch
    branch_name = branch.name
    head_sha = repo.head.object.hexsha
    return render_template('env.html', debug_mode=debug_mode, branch_name=branch_name, head_sha=head_sha, environment=environment,qa_job_name=qa_job_name,devhub_job_name=devhub_job_name)


@home.route('/latest_release')
@cache.cached()
@oidc.require_login
def latest_release():
    products = set()
    latest_product_releases_tmp = []
    latest_product_releases = []
    for row in Releases.query.join(Builds, Releases.builds_build_number==Builds.build_number)\
    .add_columns(Releases.builds_products_product_name, Releases.release_number, Builds.branches_branch_name, Releases.dh_offline_url, Releases.dh_online_url, Releases.cco_url, Releases.builds_build_number,Releases.promoted_on,Releases.release_type,Releases.customer)\
    .filter(Releases.builds_build_number==Builds.build_number).filter(Releases.promotion_status=='Passed').order_by(Releases.id.desc()):
        data=dict()
        data['builds_products_product_name'] = row[1]
        data['release_number'] = row[2]
        data['branch_name'] = row[3]
        data['dh_offline_url'] = row[4]
        data['dh_online_url'] = row[5]
        data['cco_url'] = row[6]
        data['builds_build_number'] = row[7]
        data['release_type'] = row[9]
        data['customer'] = row[10]
        if row[8]:
            data['promoted_on'] = datetime.datetime.fromtimestamp(int(row[8])).strftime('%d %b %Y')
        products.add(data['builds_products_product_name'])
        latest_product_releases_tmp.append(data)
    latest_product_releases_tmp = sorted(latest_product_releases_tmp, key=lambda i: i['builds_products_product_name'])
    for product in products:
        print ("PRODUCT::" + product)
        for row in latest_product_releases_tmp:
            if row['builds_products_product_name'] == product:
                print ("MATCH")
                latest_product_releases.append(row)
                break
    latest_product_releases = sorted(latest_product_releases, key=lambda i: i['builds_products_product_name'])
    return render_template('latest_release.html', latest_product_releases=latest_product_releases)

def latest_release_old():
    releases = []
    releases_mobj = Releases()
    builds_mobj = Builds()
    products = []
    latest_product_releases = []
    #from sqlalchemy import inspect
    #insp = inspect(Builds)
    #builds_columns = [c_attr.key for c_attr in insp.mapper.column_attrs]
    for row in releases_mobj.query.filter_by(promotion_status='Passed').order_by(releases_mobj.id.desc()):
        dict = row.__dict__
        releases.append(dict)
        products.append(dict['builds_products_product_name'])

    uniq_products = list(set(products))
    latest_product_releases_tmp = []
    latest_product_releases = []

    for product in uniq_products:
        for row in releases:
            if row['builds_products_product_name'] == product:
                latest_product_releases_tmp.append(row)
                break
    # TODO: Ideally Alchemy session query with join of two table should have been used.
    # As we query branch_name for a limited number of row ie, one query per product. There is not really a performance hit with this approach
    for row in latest_product_releases_tmp:
        build_number = row['builds_build_number']
        for brow in builds_mobj.query.filter_by(build_number=build_number):
            row['branch_name'] = brow.branches_branch_name
        latest_product_releases.append(row)

    latest_product_releases = sorted(latest_product_releases, key=lambda i: i['builds_products_product_name'])
    return render_template('latest_release.html', latest_product_releases=latest_product_releases)

def get_test_results(product_list,branch_name):
    #form = CompareForm()
    test_result = []
    pass_percentage_dic = {}
    pass_percentage = []
    sanity_obj = SANITY_REPORTS()
    for product in product_list:
        sanity = product.upper() + "_SANITY_TAAS"
        regression = product.upper() + "_REGRESSION"
        longevity = product.upper() +"_SYSTEMTEST_BV"
        #if product =='sgw':
        #    ivt = "SMF_IVT"
        #else:
        ivt = product.upper() + "_IVT"
        if product == 'amf':
            regression = "AMF_FT_REGRESSION"
            longevity = "AMF_ST_BV"
        if product == 'sgw':
            regression = "CNSGW_REGRESSION"    
        if product in ['smf', 'sgw', 'pgw']:
            db_product = "ccg"
        else:
            db_product = product
        '''
        # Future enhancement: get branch list from DB
        branch_list = []
        for row in sanity_obj.query.filter_by(products_product_name=product,functional_suite="Total").distinct():
            data = row.__dict__
            branch = data["branch_name"]
            branch_list.append(branch)
        '''
        #branch_list = ['main','rel-2023.03']
        #pass_percentage = []
        test_info = {}
        test_info["sanity_passed_percentage"] = 0
        test_info["reg_passed_percentage"] = 0
        test_info["ivt_passed_percentage"] = 0
        test_info["longevity_passed_percentage"] = 0
        for row in sanity_obj.query.filter_by(products_product_name=db_product,job_type=sanity,branch_name=branch_name,status="COMPLETED",functional_suite="Total").order_by(SANITY_REPORTS.start_time.desc()).limit(1).all():
            data = row.__dict__
            if product == 'ccg':
                test_info["product"] = 'SMF'
            else:    
                test_info["product"] = product.upper()
            test_info["branch"] = branch_name
            test_info["sanity_total_TC"] = data["total_feature_file_count"]
            test_info["sanity_passed_percentage"] = data["passed_percentage"]
            test_info["sanity_passed_case_count"] = data["passed_case_count"]
            test_info["sanity_build_number"] = data["build_number"]
            test_info["sanity_start_time"] = str((data["start_time"]).strftime('%d %b %Y'))

        for row in sanity_obj.query.filter_by(products_product_name=db_product,job_type=regression,branch_name=branch_name,status="COMPLETED",functional_suite="Total").order_by(SANITY_REPORTS.start_time.desc()).limit(1).all():
            data = row.__dict__
            if not data:
                test_info["reg_passed_percentage"] = 0
                continue
            test_info["reg_total_TC"] = data["total_feature_file_count"]
            if data["passed_percentage"]:
                test_info["reg_passed_percentage"] = data["passed_percentage"]
            else:
                test_info["reg_passed_percentage"] = 0
            test_info["reg_passed_case_count"] = data["passed_case_count"]
            test_info["reg_build_number"] = data["build_number"]
            test_info["reg_start_time"] = str((data["start_time"]).strftime('%d %b %Y'))

        for row in sanity_obj.query.filter_by(products_product_name=db_product,job_type=ivt,branch_name=branch_name,status="COMPLETED",functional_suite="Total").order_by(SANITY_REPORTS.start_time.desc()).limit(1).all():
            data = row.__dict__
            if not data:
                test_info["ivt_passed_percentage"] = 0
                continue
            test_info["ivt_total_TC"] = data["total_feature_file_count"]
            if data["passed_percentage"]:
                test_info["ivt_passed_percentage"] = data["passed_percentage"]
            else:
                test_info["ivt_passed_percentage"] = 0
            test_info["ivt_passed_case_count"] = data["passed_case_count"]
            test_info["ivt_build_number"] = data["build_number"]
            test_info["ivt_start_time"] = str((data["start_time"]).strftime('%d %b %Y'))    

        for row in sanity_obj.query.filter_by(products_product_name=db_product,job_type=longevity,branch_name=branch_name,status="COMPLETED",functional_suite="Total").order_by(SANITY_REPORTS.start_time.desc()).limit(1).all():
            data = row.__dict__
            if not data:
                test_info["longevity_passed_percentage"] = 0
                continue
            test_info["longevity_total_TC"] = data["total_feature_file_count"]
            if data["passed_percentage"]:
                test_info["longevity_passed_percentage"] = data["passed_percentage"]
            else:
                test_info["longevity_passed_percentage"] = 0
            test_info["longevity_passed_case_count"] = data["passed_case_count"]
            test_info["longevity_build_number"] = data["build_number"]
            test_info["longevity_start_time"] = str((data["start_time"]).strftime('%d %b %Y'))      
    
        if test_info:
            test_result.append(test_info)
            pass_percentage.append(test_info["sanity_passed_percentage"])
            pass_percentage.append(test_info["reg_passed_percentage"])
            pass_percentage.append(test_info["ivt_passed_percentage"])
            pass_percentage.append(test_info["longevity_passed_percentage"])
            pass_percentage_dic[product] = pass_percentage
        if test_info:
            pass_percentage_dic[product] = [
            test_info.get("sanity_passed_percentage", 0),
            test_info.get("reg_passed_percentage", 0),
            test_info.get("ivt_passed_percentage", 0),
            test_info.get("longevity_passed_percentage", 0),
            ]

        result_dataset = [
            [product.upper()] + pass_percentage_dic[product]
            for product in pass_percentage_dic
        ]
    return(test_result,pass_percentage,result_dataset)

def query_last_builds(product_list, branch, limit=3):
    qa_build_info = []
    build_mobj = Builds()
    sanity_obj = SANITY_REPORTS()
    for product in product_list:
        product_name = product
        sanity = product_name.upper() + "_SANITY_TAAS"
        regression = product_name.upper() + "_REGRESSION"
        if product == 'amf':
            regression = "AMF_FT_REGRESSION"
        if product == 'sgw':
            regression = "CNSGW_REGRESSION"    
        if product in ['smf','sgw','pgw']:
            product = 'ccg'
        results = build_mobj.query.filter_by(products_product_name=product, branches_branch_name=branch).order_by(Builds.id.desc()).limit(limit).all()
        first_row = True
        for row in results:
            test_info = {}
            data = row.__dict__
            test_info["branch"] = branch
            build_number = data["build_number"]
            if first_row:
                test_info["product"] = product_name.upper()
                first_row = False
            # Convert timestamp to a human-readable format
            build_start_time = data["build_online_start_time"]
            if build_start_time:
                test_info["build_start_time"] = datetime.datetime.fromtimestamp(int(data["build_online_start_time"])).strftime('%d %b %Y')

            # Query Sanity data
            sanity_data = sanity_obj.query.filter_by(products_product_name=product, build_number=build_number, job_type=sanity, branch_name=branch, status="COMPLETED", functional_suite="Total").first()
            if sanity_data:
                test_info["sanity_total_TC"] = sanity_data.total_feature_file_count
                test_info["sanity_passed_percentage"] = sanity_data.passed_percentage
            else:
                test_info["sanity_total_TC"] = 0
                test_info["sanity_passed_percentage"] = 0

            # Query Regression data
            regression_data = sanity_obj.query.filter_by(products_product_name=product, build_number=build_number, job_type=regression, branch_name=branch, status="COMPLETED", functional_suite="Total").first()
            if regression_data:
                test_info["reg_total_TC"] = regression_data.total_feature_file_count
                test_info["reg_passed_percentage"] = regression_data.passed_percentage or 0
                test_info["reg_passed_case_count"] = regression_data.passed_case_count
            else:
                test_info["reg_total_TC"] = 0
                test_info["reg_passed_percentage"] = 0
                test_info["reg_passed_case_count"] = 0
            test_info["build_number"] = build_number
            #test_info["build_start_time"] = data["build_online_start_time"]
            qa_build_info.append(test_info)

    return qa_build_info

def query_aas_last_builds(product, limit=3):
    qa_build_info = []
    build_mobj = Builds()
    sanity_obj = SANITY_REPORTS()
    sanity = product.upper() + "_ST_SANITY"
    regression = product.upper() + "_ST_LONGEVITY"

    results = build_mobj.query.filter_by(products_product_name=product).order_by(Builds.id.desc()).limit(limit).all()
    for row in results:
        test_info = {}
        data = row.__dict__
        build_number = data["build_number"]
        test_info["branch"] = data["branches_branch_name"]
        # get NF details, iterate through nf_details and format the output
        nf_details = data["nf_details"].split(',')
        formatted_products = []

        for nf_data in nf_details:
            product_info = nf_data.split('.')
            product_name = product_info[0]
            product_details = '.'.join(product_info[1:])
            formatted_product = product_name+": "+product_details
            formatted_products.append(formatted_product)
        test_info["nf_details"] = formatted_products  

        # Query Sanity data
        sanity_data = sanity_obj.query.filter_by(products_product_name=product, build_number=build_number, job_type=sanity, status="COMPLETED", functional_suite="Total").first()
        if sanity_data:
            test_info["sanity_total_TC"] = sanity_data.total_feature_file_count
            test_info["sanity_passed_percentage"] = sanity_data.passed_percentage
            test_info["sanity_start_time"] = sanity_data.start_time

        # Query Regression data
        regression_data = sanity_obj.query.filter_by(products_product_name=product, build_number=build_number, job_type=regression, status="COMPLETED", functional_suite="Total").first()
        if regression_data:
            test_info["reg_total_TC"] = regression_data.total_feature_file_count
            test_info["reg_passed_percentage"] = regression_data.passed_percentage
            test_info["reg_passed_case_count"] = regression_data.passed_case_count
            test_info["regression_start_time"] = regression_data.start_time
        test_info["build_number"] = build_number
        qa_build_info.append(test_info)

    return qa_build_info
