from dashboard.models import Branches, Products, Organizations, TT_Role, DASHBOARD_ACCESS
from flask import flash
from . import cache
from os import environ
import sys,os
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
import Constants as constants
from GithubApi import GithubApi
from dashboard.utils.common import get_mailer_user_list
from dashboard.build.models import Builds
import re

def get_product_list():
    product_names = constants.PRODUCT_LIST.keys()
    return list(product_names) + constants.SMI_PRODUCTS

@cache.memoize(timeout=3600, unless=environ.get("DEBUG_MODE"))
def get_product_organization(product):
    organization = []
    organizations_obj = Organizations()
    for row in organizations_obj.query.filter_by(product_name=product):
        organization.append(row.__dict__)
    if organization:
        organization_name = organization[0]['organization']
        return organization_name
    else:
        return None

@cache.memoize(timeout=3600, unless=environ.get("DEBUG_MODE"))
def get_product_name(org):
         product = []
         organizations_obj = Organizations()
         for row in organizations_obj.query.filter_by(organization=org):
             product.append(row.__dict__)
         if product:
             product_name = product[0]['product_name']
             return product_name
         else:
             return None

def sort_branches(branch_list):
    #Sort the list : main first -> followed by rel -> followed by tl- -> followed by dev
    if "main" in branch_list:
        branch_names = ['main']
    else:
        branch_names = []
    branch_names_rel = []
    branch_names_tl = []
    branch_names_others = []
    for branch_name in branch_list:
        if branch_name.startswith("rel-"):
            branch_names_rel.append(branch_name)
        elif branch_name.startswith("tl-"):
            branch_names_tl.append(branch_name)
        elif branch_name == "stage":
            branch_names.append(branch_name)
        elif branch_name == "main":
            continue
        else:
            branch_names_others.append(branch_name)

    #Sort the list : main first -> followed by rel -> followed by tl- -> followed by dev
    if branch_names_rel:
        branch_names_rel.sort()
        branch_names.extend(branch_names_rel)
    if branch_names_tl:
        branch_names_tl.sort()
        branch_names.extend(branch_names_tl)
    if branch_names_others:
        branch_names_others.sort()
        branch_names.extend(branch_names_others)
    if branch_names:
        return branch_names

@cache.memoize(timeout=3600, unless=environ.get("DEBUG_MODE"))
def get_branch_list(product):
    branch_list = []
    product_org = get_product_organization(product)
    if product_org:
        branch_obj = Branches()
        for row in branch_obj.query.filter_by(products_organization=product_org):
            data = row.__dict__
            branch_name = data['branch_name']
            if data["branch_status"] not in ['obsolete' , 'stale']:
                branch_list.append(branch_name)
    branch_names = sort_branches(branch_list)
    return branch_names

@cache.memoize(timeout=3600, unless=environ.get("DEBUG_MODE"))
def get_rel_branch_list(product):
    branch_list = []
    product_org = get_product_organization(product)
    if product_org:
        branch_obj = Branches()
        for row in branch_obj.query.filter_by(products_organization=product_org):
            data = row.__dict__
            branch_name = data['branch_name']
            if re.search("rel-", branch_name):
                if data["branch_status"] not in ['obsolete' , 'stale','locked']:
                    branch_list.append(branch_name)
    branch_names = sort_branches(branch_list)
    return branch_names


#@cache.memoize(timeout=3600, unless=environ.get("DEBUG_MODE"))
def get_tt_branch_list(product):
    product_org = get_product_organization(product)
    branch_list = []
    if product_org:
        branch_obj = Branches()
        for row in branch_obj.query.filter_by(products_organization=product_org):
            data = row.__dict__
            if data["branch_status"] in ['throttle-enabled']:
                branch_list.append(data['branch_name'])
    branch_names = sort_branches(branch_list)
    return branch_names


@cache.memoize(unless=environ.get("DEBUG_MODE"))
def get_user_role(username):
    tt_role_obj = TT_Role()
    user_info = []
    role = "viewer"
    for row in tt_role_obj.query.filter_by(role="approver"):
        user_info.append(row.__dict__)
    user_list = []
    for info in user_info:
        user_list.append(info['users_cec_id'])
    if username in user_list:
        role = "admin"

    cn_releng_users=get_mailer_user_list("cn-releng-support")
    if username in cn_releng_users:
        role = "releng"
    return role

def get_dashboard_user_role(product_org, user_name):
    dashboard_access_obj=DASHBOARD_ACCESS()
    result = []
    repo_name = user_role = None
    cn_releng_users=get_mailer_user_list("cn-releng-support")
    cn_releng_users.append("banandya")
    if user_name in cn_releng_users or user_name=="cn-releng-support":
        user_role="RELENG"
        repo_name = "all"
    else:
        for row in dashboard_access_obj.query.filter_by(ORG_NAME=product_org, CEC_ID=user_name):
            result.append(row.__dict__)
        if result:
            user_role = result[0]["ROLE"]
            repo_name = result[0]["REPO_NAME"]
    return user_role, repo_name


def get_dashboard_role_list(product_org, role_list):
    dashboard_access_obj=DASHBOARD_ACCESS()
    user_list = []
    for role in role_list:
        if role == "RELENG":
            user_list.append("cn-releng-support")
        else:
            result = []
            for row in dashboard_access_obj.query.filter_by(ORG_NAME=product_org, ROLE=role):
                result.append(row.__dict__)
            if result:
                for data in result:
                    user_list.append(data["CEC_ID"])
    return user_list


@cache.memoize(unless=environ.get("DEBUG_MODE"))
def list_to_choices(db_list, default=None):
    choices = list()
    if default:
        default = tuple()
        default = ['XX', 'Pick a Build No']
        choices.append(default)
    if db_list:
        for item in db_list:
            tmp = tuple()
            tmp = [item, item]
            choices.append(tmp)
    return choices

def get_product_art_ep(product):
    organization = []
    organizations_obj = Organizations()
    for row in organizations_obj.query.filter_by(product_name=product):
        organization.append(row.__dict__)
    if organization:
        organization_ep = organization[0]['PRODUCT_ARTIFACTORY_EP']
        return organization_ep
    else:
        return None


def check_team(username, product, product_org, team):
    # Check if user is part of the team write or not.
    team_name = product + "-" + team
    if product == 'pcf':
        team_name = "policy" + "-" + team
    if product == 'cnee':
        team_name = "infrastructure" + "-" + team
    if product == 'ccg':
        team_name = "cn" + "-" + team
    if product == 'cnvpc':
        team_name = "vpc" + "-" + team
    if product == 'ulb':
        team_name = "lbs-apps" + "-" + team
    if product == 'lbs-libraries':
        team_name = "lbs-libraries" + "-" + team          
    github_obj = GithubApi()
    perm = github_obj.check_user_in_team(product_org, team_name, username)
    return perm, team_name

def get_list_passed_cc_build(product,branch_name):
    branch_obj = Branches()
    build_obj = Builds()
    build_details={}
    bn_details={}
    result=[]
    cc_branch_list=[]
    if product in ('sgw','smf','pgw'):
        product_org = 'mobile-cnat-cn'
        product_name = 'ccg'
        flash("org %s" % product_org)
    else:
        product_org = get_product_organization(product.lower())
        product_name = product.lower()
    results= branch_obj.query.filter_by(products_organization=product_org,parent_branch=branch_name, branch_type='cc_branch').order_by(Branches.id.desc()).limit(10).all()
    if results:
        for row in results:
            result.append(row.__dict__)
        for data in result:
            cc_branch_list.append(data['branch_name'])
            bn_details[data['branch_name']] = data['parents_branch_point']
            print("parent branch point %s" % bn_details[data['branch_name']])
    else:
        return None,None
    for items in cc_branch_list:
        new_result= build_obj.query.filter_by(products_product_name=product_name,branches_branch_name=items,build_status='Passed',build_type='cc_build')
        for row in new_result:
            data_result = row.__dict__
            if data_result:
                build_details[data_result['build_number']] = data_result['branches_branch_name']
                bn_details[data_result['build_number']] = bn_details[items]
            else:
                return None,None
    print("build information %s %s" % (build_details,bn_details))
    return build_details,bn_details
