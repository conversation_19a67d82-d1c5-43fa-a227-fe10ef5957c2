from flask_wtf import <PERSON>laskForm
from wtforms import StringField, SubmitField, widgets, SelectMultipleField, SelectField, HiddenField, BooleanField, RadioField
from wtforms.validators import DataRequired

def list_to_choices(db_list, default=None):
    choices = list()
    if default:
        default = tuple()
        default = ['XX', 'Pick a Build No']
        choices.append(default)
    if db_list:
        for item in db_list:
            tmp = tuple()
            tmp = [item, item]
            choices.append(tmp)
    return choices

def retrieve_internal_dicts(nested_dict):
    internal_dicts = []
    for value in nested_dict.values():
        if isinstance(value, dict):
            internal_dicts.append(value)
            internal_dicts.extend(retrieve_internal_dicts(value))
    return internal_dicts

def list_to_choices_predecessors(db_dict, default=None):
    choices = list()
    # create a deafult None for no selection of predecessor ID
    if default:
        default = tuple()
        default = ['XX', 'XX', 'XX', 'XX']
        choices.append(default)
    if db_dict:
        choices = retrieve_internal_dicts(db_dict)
    else:
        choices = 'none'
    return choices

class QABuildForm(FlaskForm):
    branches = predecessor_id = []
    branch_name = SelectField('BRANCH_NAME', choices=list_to_choices(branches), default='main')
    build_no = StringField('BUILD_NUMBER',default=' ')
    trigger_corona_scan = BooleanField('trigger_corona_scan' ,default=False)
    predecessor_type = RadioField('toggle',choices=[('predecessor_id_option1','Select from Previous Corona IDs'),
                                                   ('predecessor_id_input_option2','Enter predecessor ID as input')])
    predecessor_id = SelectField('predecessor_id', choices=list_to_choices_predecessors(predecessor_id), default= 'none')
    predecessor_id_input = StringField('predecessor_id_input', default='none')
    users_to_notify = StringField('Users to be notified')
    staros_full_version = StringField('STAROS_FULL_VERSION', default='none')
    nso_version = StringField('nso_version', default='none')
    submit = SubmitField('Start Build')

class DevBuildForm(FlaskForm):
    dev_branch_name = StringField('DEV_BRANCH_NAME',default=' ')
    submit = SubmitField('Start Build')


class PromoteForm(FlaskForm):
    qa_build_no = HiddenField()
    branch = HiddenField()
    promote_dh = SubmitField(label="Devhub")
    promote_cco = SubmitField(label="CCO")

class BranchForm(FlaskForm):
    submit_all = SubmitField(label="Show all branches - Active & Inactive")
    submit_active = SubmitField(label="Show only active branches")

class PastQABuildForm(FlaskForm):
    show_all = SubmitField(label="Show all builds")
    show_100 = SubmitField(label="Show only latest 100 builds")

class CoronaRequestForm(FlaskForm):
    branches = predecessor_id = []
    #qa_build_branch = SelectField('QA Build Branch', choices=list_to_choices(branches), default='main')
    qa_build_number = StringField('QA Build Number', [DataRequired()])
    trigger_corona_scan = BooleanField('trigger_corona_scan' ,default=True)
    predecessor_type = RadioField('toggle',choices=[('predecessor_id_option1','Select from Previous Corona IDs'),
                                                   ('predecessor_id_input_option2','Enter predecessor ID as input')])
    predecessor_id = SelectField('predecessor_id', choices=list_to_choices_predecessors(predecessor_id), default='none')
    predecessor_id_input = StringField('predecessor_id_input', default='none')
    users_to_notify = StringField('Users to be notified')
    #get_deliverables = SubmitField('Get Deliverables')
    submit = SubmitField('Trigger Corona Scan')
