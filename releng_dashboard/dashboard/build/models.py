from .. import db


class TriggeredBuilds(db.Model):
    __tablename__ = 'TRIGGERED_BUILDS'
    queue_id = db.Column(db.Integer, primary_key=True)
    product_name = db.Column(db.String(100), default=None)
    job_name = db.Column(db.String(100), default=None)
    build_number = db.Column(db.Integer, default=None)
    build_url = db.Column(db.String(300), default=None)
    triggered_by = db.Column(db.String(100), default=None)
    status = db.Column(db.String(100), default=None)
    start_time = db.Column(db.BigInteger, default=None)
    estimated_time = db.Column(db.BigInteger, default=None)
    duration = db.Column(db.BigInteger, default=None)
    queue_time = db.Column(db.BigInteger, default=None)
    progress = db.Column(db.Integer, default=None)
    eta = db.Column(db.Integer, default=None)
    notes = db.Column(db.String(2000), default=None)
    result_content = db.Column(db.String(500), default=None)


class Builds(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    build_number = db.Column(db.String(200))
    products_product_name = db.Column(db.String(200))
    branches_branch_name = db.Column(db.String(200))
    products_organization = db.Column(db.String(200))
    build_type = db.Column(db.String(200))
    build_stage = db.Column(db.String(200))
    build_status = db.Column(db.String(200))
    change_log = db.Column(db.String(200))
    loc_report = db.Column(db.String(200))
    corona_report = db.Column(db.String(1000))
    sonar_report = db.Column(db.String(1000))
    int_artifactory = db.Column(db.String(1000))
    off_artifactory = db.Column(db.String(1000))
    dh_promotion_status = db.Column(db.String(1))
    cco_posting_status = db.Column(db.String(1))
    archive_location = db.Column(db.String(200))
    build_online_start_time = db.Column(db.BigInteger)
    build_offline_start_time = db.Column(db.BigInteger)
    ext_source_build = db.Column(db.String(1000))
    branches_branch_version = db.Column(db.String(200))
    triggered_by = db.Column(db.String(1000))
    source_url =  db.Column(db.String(1000))
    cdl_version = db.Column(db.String(200))
    cdets_list = db.Column(db.String(2000))
    artifact_delete_time= db.Column(db.Integer)
    nf_details = db.Column(db.String(2000))

class Products(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_name = db.Column(db.String(100), default=None)

class Checksums(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    csum_build_number = db.Column(db.String(200))
    csum_rel_number = db.Column(db.String(200))
    products_product_name = db.Column(db.String(200))
    deliverable = db.Column(db.String(200))
    corona_id = db.Column(db.String(20))
    predecessor_id = db.Column(db.String(20))
