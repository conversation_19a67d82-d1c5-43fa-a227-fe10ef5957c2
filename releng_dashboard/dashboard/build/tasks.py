from dashboard.build.models import TriggeredBuilds, Builds, Checksums
from .. import celery
from .. import db
from pprint import pprint
import time
import sys, os
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
from GithubApi import GithubApi
from Bot import bot_send_message
from dashboard.utils.cnjenkins import CNJenkins
from dashboard.models import Releases
from os import environ
import re

@celery.task(name='trigger_jenkins_build', trail=True)
def trigger_jenkins_build(username, product, job_name, parameter, fname=None):
    pprint("Connecting to Jenkins..")
    email = username + "@cisco.com"
    room_id = os.environ['room_id']
    if (environ.get('JENKINS_TOKEN')):
        token = environ.get('JENKINS_TOKEN')
    else:
        print("Raising exception, no JENKINS_TOKEN")
        raise Exception("J<PERSON><PERSON>INS_TOKEN is not present in releng_dashboard/dashboard/build/tasks.py")
    jenkins = CNJenkins(server_url=environ.get('JENKINS_SERVER'), username=environ.get('JENKINS_USERNAME'),
                         token=environ.get('JENKINS_TOKEN'))
    if jenkins:
        # When a build is triggered a queue_id is returned
        queue_id = jenkins.build_job(job_name, parameter)
        if queue_id:
            build = TriggeredBuilds()
            build.queue_id = queue_id
            build.job_name = job_name
            build.product_name = product
            build.status = 'QUEUED'
            time.sleep(10) # This is needed, as soon as it the job is triggered the value of 'why' will be 'Finished waiting' .
            # We want to check if "A build with matching parameters is already running"
            queue_detail = jenkins.server.get_queue_item(queue_id)
            if queue_detail:
                why = queue_detail.get('why')
                if why == "A build with matching parameters is already running":
                    build.notes = "A build with matching parameters is already running"
                else:
                    build.notes = "Build is in queue"
            build.start_time = int(time.time())
            build.triggered_by = username
            try:
                db.session.add(build)
                db.session.commit()
            except Exception as e:
                pprint("Unable to write to DB %s" % e)
                db.session.rollback()
                if environ.get("ENVIRONMENT") == "PRODUCTION":
                    bot_send_message(room_id=room_id, message="HIGH: Unable to write QA Build info to DB.\n" + build)
                bot_send_message(email,"Your request to trigger a build from 5G Dashboard can't be completed. Jenkins is unreachable. Plz reach <NAME_EMAIL>")
                return (None)

            finally:
                db.session.close()
            pprint("Build has been triggered. Entry has been made in DB")

            build_url = None
            while build_url is None:
                pprint("Waiting for Build to start.... Jenkins Queue ID %s" % queue_id)
                queue_detail = jenkins.server.get_queue_item(queue_id)
                #pprint(queue_detail)
                if queue_detail:
                    exes = queue_detail.get('executable')
                    if exes:
                        build_url = exes.get('url')
                        if build_url:
                            build_number = build_url.split('/')[-2]
                            break
                        else:
                            time.sleep(10)
                    canc = queue_detail.get('cancelled')
                    if canc:
                        build_url = "Aborted"
                        break
                else:
                    build_url = "Aborted"
            # update DB with build_url
            db_build = build.query.filter_by(queue_id=queue_id).first()
            notes = ""

            if build_url == "Aborted":
                build_url = None
                build_status = "ABORTED"
                build.status = "ABORTED"
                db_build.status = "ABORTED"
                notes = "Build was aborted before it could start. Plz contact Releng"
            else:
                build_info = jenkins.get_build_details(job_name, build_number, fname)
                if build_info:
                    build_status = build_info["result"]
                    notes = "Build is in progress for %s. Track progress here %s" % (product, build_url)
                    start_time = int(build_info["timestamp"] / 1000)  # converting from unix epoch millisecond to second
                    estimated_time = int(build_info["estimatedDuration"] / 1000)  # millisecond to second
                    db_build.start_time = start_time
                    db_build.estimatedDuration = estimated_time
                    db_build.duration = build_info["duration"]
                    db_build.build_number = build_number
                    db_build.status = "IN-PROGRESS"

            pprint("For Queue_ID %s : %s " % (queue_id, notes))

            bot_send_message(email, str(notes))
            db_build.build_url = build_url
            db_build.notes = notes
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
            finally:
                db.session.close()

            retry = 0
            while build_status is None:
                build_info = jenkins.get_build_details(job_name, build_number)
                if build_info:
                    # find how long the build has run for
                    current_time = int(time.time())
                    duration = (current_time - start_time)  # in seconds
                    progress = int((duration / estimated_time) * 100)
                    eta = estimated_time - duration  # time left
                    db_build.duration = duration  # time.time() gives current time in seconds
                    db_build.eta = eta
                    db_build.progress = progress
                    build_status = build_info["result"]
                    pprint(build_info)
                    if "description" in build_info:
                        build_description = build_info["description"]
                    else:
                        build_description = None

                    # if build completes
                    if build_status is not None:
                        if build_status == "ABORTED":
                            if build_description:
                                pprint("Build is aborted : %s" % build_description)
                                html_link = "%s/artifact/promotion/qa_builds/component_build_status.html" % (build_url)
                                if re.search("dev_build_failure", build_description):
                                    notes = "SMI dev builds of dependent components have failed. For more Info check: %s" % (
                                        html_link)
                                elif re.search("branch_reference_failure", build_description):
                                    notes = "Wrong branch references were found in the code. For more Info check: %s" % (
                                        html_link)
                                elif re.search("no_change", build_description):
                                    build_status = "ABORTED"
                                    notes = "no code change from the last QA build"
                                else:
                                    build_status = "ABORTED"
                                    notes = "Build is aborted. plz check console logs "
                        else:
                            notes = "Build for %s is %s. %s" % (product, build_status, build_url)
                        db_build.status = build_status
                        db_build.progress = None
                        try:
                            pprint(db_build)
                            pprint("Commiting to DB")
                            db.session.add(db_build)
                            db.session.commit()
                        except Exception as e:
                            db.session.rollback()
                        finally:
                            db.session.close()
                        break
                    else:
                        pprint("Waiting for Build to complete.... Jenkins Build url %s" % build_url)
                        db_build.status = "IN-PROGRESS"
                        try:
                            pprint("Commiting to DB")
                            db.session.add(db_build)
                            db.session.commit()
                        except Exception as e:
                            db.session.rollback()
                        finally:
                            db.session.close()
                        time.sleep(30)
                else:
                    pprint("Unable to get Jenkins information")
                    time.sleep(30)
                    retry += 1
                    if retry > 5:
                        return (None)

            db_build.notes = notes
            #pprint("Build notes : %s" % notes)
            if "fcontent" in build_info:
                build_fcontent = build_info["fcontent"]
            else:
                build_fcontent = None
            if build_fcontent:
                db_build.result_content = build_fcontent
                pprint("Build fcontent : %s" % build_fcontent)

            try:
                db.session.add(db_build)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                if environ.get("ENVIRONMENT") == "PRODUCTION":
                    bot_send_message(room_id=room_id,
                                     message="HIGH: Build %s is successful, but fail to update DB" % build_url)
            finally:
                db.session.close()

            bot_send_message(email, notes)
            return ("Build is completed")
            #return (build_fcontent)
        else:
            if environ.get("ENVIRONMENT") == "PRODUCTION":
                bot_send_message(room_id=room_id, message="HIGH: Jenkins is unreachable")
            bot_send_message(email=email,
                             message="Your request to trigger a build from 5G Dashboard can't be completed. Jenkins is unreachable. Plz reach <NAME_EMAIL>")
            return (None)
    else:
        if environ.get("ENVIRONMENT") == "PRODUCTION":
            bot_send_message(room_id=room_id, message="HIGH: Jenkins is unreachable")
        bot_send_message(email=email,
                        message="Your request to trigger a build from 5G Dashboard can't be completed. Jenkins is unreachable. Plz reach <NAME_EMAIL>")
        return (None)


def build_trigger_failed():
    build = TriggeredBuilds()
    build.queue_id = queue_id
    build.job_name = job_name
    build.product_name = product
    build.status = 'QUEUED'
    build.notes = "Build is in queue"
    build.start_time = int(time.time())
    build.triggered_by = username
    db.session.add(build)
    try:
        db.session.commit()
    except Exception as e:
        pprint("unable to write to DB %s" % e)
        db.session.rollback()


def validate_qa_build(build_number,branches_branch_name,products_product_name):
    build_obj = Builds()
    build_info = []
    for row in build_obj.query.filter_by(build_number=build_number,branches_branch_name=branches_branch_name,products_product_name=products_product_name):
        build_info.append(row.__dict__)
    if build_info:
        return build_info

def get_qa_build_branch(products_product_name, build_number):
    builds_obj = Builds()
    branch = []
    for row in builds_obj.query.filter_by(products_product_name=products_product_name, build_number=build_number):
        branch.append(row.__dict__)
    if branch:
        branch_name = branch[0]['branches_branch_name']
        return branch_name
    else:
        return None

def get_list_of_qa_deliverables(build_number,branches_branch_name,products_product_name):
    checksums_obj = Checksums()
    builds_obj = Builds()
    deliverable_info = []
    deliverables = {}
    cdl_version = ''
    try:
        for row in checksums_obj.query.filter_by(csum_build_number=build_number,products_product_name=products_product_name):
            deliverable_info.append(row.__dict__)
    except Exception as e:
        pprint(e)
    
    if deliverable_info:
        for each_deliverable in deliverable_info:
            deliverable_value = each_deliverable.get('deliverable', 'default_value')
            if products_product_name == 'upf' :
                if deliverable_value.startswith('companion-vpc') and deliverable_value.endswith('.SSA.tgz'):
                    deliverables['companion_deliverable'] = deliverable_value
                elif deliverable_value.startswith('qvpc-si_T') and deliverable_value.endswith('qcow2.zip.SSA.tgz'):
                        deliverables['qvpc-si_T-qcow2_deliverable'] = deliverable_value
                elif deliverable_value.startswith('qvpc-si-') and deliverable_value.endswith('qcow2.zip.SSA.tgz'):
                        deliverables['qvpc-si-qcow2_deliverable'] = deliverable_value
                elif deliverable_value.startswith('qvpc-si_T') and deliverable_value.endswith('bin.SSA.tgz'):
                        deliverables['qvpc-si_T-bin_deliverable'] = deliverable_value
                elif deliverable_value.startswith('qvpc-si-') and deliverable_value.endswith('bin.SSA.tgz'):
                        deliverables['qvpc-si-bin_deliverable'] = deliverable_value
            else:
                pattern = r'^(?P<name>[\w-]+).(?P<version>\d+\.\d+\.[0-9a-zA-Z]+\.[0-9a-zA-Z]+)\.(?P<extension>\w+)\.tgz'
                deliverable_match = re.match(pattern, deliverable_value)
                if deliverable_match and products_product_name not in ['upf','staros']:
                    if deliverable_match.group('name') == 'amf':
                        # Get correspoding CDL deliverables specific for amf
                        try:
                            for row in builds_obj.query.filter_by(build_number=build_number,branches_branch_name=branches_branch_name,products_product_name=products_product_name):
                                cdl_version = row.cdl_version
                        except Exception as e:
                            pprint(e)
                        if cdl_version:
                            cdl_deliverable =  f'cdl-{cdl_version}.SSA.tgz'
                            deliverables['cdl_deliverable'] = cdl_deliverable
                            deliverables['deliverable'] = deliverable_value
                        else:
                            deliverables['cdl_deliverable'] = ''
                            deliverables['deliverable'] = deliverable_value
                    else:
                        deliverables['deliverable'] = deliverable_value
    return deliverables

def get_previous_corona_ids(products_product_name):
    checksums_obj = Checksums()
    releases_obj = Releases()
    prev_coronaid_info = []
    corona_id_dict = {}
    none_dict = {'none':{
            'build_number': 'none',
            'corona_id': 'none',
            'release_type': 'none',
            'release_version': 'none'
            }
        }
    corona_id_dict.update(none_dict)
    try:
        for row in checksums_obj.query.filter_by(products_product_name=products_product_name):
            prev_coronaid_info.append(row.__dict__)
    except Exception as e:
        pprint(e)
    if prev_coronaid_info:
        for deliv in prev_coronaid_info: 
            deliverable = deliv['deliverable']
            corona_id = deliv['corona_id']
            predecessor_id = deliv['predecessor_id']
            build_number = deliv['csum_build_number']
            int_dict = {}
            release_type = ''
            release_version = ''
            if (corona_id is not None or predecessor_id is not None) and deliverable.startswith(products_product_name) and deliverable.endswith('.SSA.tgz'):
                try:
                    for row in releases_obj.query.filter_by(builds_build_number=build_number,builds_products_product_name=products_product_name):
                        release_type = row.release_type
                        release_version = row.release_number
                except Exception as e:
                    pprint(e)

                int_dict[deliverable] = {
                    'build_number': build_number,
                    'corona_id': corona_id,
                    'release_type': release_type,
                    'release_version': release_version
                }
                corona_id_dict.update(int_dict)
    else:
        corona_id_dict = none_dict
    return corona_id_dict