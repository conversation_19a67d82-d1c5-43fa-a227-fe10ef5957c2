from flask import render_template, redirect, flash, Blueprint, request
from os import environ
from dashboard.build.forms import QABuildForm, list_to_choices, list_to_choices_predecessors, PromoteForm, DevBuildForm, PastQABuildForm, CoronaRequestForm
from dashboard.build.tasks import trigger_j<PERSON><PERSON>_build, validate_qa_build, get_list_of_qa_deliverables, get_previous_corona_ids, get_qa_build_branch
from dashboard.build.models import Builds, Products, TriggeredBuilds
from dashboard.tasks import get_product_list, get_product_organization, get_branch_list, get_user_role, get_product_art_ep, list_to_choices, get_dashboard_user_role
import datetime
import time
from dateutil.parser import parse
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
import Constants
from GithubApi import GithubApi
from CoronaScan import CoronaScan
from dashboard.utils.smi_build import get_smi_build_details
from dashboard.utils.common import get_content
from .. import cache
from dashboard import oidc
from dashboard.models import IVT, Releases, Branches, SANITY_REPORTS
import re
import json
import ast
#import requests

build = Blueprint('build', __name__)


@build.route('/<product>', methods=['GET', 'POST'])
@oidc.require_login
def product_home(product):
    '''
    home page for each product.
    this page will list the following
    - currently running qa builds & their status
    - information about old QA builds
    - Stats on Sanity result
    old code
    #from sqlalchemy import inspect
    #insp = inspect(Builds)
    #builds_columns = [c_attr.key for c_attr in insp.mapper.column_attrs]
    '''
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product == 'staros':
        return redirect('/branches/' + product )
    user_role = get_user_role(oidc.user_getfield('uid'))

    form = PromoteForm()
    if form.validate_on_submit():
        parameter = {'PRODUCT': product,
                     'QA_BUILD_NUMBER': form.qa_build_no.data,
                     'TRIGGERED_BY': oidc.user_getfield('uid')
                     }
        if form.promote_dh.data:
            parameter["PROMOTION_TYPE"] = "DevHub"
            job_name = environ.get('DEVHUB_BUILD_JOB_NAME')
            flash("Processing your request. Plz wait for few mins...", "info")
            trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, job_name, parameter)
        else:
            parameter["PROMOTION_TYPE"] = "CCO"
            # job_name = environ.get('CCO_BUILD_JOB_NAME')
            flash("CCO posting via Dashboard is still in development state. Plz raise an RER for CCO posting", "info")
        return redirect('/%s' % product)

    qa_build_info = query_last_5_build(product,build_type="regular")
    cc_qa_build_info = query_last_5_build(product,build_type="cc_build")
    all_builds = []
    current_time = int(time.time())
    time_24_back = current_time - 86400

    qa_current_builds = []
    offline_current_builds = []
    devhub_current_builds = []
    current_builds = {}

    triggered_build_mobj = TriggeredBuilds()
    for row in triggered_build_mobj.query.filter_by(product_name=product).order_by(TriggeredBuilds.start_time.desc()):
        all_builds.append(row.__dict__)
    for data in all_builds:
        # converting start_time to human readable format
        if data["start_time"]:
            if data["status"] not in ["SUCCESS"]:
                if data["start_time"] >= time_24_back:
                    data["start_time"] = datetime.datetime.fromtimestamp(int(data["start_time"])).strftime(
                        '%d %b %Y, %H:%M')
                    # ETA is in seconds, converting to mins.
                    if data["eta"]:
                        data["eta"] = int(data["eta"] / 60)
                    if data["job_name"] == environ.get('QA_BUILD_JOB_NAME'):
                        qa_current_builds.append(data)
                    if data["job_name"] == environ.get('DEVHUB_BUILD_JOB_NAME'):
                        devhub_current_builds.append(data)
                    if data["job_name"] == environ.get('DEV_OFFLINE_BUILD_JOB_NAME'):
                        offline_current_builds.append(data)

    current_builds["QA Build"] = qa_current_builds
    current_builds["Private Offline Bundle"] = offline_current_builds
    current_builds["Devhub Promotion"] = devhub_current_builds
    return render_template('product_home.html', product=product, current_builds=current_builds,
                           qa_build_info=qa_build_info, cc_qa_build_info = cc_qa_build_info, form=form, user_role=user_role, releng_cdl_artifactory=environ.get('CDL_RELENG_ARTIFACTORY'))



# Trigger qa build job
@build.route('/qabuild/<product>', methods=['GET', 'POST'])
@oidc.require_login
def qabuild(product):
    username = str(oidc.user_getfield('uid'))
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    if product in ["app-infra", "golang-lib", "infrastructure", "staros","lbs-libraries"]:
        flash("QA build for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    user_role,repo_name = get_dashboard_user_role(product_org,username)
    if not user_role in ["ORG_OWNER","DEV_MANAGER","TECH_LEAD","RELENG"]:
        flash("%s, you are not authorize to this page. Plz contact releng team" % str(username),
              "danger")
        return redirect('/%s' % product)

    form = QABuildForm()
    previous_corona_ids = []
    pre_build_products = ['cee', 'base-vm', 'deployer', 'cee-bm', 'base-bm', 'deployer-bm']
    branches = get_branch_list(product)
    
    if not branches:
        branches = ['main']
    form.branch_name.choices = list_to_choices(branches)
    previous_corona_ids = get_previous_corona_ids(product)

    if previous_corona_ids == 'none':
        form.predecessor_id.choices = 'none'
    else:
        form.predecessor_id.choices = list_to_choices_predecessors(previous_corona_ids)
    if request.method == 'POST':
        selected_option = form.predecessor_type.data
        if form.validate_on_submit():
            parameter = {'PRODUCT': product,
                        'BRANCH_NAME': form.branch_name.data,
                        'TRIGGERED_BY': oidc.user_getfield('uid'),
                         'DB_INST' : environ.get("DB_INST")
                        }
            if form.branch_name.data.startswith("dev-") or environ.get("DB_INST") == "5g-dev":
                parameter["TRIGGER_TESTS"] = None
            users_to_notify = form.users_to_notify.data
            if not users_to_notify:
                users_to_notify = username + '@cisco.com'
            if product in pre_build_products:
                parameter["BRANCH_NAME"] = "main"
            if product == "cee":
                parameter["CEE_BUILD_NO"] = form.build_no.data
            if product == "base-vm":
                parameter["BASEVM_BUILD_NO"] = form.build_no.data
            if product == 'deployer':
                parameter["DEPLOYER_BUILD_NO"] = form.build_no.data
            if product == 'cee-bm':
                parameter["CEE_BM_BUILD_NO"] = form.build_no.data
            if product == 'base-bm':
                parameter["BASE_BM_BUILD_NO"] = form.build_no.data
            if product == 'deployer-bm':
                parameter["DEPLOYER_BM_BUILD_NO"] = form.build_no.data
            if product == 'upf':
                parameter["STAROS_FULL_VERSION"] = form.staros_full_version.data
                parameter["BUILD_TYPE"] = "regular"
            if product in ['up-recovery','smi-nso']:
                parameter["NSO_VERSION"] = form.nso_version.data
            
            parameter["trigger_corona_scan"] = form.trigger_corona_scan.data
            parameter["users_to_notify"] = users_to_notify
            if selected_option == "predecessor_id_option1":
                predecessor_id_data = form.predecessor_id.data
                dict_obj = ast.literal_eval(predecessor_id_data)
                parameter["predecessor_id"] = dict_obj['corona_id']
            elif selected_option == "predecessor_id_input_option2":
                parameter["predecessor_id"] = form.predecessor_id_input.data
            else:
                predecessor_id_data = 'none'
                parameter["predecessor_id"] = ''
            if product == "ulb":
                parameter["RELENG_BRANCH_NAME"] = "main"
            flash("Processing your request. Plz wait for few mins...",  "info")
            if product in ['up-recovery','smi-nso']:
                trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('SMI_NSO_QA_JOB_NAME'),parameter)
            else:
                trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('QA_BUILD_JOB_NAME'),parameter)
            return redirect('/%s' % product)
    return render_template('trigger_qa_build.html', form=form, product=product, pre_build_products=pre_build_products,predecessor_id_choices=form.predecessor_id.choices,staros_full_version=form.staros_full_version.data,nso_version=form.nso_version.data)


# Display Old Build data
@build.route('/builds/<product>/<branch>', methods=['GET', 'POST'])
@oidc.require_login
def product_builds(product, branch):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    form = PastQABuildForm()
    show_all_button = True
    if form.validate_on_submit():
        if form.show_all.data:
            qa_build_info = query_last_builds(product, branch, "all")
            show_all_button = False

    if show_all_button:
        qa_build_info = query_last_builds(product, branch, None)
    return render_template('product_builds.html', form=form, product=product, qa_build_info=qa_build_info,show_all_button=show_all_button, releng_cdl_artifactory=environ.get('CDL_RELENG_ARTIFACTORY'))


@build.route('/smibuild/<product>', methods=['GET', 'POST'])
@oidc.require_login
def product_smi_builds(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    smi_build_info = get_smi_build_details(product)
    if not smi_build_info:
        flash("SMI Jenkins is not reachable. <NAME_EMAIL>", "danger")
        return redirect('/%s' % product)
    return render_template('product_smibuilds.html', product=product, smi_build_info=smi_build_info)


@build.route('/released_builds/<product>', methods=['GET', 'POST'])
@oidc.require_login
def release(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is,<NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    released_builds = query_last_releases(product)
    return render_template('released_builds.html', releases=released_builds, product=product)

@build.route('/private_offline_build/<product>', methods=['GET', 'POST'])
@oidc.require_login
def private_offline_build(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    if product in ["upf","staros"]:
        flash("Offline build for %s is not supported via dashboard. Plz raise an RER" % product, "info")
        return redirect('/%s' % product)

    if product in ["app-infra", "golang-lib", "infrastructure", "cee", "base-vm", "deployer", "cee-bm", "base-bm", "deployer-bm", "staros", "lbs-libraries", "smi-nso", "up-recovery"]:
        flash("Offline build for %s is not supported" % product, "info")
        return redirect('/%s' % product)


    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries"        
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)

    form = DevBuildForm()
    if form.validate_on_submit():
        parameter = {'PRODUCT': product,
                     'BRANCH_NAME': form.dev_branch_name.data,
                     'TRIGGERED_BY': oidc.user_getfield('uid'),
                     'RELENG_BRANCH_NAME': "main"
                    }

        if (not form.dev_branch_name.data.strip().startswith('dev-') ) and (not form.dev_branch_name.data.strip().startswith('test-') ) :
            flash("This feature is supported for dev and test branches only" , "danger")
            return redirect('/%s' % product)

        product_org = get_product_art_ep(product)
        if product_org:
            try:               
                flash("Processing your request. Plz wait for few mins...", "info")
                trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('DEV_OFFLINE_BUILD_JOB_NAME'),
                                parameter)
            except Exception as e:
               flash("Caught an excpetion: %s" % e, "danger")
        else:
            flash("Product org for product %s not found !" % product, "danger")

        return redirect('/%s' % product)
    return render_template('trigger_dev_offline_build.html', form=form, product=product)



@cache.memoize()
def query_last_5_build(product,build_type):
    qa_build_info = []
    build_mobj = Builds()
    ivt_obj = IVT()
    sanity_obj = SANITY_REPORTS()
    for row in build_mobj.query.filter_by(products_product_name=product,build_type=build_type).order_by(Builds.id.desc()).limit(5).all():
        data = row.__dict__
        if data["build_online_start_time"]:
            data["build_start_time"] = datetime.datetime.fromtimestamp(int(data["build_online_start_time"])).strftime(
                '%d %b %Y, %H:%M')
        else:
            data["build_start_time"] = datetime.datetime.fromtimestamp(int(data["build_offline_start_time"])).strftime(
                '%d %b %Y, %H:%M')
        if build_type == "cc_build":
            branches_obj = Branches()
            product_org = get_product_organization(product)
            for row in branches_obj.query.filter_by(products_organization=product_org,branch_type="cc_branch",branch_name=data["branches_branch_name"]):
                data_branch = row.__dict__
                if data_branch["parent_branch"]:
                    data["cc_parent_branch"] = data_branch["parent_branch"]
                if data_branch["parents_branch_point"]:
                    data["parents_branch_point"] = data_branch["parents_branch_point"]
        # if product is cnVPC add StarOS link as qa info data
        if product in ['cnvpc', 'dvpc']:
            if data["loc_report"]:
                staros_build_list = data["loc_report"].split('/')
                staros_build_list.pop()
                staros_build_info = '/'.join(staros_build_list) + '/staros_bn.txt'
                data["staros_build_info"] = staros_build_info
        if data["sonar_report"]:
            data["sonar_report"] = data["sonar_report"]
        if data["corona_report"]:
            data["corona_report"] = data["corona_report"]
        if product in ["upf", "staros"]:
            if data["source_url"]:
                # check if the source url have build number.
                build_number_value = data["source_url"].split("buildnum=")
                if len(build_number_value) > 1:
                    star_os_build = data["source_url"].split("buildnum=")[1].split("&")[0]
                    data["star_os_build"] = star_os_build
                    data["source_url"] = data["source_url"] 
        #if data['cdets_list']:
        #    ir_report_link = os.path.dirname(data['change_log']) + '/Update_IR_Report.html'
        #    response = requests.get(ir_report_link)
        #    if response.status_code == 200:
        #        data['ir_report_link'] = ir_report_link
        for row_ivt in ivt_obj.query.filter_by(products_product_name=product, build_number=data["build_number"]):
            data_ivt = row_ivt.__dict__
            if data_ivt:
                data["wrt_report"] = data_ivt["wrt_report"]
                data["ivt_approved"] = data_ivt["ivt_approved"]
                data["ivt_passed_percentage"] = data_ivt["passed_percentage"]
            else:
                data["wrt_report"] = None
                data["ivt_approved"] = None
                data["ivt_passed_percentage"] = None
        # Sanity can be a nested dic
        test_info = {}
        for row_sanity in sanity_obj.query.filter_by(products_product_name=product, build_number=data["build_number"],functional_suite="Total"):
            data_sanity = row_sanity.__dict__
            if data_sanity:
                if data_sanity["status"] == "COMPLETED":
                    test_info[data_sanity["job_type"]] = data_sanity["passed_percentage"]
                else:
                    test_info[data_sanity["job_type"]] = data_sanity["status"]
            else:
                test_info = {}
        data["test_info"] = test_info
        qa_build_info.append(data)
    return qa_build_info


def query_last_builds(product, branch, limit=None):
    qa_build_info = []
    build_mobj = Builds()
    ivt_obj = IVT()
    sanity_obj = SANITY_REPORTS()
    if branch == 'all':
        if limit == 'all':
            results = build_mobj.query.filter_by(products_product_name=product).order_by(Builds.id.desc())
        else:
            results = build_mobj.query.filter_by(products_product_name=product).order_by(Builds.id.desc()).limit(100).all()
    else:
        if limit == 'all':
            results = build_mobj.query.filter_by(products_product_name=product,branches_branch_name=branch).order_by(Builds.id.desc())
        else:
            results = build_mobj.query.filter_by(products_product_name=product,branches_branch_name=branch).order_by(Builds.id.desc()).limit(100).all()
    for row in results:
        data = row.__dict__
        if data["artifact_delete_time"]:
            data["int_artifactory"] = None
            data["off_artifactory"] = None
        if data["build_online_start_time"]:
            data["build_start_time"] = datetime.datetime.fromtimestamp(int(data["build_online_start_time"])).strftime(
                '%d %b %Y')
        elif data["build_offline_start_time"]:
            data["build_start_time"] = datetime.datetime.fromtimestamp(int(data["build_offline_start_time"])).strftime(
                '%d %b %Y')
        # if product is cnVPC add StarOS link as qa info data
        if product == 'cnvpc':
            if data["loc_report"]:
                staros_build_list = data["loc_report"].split('/')
                staros_build_list.pop()
                staros_build_info = '/'.join(staros_build_list) + '/staros_bn.txt'
                data["staros_build_info"] = staros_build_info
        #if data['cdets_list']:
        #    ir_report_link = os.path.dirname(data['change_log']) + '/Update_IR_Report.html'
        #    response = requests.get(ir_report_link)
        #    if response.status_code == 200:
        #        data['ir_report_link'] = ir_report_link
        if data["sonar_report"]:
            data["sonar_report"] = data["sonar_report"]
        if data['corona_report']:
            data["corona_report"] = data["corona_report"]
        if product in ["upf", "staros"]:
            if data["source_url"]:
                # check if the source url have build number.
                build_number_value = data["source_url"].split("buildnum=")
                if len(build_number_value) > 1:
                    star_os_build = data["source_url"].split("buildnum=")[1].split("&")[0]
                    data["star_os_build"] = star_os_build
                    data["source_url"] = data["source_url"]     
        for row_ivt in ivt_obj.query.filter_by(products_product_name=product, build_number=data["build_number"]):
            data_ivt = row_ivt.__dict__
            if data_ivt:
                data["wrt_report"] = data_ivt["wrt_report"]
                data["ivt_approved"] = data_ivt["ivt_approved"]
                data["ivt_passed_percentage"] = data_ivt["passed_percentage"]
            else:
                data["wrt_report"] = None
                data["ivt_approved"] = None
                data["ivt_passed_percentage"] = None

        test_info = {}
        for row_sanity in sanity_obj.query.filter_by(products_product_name=product, build_number=data["build_number"],functional_suite="Total"):
            data_sanity = row_sanity.__dict__
            if data_sanity:
                if data_sanity["status"] == "COMPLETED":
                    test_info[data_sanity["job_type"]] = data_sanity["passed_percentage"]
                else:
                    test_info[data_sanity["job_type"]] = data_sanity["status"]
            else:
                test_info = {}
        data["test_info"] = test_info
        qa_build_info.append(data)
    return qa_build_info



@cache.memoize()
def query_last_releases(product):
    releases = []
    releases_mobj = Releases()
    build_mobj = Builds()
    ivt_obj = IVT()
    sanity_obj = SANITY_REPORTS()
    for row in releases_mobj.query.filter_by(promotion_status='Passed', builds_products_product_name=product).order_by(
            Releases.id.desc()):
        data = row.__dict__
        if data["promoted_on"]:
            data["promoted_on"] = datetime.datetime.fromtimestamp(int(data["promoted_on"])).strftime('%d %b %Y')
            data["loc_report"] = data["loc_report"]
            data["change_log"] = data["change_log"]
        if "offline_url" in data:
            if data["offline_url"]:
                customer_endpoint=data["offline_url"].split("/")[4]
                if re.search("bharti",customer_endpoint):
                    data["customer"] = "BHARTI"
                elif re.search("charter",customer_endpoint):
                    data["customer"] = "CHARTER"
                elif re.search("bharti",customer_endpoint):
                    data["customer"] = "BHARTI"
                elif re.search("gogo",customer_endpoint):
                    data["customer"] = "GOGO"
                elif re.search("zain",customer_endpoint):
                    data["customer"] = "ZAIN"
                elif re.search("sfr",customer_endpoint):
                    data["customer"] = "SFR"
                elif re.search("5gaas",customer_endpoint):
                    data["customer"] = "5GAAS"
                elif re.search("p4",customer_endpoint):
                    data["customer"] = "P4"
        for row_build in build_mobj.query.filter_by(products_product_name=product,
                                                    build_number=data["builds_build_number"]):
            data_build = row_build.__dict__
            data["branches_branch_name"] = data_build["branches_branch_name"]
            data["cdl_version"] = data_build["cdl_version"]
            data["corona_report"] = data_build["corona_report"]
            data["sonar_report"] = data_build["sonar_report"]
            if product in ["upf", "staros"]:
                if data_build["source_url"]:
                    # check if the source url have build number.
                    build_number_value = data_build["source_url"].split("buildnum=")
                    if len(build_number_value) > 1:
                        star_os_build = data_build["source_url"].split("buildnum=")[1].split("&")[0]
                        data["star_os_build"] = star_os_build
                        data["source_url"] = data_build["source_url"]
        for row_ivt in ivt_obj.query.filter_by(products_product_name=product, build_number=data["builds_build_number"]):
            data_ivt = row_ivt.__dict__
            if data_ivt:
                data["wrt_report"] = data_ivt["wrt_report"]
                data["ivt_approved"] = data_ivt["ivt_approved"]
                data["ivt_passed_percentage"] = data_ivt["passed_percentage"]
            else:
                data["wrt_report"] = None
                data["ivt_approved"] = None
                data["ivt_passed_percentage"] = None
        # Sanity can be a nested dic
        test_info = {}
        for row_sanity in sanity_obj.query.filter_by(products_product_name=product, build_number=data["builds_build_number"],functional_suite="Total"):
            data_sanity = row_sanity.__dict__
            if data_sanity:
                if data_sanity["status"] == "COMPLETED":
                    test_info[data_sanity["job_type"]] = data_sanity["passed_percentage"]
                else:
                    test_info[data_sanity["job_type"]] = data_sanity["status"]
            else:
                test_info = {}
        data["test_info"] = test_info
        releases.append(data)
    return (releases)

@build.route('/corona_scan_home/<product>', methods=['GET', 'POST'])
@oidc.require_login
def create_corona_request(product):
    username = str(oidc.user_getfield('uid'))
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    
    form = CoronaRequestForm()
    #branches = get_branch_list(product)
    #form.qa_build_branch.choices = list_to_choices(branches)
    #qa_build_branch=form.qa_build_branch.data
    qa_build_number=form.qa_build_number.data
    previous_corona_ids = get_previous_corona_ids(product)
    if previous_corona_ids == 'none':
        form.predecessor_id.choices = 'none'
    else:
        form.predecessor_id.choices = list_to_choices_predecessors(previous_corona_ids)

    #get_deliverables_clicked = False
    # Get list of Deliverables 
    #dict_of_deliverables = {}
    #dict_of_deliverables = get_list_of_qa_deliverables(qa_build_number,qa_build_branch, product)
    #qa_deliverables_list = list(dict_of_deliverables.values())
    #for qa_deliv in qa_deliverables_list:
    if request.method == "POST":
        selected_option = form.predecessor_type.data
        if form.validate_on_submit():
            if form.submit.data:
                flash("Corona Scan initializing for QA Build %s.Please wait.." % (qa_build_number), "info" )
                users_to_notify = form.users_to_notify.data
                if not users_to_notify:
                    users_to_notify = username + '@cisco.com'
                DB_INST = environ.get("DB_INST")
                # Check if QA build number supplied is a single build or multiple QA builds                
                qa_build_number_list = []
                qa_build_number_values = qa_build_number.split(',')
                if len(qa_build_number_values) > 1:
                    qa_build_number_list = qa_build_number_values

                else:
                    qa_build_number_list = qa_build_number_values
                # Check initally that all the QA build numbers provided exists in Corona or not
                #coronascan_obj =  CoronaScan()
                # Get the Corona specific product for a given releng product
                #corona_product = coronascan_obj.get_corona_product(product)
                #for each_qa_build in qa_build_number_list:
                #    release_info = coronascan_obj.corona_ids_for_product_release(corona_product, each_qa_build)
                #    release_exists = release_info[0]
                #    # Check if the QA build number provided already exists in Corona
                #    if release_exists:
                #        flash("Release %s already exists in Corona..Corona scan cannot be triggered.." % each_qa_build , "danger")
                #        return redirect('/corona_scan_home/%s' % product)
                #    else:
                #        pass
                corona_scan_parameter = {
                                'PRODUCT': product,
                                'RELENG_BRANCH_NAME': os.environ.get('RELENG_BRANCH_NAME'),
                                'DB_INST': DB_INST,
                                'users_to_notify': users_to_notify
                            }
                for each_qa_build in qa_build_number_list:
                    qa_build_branch = get_qa_build_branch(product, each_qa_build)
                    corona_scan_parameter['QA_BUILD_BRANCH'] = qa_build_branch
                    if not validate_qa_build(each_qa_build, qa_build_branch, product):
                        flash("Entered QA build number %s is Invalid..." % each_qa_build ,"danger")
                        return redirect('/corona_scan_home/%s' % product)
                    else:
                        if selected_option == "predecessor_id_option1":
                            predecessor_id_data = form.predecessor_id.data
                            dict_obj = ast.literal_eval(predecessor_id_data)
                            corona_scan_parameter['QA_BUILD_NUMBER'] = each_qa_build
                            corona_scan_parameter["predecessor_id"] = dict_obj['corona_id']
                        elif selected_option == "predecessor_id_input_option2":
                            corona_scan_parameter["predecessor_id"] = form.predecessor_id_input.data
                            corona_scan_parameter['QA_BUILD_NUMBER'] = each_qa_build
                        else:
                            predecessor_id_data = 'none'
                            corona_scan_parameter['QA_BUILD_NUMBER'] = each_qa_build
                            corona_scan_parameter["predecessor_id"] = ''
                        trigger_jenkins_build.delay(username, product, environ.get('CORONA_SCAN_JOB_NAME'),corona_scan_parameter)
                # Redirect to corona scan homepage
                return redirect('/corona_scan_home/%s' % product)
    return render_template('corona_scan_home.html', product_org=product_org, product=product, form=form,predecessor_id_choices=form.predecessor_id.choices)

