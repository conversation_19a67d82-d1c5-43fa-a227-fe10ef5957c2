from flask import Flask
from dotenv import load_dotenv
import os, sys
from os import environ
from flask_mail import Mail
from flask_sqlalchemy import SQLAlchemy
from dashboard.utils.celery_utils import make_celery
from dashboard import celeryconfig
from flask_caching import Cache
import configparser

sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../libs/")
from pprint import pprint
from flask_oidc import OpenIDConnect
db_ini = os.path.join(os.path.dirname(__file__)) + "/../../libs/db.ini"

app = Flask(__name__)
basedir = os.path.abspath(os.path.dirname(__file__))
app.config["OIDC_CLIENT_SECRETS"] = "dashboard/client_secrets.json"
app.config["OIDC_COOKIE_SECURE"] = True
app.config["OIDC_SCOPES"] = ["openid", "email", "profile"]
if environ.get("ENVIRONMENT") == "PRODUCTION" or environ.get("ENVIRONMENT") == "STAGING":
    app.config["SECRET_KEY"] = "releng_dashboard"
if environ.get("ENVIRONMENT") == "DEVELOPMENT":
    app.config["SECRET_KEY"] = "test"

# Documentation on OIDC : https://flask-oidc.readthedocs.io/en/latest/#flask_oidc.OpenIDConnect.init_app
try:
    oidc = OpenIDConnect()
except Exception as e:
    print("Unable to instanciate OpenIDConnect %s" % e)

try:
    oidc.init_app(app)
except Exception as e:
    print("Unable to initialize the app %s" % e)

# Loading Env variables from .env file
load_dotenv(override=True)

# Loading flask config variables
app.config.from_pyfile('.flaskenv')

# Setup Environment
JENKINS_USERNAME = environ.get('JENKINS_USERNAME')
JENKINS_TOKEN = environ.get('JENKINS_TOKEN')
USERNAME = environ.get('USERNAME')
FULL_TOKEN = environ.get('FULL_TOKEN')
DB_PROD_PASSWORD = environ.get('DB_PROD_PASSWORD')
DB_DEV_PASSWORD = environ.get('DB_DEV_PASSWORD')
room_id = environ.get('room_id')

db_config = configparser.ConfigParser()
db_config.read(db_ini)

if environ.get("ENVIRONMENT") == "PRODUCTION":
    SQLALCHEMY_DATABASE_URI = "oracle" + '://' + db_config['5g-prod']['user'] + ':' + DB_PROD_PASSWORD +'@' + db_config['5g-prod']['host'] + ':' + db_config['5g-prod']['port'] + '/?service_name=' + db_config['5g-prod']['service_name']
elif environ.get("ENVIRONMENT") == "STAGING":
    SQLALCHEMY_DATABASE_URI = "oracle" + '://' + db_config['5g-dev']['user'] + ':' + DB_DEV_PASSWORD +'@' + db_config['5g-dev']['host'] + ':' + db_config['5g-dev']['port'] + '/?service_name=' + db_config['5g-dev']['service_name']
else:
    SQLALCHEMY_DATABASE_URI = "oracle" + '://' + db_config['5g-dev']['user'] + ':' + DB_DEV_PASSWORD +'@' + db_config['5g-dev']['host'] + ':' + db_config['5g-dev']['port'] + '/?service_name=' + db_config['5g-dev']['service_name']


app.config['SQLALCHEMY_DATABASE_URI'] = SQLALCHEMY_DATABASE_URI
db = SQLAlchemy(app)

# mail configuration
app.config['MAIL_SERVER'] = 'outbound.cisco.com'
mail = Mail(app)

# Adding celery
app.config.update(
    # CELERY_BROKER_URL='amqp://localhost//',
    CELERY_BROKER_URL='amqp://rabbitmq:rabbitmq@rabbit:5672',
    # CELERY_RESULT_BACKEND='oracle://CN_REL_DB:CnDev!2#<EMAIL>:1521/EIFLNXD4'
    CELERY_RESULT_BACKEND=None
)

celery = make_celery(app)
celery.config_from_object(celeryconfig)

config = {
    "DEBUG": True,  # some Flask specific configs
    "CACHE_TYPE": "simple",  # Flask-Caching related configs
    "CACHE_DEFAULT_TIMEOUT": 300
}

app.config.from_mapping(config)
cache = Cache(app)


# Global variables which can be used across all html
@app.context_processor
def context_processor():
    return dict(username=oidc.user_getfield('uid'))
