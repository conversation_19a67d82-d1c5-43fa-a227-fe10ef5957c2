from .. import celery
from .. import db
from pprint import pprint
import time
from flask import flash
import os
import sys
from os import environ
import re
from dashboard import oidc
from dashboard.models import SANITY_REPORTS

def get_summary(product, builds_to_compare,job_type):
    sanity_obj = SANITY_REPORTS()
    builds = []
    for build in builds_to_compare:
        for row_sanity in sanity_obj.query.filter_by(products_product_name=product,build_number=build,job_type=job_type):
            data = {}
            data_sanity = row_sanity.__dict__
            if data_sanity["build_number"] == build:
                data["functional_suite"] = data_sanity["functional_suite"]
                data["build_number"] = data_sanity["build_number"]
                data["total_feature_file_count"] = data_sanity["total_feature_file_count"]
                data["passed_case_count"] = data_sanity["passed_case_count"]
                data["failed_case_count"] = data_sanity["failed_case_count"]
                builds.append(data)
    return builds


def get_functional_suites(product, builds_to_compare,job_type):
    sanity_obj = SANITY_REPORTS()
    functional_suites = []
    tests_list = []
    tests_info = []
    data = {}
    for build in builds_to_compare:
        for row_sanity in sanity_obj.query.filter_by(products_product_name=product,build_number=build,job_type=job_type):
            data_sanity = row_sanity.__dict__
            if data_sanity["functional_suite"] not in functional_suites:
                functional_suites.append(data_sanity["functional_suite"])
    return functional_suites

def get_tests_info(product, builds_to_compare,job_type,functional_suites):
    sanity_obj = SANITY_REPORTS()
    tests_info = []
    for functional_suite in functional_suites:
        tests_list = []
        data = {}
        for build in builds_to_compare:
            tests = ""     
            for row_sanity in sanity_obj.query.filter_by(products_product_name=product,build_number=build,job_type=job_type,functional_suite=functional_suite):
                data_sanity = row_sanity.__dict__
                
                if data_sanity["pass_test_case"] not in ("none","",None):
                    tests += str(data_sanity["pass_test_case"])
                if data_sanity["fail_test_case"] not in ("none","",None):
                    tests += ","+str(data_sanity["fail_test_case"])
        if tests:
            tests_list.append(tests)
        data[functional_suite] = tests_list
        tests_info.append(data)
    return tests_info
    

def compare_builds(product, builds_to_compare,job_type):
    sanity_obj = SANITY_REPORTS()
    qa_build_info = []
    for build in builds_to_compare:
        
        for row_sanity in sanity_obj.query.filter_by(products_product_name=product,build_number=build,job_type=job_type):
            test_info = {}
            data_sanity = row_sanity.__dict__
            if data_sanity:
                if ((data_sanity["pass_test_case"] or data_sanity["fail_test_case"]) not in ("none","",None)):
                    test_info["build_number"] = data_sanity["build_number"]
                    test_info["functional_suite"] = data_sanity["functional_suite"]
                    test_info["pass_test_case"] = data_sanity["pass_test_case"]
                    test_info["fail_test_case"] = data_sanity["fail_test_case"]
            
                    qa_build_info.append(test_info)                
    return qa_build_info

#@cache.memoize(timeout=3600, unless=environ.get("DEBUG_MODE"))
def get_job_types(product):
    job_types = []
    branches = []
    sanity_obj = SANITY_REPORTS()
    for row in sanity_obj.query.filter_by(products_product_name=product):
        data = row.__dict__
        if data['job_type'] not in job_types:
            job_types.append(data['job_type'])
        if data['branch_name'] not in branches:
            branches.append(data['branch_name'])     
    return job_types,branches  
