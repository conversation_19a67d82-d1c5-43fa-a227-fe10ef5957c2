from flask_wtf import FlaskForm
from wtforms import SubmitField
from dashboard.tasks import list_to_choices
from wtforms import SelectField
class CompareForm(FlaskForm):
    submit = SubmitField('Submit')
    job_types = []
    branches = []
    job_type = SelectField('Job_type', choices=list_to_choices(job_types), default='SMF_SANITY')
    branch_name = SelectField('Branch_name', choices=list_to_choices(branches), default='main')
