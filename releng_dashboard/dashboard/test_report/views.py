from flask import render_template, redirect, flash, Blueprint, request
from dashboard.tasks import get_product_list,list_to_choices
from dashboard import oidc
import os
import sys
from .forms import CompareForm

from dashboard.models import SANITY_REPORTS, IVT
from dashboard.test_report.tasks import compare_builds,get_functional_suites, get_tests_info,get_summary,get_job_types
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../libs/")
#sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../libs/")
#import test_compare

test_report = Blueprint('test_report', __name__)
grafana_host=os.environ["GRAFANA_HOST"]

@test_report.route('/sanity/<product>', methods=['GET', 'POST'])
@oidc.require_login
def sanity_report(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    return render_template('product_sanity.html', product=product,grafana_host=grafana_host )


@test_report.route('/ivt/<product>')
@oidc.require_login
def ivt_report(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    return render_template('product_ivt.html', product=product,grafana_host=grafana_host)

@test_report.route('/test_report/<product>/<build_number>/<job_type>')
@oidc.require_login
def qa_report(product,build_number,job_type):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    sanity_result = []
    sanity_obj = SANITY_REPORTS()
    sanity_info = {}
    sanity_info["build_number"] = build_number
    sanity_info["product"] = product
    sanity_info["job_type"] = job_type

    for row in sanity_obj.query.filter_by(products_product_name=product, build_number=build_number, job_type=job_type).all():
        data = row.__dict__
        if data["functional_suite"] == "Total":
            sanity_info["total_feature_file_count"] = data["total_feature_file_count"]
            sanity_info["passed_percentage"] = data["passed_percentage"]
            sanity_info["failed_case_count"] = data["failed_case_count"]
            sanity_info["passed_case_count"] = data["passed_case_count"]
            sanity_info["branch_name"] = data["branch_name"]
            sanity_info["status"] = data["status"]
            sanity_info["fail_test_case"] = data["fail_test_case"] 
        else:
            sanity_result.append(data)
    return render_template('test_report_qa_build.html', sanity_result=sanity_result, sanity_info = sanity_info )

# Not sure if this is adding value can be removed later.
@test_report.route('/test_report/<job_type>/<product>', methods=['GET', 'POST'])
@oidc.require_login
def test_report_all(job_type, product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')

    sanity_result = []
    sanity_obj = SANITY_REPORTS()
    if job_type == "all":
        query_result = sanity_obj.query.filter_by(products_product_name=product).order_by(sanity_obj.start_time).all()
    else:
        query_result = sanity_obj.query.filter_by(products_product_name=product, job_type = job_type).all()
    for row in query_result:
        data = row.__dict__
        sanity_result.append(data)
    return render_template('test_report_all.html', sanity_result=sanity_result, product = product )


@test_report.route('/test_compare_home/<product>', methods=['GET', 'POST'])
@oidc.require_login
def test_compare_home(product):
    product_list = get_product_list()
    form = CompareForm()
    job_types,branches = get_job_types(product)
    if not job_types or not branches:
        flash("For %s product there are no test reports to compare. <NAME_EMAIL> to integrate test reports " % product, "danger""")
        return redirect('/%s' % product)

    form.job_type.choices = list_to_choices(job_types)
    form.branch_name.choices = list_to_choices(branches)
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')

    if request.method == "POST":
        job_type = form.job_type.data
        branch_name = form.branch_name.data
        return redirect('/test_compare/%s/%s/%s' % (product,job_type,branch_name))
    return render_template('test_report_home.html',product=product,form=form )


@test_report.route('/test_compare/<product>/<job_type>/<branch_name>', methods=['GET', 'POST'])
@oidc.require_login
def test_compare_all(product,job_type,branch_name):
    product_list = get_product_list()
    builds_to_compare = []
    form = CompareForm()
    tests_info = {}
    functional_suites = []
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')

    sanity_result = []
    sanity_obj = SANITY_REPORTS()
    query_result = sanity_obj.query.filter_by(products_product_name=product,job_type=job_type,branch_name=branch_name,functional_suite="Total").order_by(SANITY_REPORTS.start_time.desc()).limit(10).all()
    for row in query_result:
        data = row.__dict__
        if data["build_number"] not in builds_to_compare:
            builds_to_compare.append(data["build_number"])
            sanity_result.append(data)
        
    if request.method == "POST":    
        builds_to_compare = request.form.getlist(product + "_CompareBuilds")
        if builds_to_compare:
            builds_to_compare.sort()
            functional_suites = get_functional_suites(product,builds_to_compare,job_type)
            tests_info = get_tests_info(product,builds_to_compare,job_type,functional_suites)
            build_info = compare_builds(product, builds_to_compare,job_type)
            builds = get_summary(product, builds_to_compare,job_type)
            return render_template('tests_compare_report.html', product=product, builds=builds, tests_info=tests_info, builds_to_compare=builds_to_compare,functional_suites=functional_suites,build_info=build_info,form=form)
        else:
            flash("no QA buids to compare")    
    return render_template('test_compare_all.html', sanity_result=sanity_result,product=product,form=form,job_type=job_type )

@test_report.route('/test_report/functional_suite/<functional_suite>')
@oidc.require_login
def functional_suite(functional_suite):
    functional_suite_data = []
    sanity_obj = SANITY_REPORTS()
    for row in sanity_obj.query.filter_by(functional_suite=functional_suite):
        data = row.__dict__
        functional_suite_data.append(data)
    return render_template('test_report_function_suite.html', functional_suite = functional_suite, functional_suite_data=functional_suite_data)
