from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SubmitField, widgets, SelectMultipleField, SelectField, HiddenField, RadioField
from dashboard.tasks import list_to_choices
import Constants as constants

class SonarRunForm(FlaskForm):
    dev_branch_name = StringField('DEV BRANCH NAME',default=' ')
    repo = StringField('REPO',default=' ')
    submit = SubmitField('Execute Sonar Run')


class SonarReportForm(FlaskForm):
    branches = []
    branch_name = StringField('BRANCH_NAME', default='main')
    mail_to = StringField('MAIL_TO',default=' ')
    submit = SubmitField('Generate Sonar Report')


class GosecReportForm(FlaskForm):
    branches = []
    branch_name = StringField('BRANCH_NAME', default='main')
    mail_to = StringField('MAIL_TO',default=' ')
    submit = SubmitField('Generate Gosec Report')

class ClocReportForm(FlaskForm):
    branches = []
    branch_name = StringField('BRANCH_NAME', default='main')
    mail_to = StringField('MAIL_TO',default=' ')
    submit = SubmitField('Generate CLOC Report')

class MergedFeatureCodeCoverageForm(FlaskForm):
    versions = constants.release_version_mapping
    main_version = SelectField('MAIN_VERSION', choices=list_to_choices(versions))
    feature_list = StringField('FEATURE_LIST')
    mail_to = StringField('MAIL_TO',default=' ')
    submit = SubmitField('Generate Report')
    proceed = SubmitField('Proceed with Feature Code Coverage')

class OngoingFeatureCodeCoverageForm(FlaskForm):
    jira_id = StringField('JIRA_ID')
    branch_name = StringField('BRANCH_NAME')
    base_branch_name = StringField('BASE_BRANCH_NAME', default='main')
    mail_to = StringField('MAIL_TO',default=' ')
    submit = SubmitField('Generate Report')
    proceed = SubmitField('Proceed with Feature Code Coverage')

class GenerateFeatureReport(FlaskForm):
    versions = constants.release_version_mapping
    main_version = SelectField('MAIN_VERSION', choices=list_to_choices(versions))
    feature_id = StringField('FEATURE_ID')
    mail_to = StringField('MAIL_TO',default=' ')
    submit = SubmitField('Generate Report')

class FeatureCodeCoverageHomeForm(FlaskForm):
    submit = SubmitField('Next')
    feature_status = RadioField('generate_feature_dod',choices=[('O','For a feature which is ongoing'),
                                                                ('M','For a feature which is merged'),
                                                                ('G','Regenerate sonar report of a merged feature'),
                                                                ('V','View feature coverage data')])
