from flask import render_template, redirect, flash, Blueprint, request
from os import environ
from dashboard.sonar.forms import SonarRunForm, SonarReportForm,ClocReportForm,GosecReportForm,MergedFeatureCodeCoverageForm, OngoingFeatureCodeCoverageForm, FeatureCodeCoverageHomeForm, GenerateFeatureReport
from dashboard.sonar.models import FeatureCoverage
from dashboard.build.tasks import trigger_jenkins_build
from dashboard.tasks import get_product_list, get_product_organization, get_branch_list, get_user_role, get_product_art_ep

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
import Constants
import requests
import json
from dashboard.models import db
from sqlalchemy import text
from dashboard import oidc
from GithubApi import GithubApi
from dashboard.tasks import list_to_choices
# Removed the import for update_feature_coverage_with_test_loc

sonar = Blueprint('sonar', __name__)
import configparser
sonar_checks_ini = os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/sonar_checks.ini"

def _api_call(url, auth=None, params=None):
    """Helper function to make API calls with proper error handling"""
    try:
        if auth:
            response = requests.get(url, params=params, auth=(auth, ''))
        else:
            response = requests.get(url, params=params)
        return response
    except Exception as e:
        print(f"API call failed: {e}")
        return None

# Trigger sonar coverage report
@sonar.route('/sonarrrun/<product>', methods=['GET', 'POST'])
@oidc.require_login
def sonarrrun(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros","smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries"     
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)

    form = SonarRunForm()
    if form.validate_on_submit():
        config = configparser.ConfigParser()
        config.read(os.path.realpath(sonar_checks_ini))
        repo = form.repo.data
        repos_admin_cmd = config.get(product_org, 'repos_admin_cmd').split(",")
        job_name = environ.get('SONAR_RUN_JOB_NAME')
        for repo_config in repos_admin_cmd:
            if repo_config == repo.strip():
                job_name = environ.get('SONAR_RUN_JOB_NAME_RHEL_8')
        parameter = {'ORGANIZATION': product_org,
                     'REPO': form.repo.data,
                     'BRANCH_NAME': form.dev_branch_name.data,
                     'TRIGGERED_BY': oidc.user_getfield('uid'),
                     'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME')
                     }
        flash("Processing your request. Plz wait for few mins...", "info")
        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, job_name,
                                    parameter)
        return redirect('/%s' % product)
    return render_template('trigger_sonar.html', form=form, product=product,product_org=product_org)


@sonar.route('/sonarstats/<product>', methods=['GET', 'POST'])
@oidc.require_login
def sonarstats(product):
    product_list = get_product_list()
    grafana_host=os.environ["GRAFANA_HOST"]
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros","smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)
    product_org = get_product_organization(product)
    return render_template('sonar_stats.html', product=product,product_org=product_org,grafana_host=grafana_host)

@sonar.route('/sonarreport/<product>', methods=['GET', 'POST'])
@oidc.require_login
def sonarreport(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros", "smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries"    
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)
    form = SonarReportForm()
    mail_list = form.mail_to.data + "," + oidc.user_getfield('uid')
    if form.validate_on_submit():
        parameter = {'PRODUCT': product,
                     'EMAIL_TO': mail_list,
                     'BRANCH_NAME': form.branch_name.data,
                     'QA_BUILD' : "latest",
                     'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME')
                     }
        flash("Processing your request. Plz wait for few mins...", "info")
        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('SONAR_REPORT_JOB_NAME'),
                                    parameter)
        return redirect('/%s' % product)
    return render_template('sonar_report.html', form=form, product=product)


@sonar.route('/gosecreport/<product>', methods=['GET', 'POST'])
@oidc.require_login
def gosecreport(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros","smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries"    
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)
    form = GosecReportForm()
    mail_list = form.mail_to.data + "," + oidc.user_getfield('uid')
    if form.validate_on_submit():
        parameter = {'org_name': product_org,
                     'emails': mail_list,
                     'BRANCH_NAME': form.branch_name.data,
                     'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME')
                     }
        flash("Processing your request. Plz wait for few mins...", "info")
        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('GOSEC_REPORT_JOB_NAME'),
                                    parameter)
        return redirect('/%s' % product)
    return render_template('gosec_report.html', form=form, product=product,product_org=product_org)


@sonar.route('/clocreport/<product>', methods=['GET', 'POST'])
@oidc.require_login
def clocreport(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros","smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries"    
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)
    form = ClocReportForm()
    mail_data = form.mail_to.data
    mail_list = mail_data.strip() + "," + oidc.user_getfield('uid')
    if form.validate_on_submit():
        parameter = {'ORGANIZATION': product_org,
                     'emails': mail_list,
                     'BRANCH_NAME': form.branch_name.data,
                     'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME')
                     }
        flash("Processing your request. Plz wait for few mins...", "info")
        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('CLOC_REPORT_JOB_NAME'),
                                    parameter)
        return redirect('/%s' % product)
    return render_template('cloc_report.html', form=form, product=product,product_org=product_org)

'''
@sonar.route('/featureccreporthome/<product>', methods=['GET', 'POST'])
@oidc.require_login
def create_branch_home(product):
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')
    if product in ["smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'

    form = FeatureCodeCoverageHomeForm()
    if request.method == "POST":
        feature_status = form.feature_status.data
        if form.validate_on_submit():
            if feature_status == 'M':
                return redirect('/mergedfeature/%s' % product)
            elif feature_status == 'G':
                return redirect('/generateFeatureReport/%s' % product)
            elif feature_status == 'V':
                return redirect('/featurecoverage/%s' % product)
            else:
                return redirect('/ongoingfeature/%s' % product)
    return render_template('feature_cc_home.html', form=form)
'''

@sonar.route('/mergedfeature/<product>', methods=['GET', 'POST'])
@oidc.require_login
def mergedfeature(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros", "smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries" 
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)
    form = MergedFeatureCodeCoverageForm()
    if form.validate_on_submit():
        main_version = form.main_version.data
        feature_list = form.feature_list.data
        mail_data = form.mail_to.data
        if mail_data:
            mail_list = mail_data.strip() + "," + oidc.user_getfield('uid')
        else:
            mail_list = oidc.user_getfield('uid')
        parameter = {'PRODUCT' : product,
                     'FEATURE_ID': feature_list,
                     'MAIN_VERSION': main_version.split(":")[0].strip(),
                     'MAIL_TO' : mail_list,
                     'COMMIT_AUTHOR_CEC_ID': oidc.user_getfield('uid')
                    }
        flash("Processing your request. Plz wait for few mins...", "info")
        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('MERGED_FEATURE_CC_REPORT_JOB_NAME'), parameter)
        return redirect('/%s' % product)
    return render_template('merged_feature_cc.html', form=form, product=product)


@sonar.route('/ongoingfeature/<product>', methods=['GET', 'POST'])
@oidc.require_login
def ongoingfeature(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros", "smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries"     
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)
    form = OngoingFeatureCodeCoverageForm()
    if form.validate_on_submit():
        jira_id = form.jira_id.data
        branch_name = form.branch_name.data
        base_branch_name = form.base_branch_name.data
        mail_data = form.mail_to.data
        mail_list = mail_data.strip() + "," + oidc.user_getfield('uid')
        jira_id = jira_id.upper()
        if jira_id.startswith('FEAT') or jira_id.startswith('PMOB'):
            if branch_name.startswith('dev-') or branch_name.startswith('test-'):
                parameter = {'PRODUCT' : product,
                             'BRANCH_NAME': branch_name,
                             'REFERENCE_BRANCH': base_branch_name,
                             'JIRA_ID': jira_id,
                             'TEST_TYPE' : "UT",
                             'MAIL_TO' : mail_list
                             }
                flash("Processing your request. Plz wait for few mins...", "info")
                trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('ONGOING_FEATURE_CC_REPORT_JOB_NAME'), parameter)
                return redirect('/%s' % product)
            else:
                flash("Branch entered : %s is not valid. Branch name should start with dev-/test-" % branch_name, "danger" )
                return redirect('/ongoingfeature/%s' % product)
        else:
            flash("Feature ID entered : %s is not valid. Feature ID should start with FEAT/PMOB" % jira_id, "danger" )
            return redirect('/ongoingfeature/%s' % product)
    return render_template('ongoing_feature_cc.html', form=form, product=product)


@sonar.route('/generateFeatureReport/<product>', methods=['GET', 'POST'])
@oidc.require_login
def generateFeatureReport(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if product in ["upf","staros", "smi-nso", "up-recovery"]:
        flash("Code coverage for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    product_org = get_product_organization(product)
    github_obj = GithubApi()
    # Check if user is part of the $org-write team or not.
    team_prod = product
    if product == 'pcf':
        team_prod = "policy"
    if product == 'ccg':
        team_prod = "cn"
    if product == 'cnvpc':
        team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries"     
    no_per_write = github_obj.check_user_in_team(product_org, team_prod+"-write", oidc.user_getfield('uid'))
    no_per_admin = github_obj.check_user_in_team(product_org, team_prod+"-admins", oidc.user_getfield('uid'))
    if no_per_write and no_per_admin:
        flash("Operation Denied .You are not part of %s product's Admin or Write GitHub Team . "
              "Only %s product's Admin or Write team's member can trigger this operation via Dashboard. " % (product, product), "danger")
        return redirect('/%s' % product)
    form = GenerateFeatureReport()
    if form.validate_on_submit():
        main_version = form.main_version.data
        feature_id = form.feature_id.data
        release_version = main_version.split(":")[0].strip()
        # check if data exist for given version & feature_id
        result = check_feature_in_db(release_version,feature_id)
        if not result:
            flash("Releng don't have any record for feature:%s merged in %s version of main."
                  "Plz initiate a run" % (feature_id, release_version), "danger")
            return redirect('/mergedfeature/%s' % product)
        mail_data = form.mail_to.data
        mail_list = mail_data.strip() + "," + oidc.user_getfield('uid')
        parameter = {'PRODUCT' : product,
                     'BRANCH_NAME': "dev-cc-%s" % feature_id,
                     'FEATURE_ID': feature_id,
                     'RELEASE_VERSION': release_version,
                     'EMAIL_TO' : mail_list,
                     'EMAIL_SUBJECT' : "5G Feature Sonar Report",
                     'FEATURE_MERGED' : True
                     }
        flash("Processing your request. Plz wait for few mins...", "info")
        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('SONAR_REPORT_JOB_NAME'), parameter)
        return redirect('/%s' % product)
    return render_template('generate_feature_report.html', form=form, product=product)


def check_feature_in_db(main_version,feature_id):
    feature_coverage_obj = FeatureCoverage()
    data = {}
    for row in feature_coverage_obj.query.filter_by(release_version=main_version,feat_id=feature_id,sonar_app_branch="UT-merge-%s" % feature_id).all():
        data = row.__dict__
    return data

@sonar.route('/featureccreporthome/<product>', methods=['GET', 'POST'])
@oidc.require_login
def feature_coverage(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    
    # Define available release versions
    release_versions = ['2025.03.m0', '2025.02.m0']
    
    # Get selected version from form or use default
    selected_version = request.args.get('release_version', release_versions[0])
    
    try:
        # Query the database using the FeatureCoverage model
        if selected_version == 'all':
            query_result = FeatureCoverage.query.filter_by(product=product).all()
        else:
            query_result = FeatureCoverage.query.filter_by(product=product, release_version=selected_version).all()
        
        feature_data = []
        for row in query_result:
            # Convert SQLAlchemy model to dictionary
            data = row.__dict__.copy()
            if '_sa_instance_state' in data:
                del data['_sa_instance_state']
            
            # Get feature ID and check if it's merged
            feat_id = data.get('feat_id')
            sonar_app_branch = data.get('sonar_app_branch', '')
            feature_merged = sonar_app_branch.startswith('UT-merge-') if sonar_app_branch else False
            
            # Get sonar projects for this feature
            if feat_id:
                print(f"Fetching sonar projects for feature {feat_id}")
                sonar_projects = get_feature_sonar_projects(
                    feat_id, 
                    data.get('release_version'), 
                    product,
                    feature_merged
                )
                
                if sonar_projects:
                    print(f"Found {len(sonar_projects)} projects for feature {feat_id}")
                    data['sonar_projects'] = sonar_projects
                else:
                    print(f"No projects found for feature {feat_id}")
                    data['sonar_projects'] = []
            else:
                print(f"No feature ID found in data")
                data['sonar_projects'] = []
            
            feature_data.append(data)
        
        # For debugging - print the first feature data
        if feature_data:
            print(f"First feature data: {feature_data[0]}")
        
        return render_template('feature_cc_home.html', 
                              product=product, 
                              feature_data=feature_data, 
                              release_versions=release_versions,
                              selected_version=selected_version)
    except Exception as e:
        import traceback
        print(f"Error in feature_coverage: {e}")
        print(traceback.format_exc())
        flash(f"Error retrieving feature coverage data: {e}", "danger")
        return redirect(f'/{product}')

def get_sonar_projects(sonar_app_name, sonar_app_branch):
    """
    Fetch sonar projects data for a given application and branch using the applications/show API
    
    Args:
        sonar_app_name: The name of the sonar application
        sonar_app_branch: The branch name
        
    Returns:
        List of dictionaries containing project data
    """
    url = Constants.SONAR_URL + "/api/applications/show"
    params = {
        'application': sonar_app_name,
        'branch': sonar_app_branch
    }
    auth = Constants.SONAR_TOKEN
    
    print(f"Making API request to {url} with params {params}")
    
    response = _api_call(url, auth, params)
    if not response:
        print("API call failed")
        return []
        
    print(f"Response status: {response.status_code}")  
    if response.status_code == 200:
        data = response.json()
        print(f"API response received")
        
        # Process each project in the application
        projects = []
        
        # Extract projects from the application data
        app_projects = data.get('application', {}).get('projects', [])
        
        for project_info in app_projects:
            if project_info.get('selected') == True:  # Note: API returns boolean, not string
                project_key = project_info.get('key')
                project_branch = project_info.get('branch')
                
                # Get metrics for this project
                project_data = get_project_metrics(project_key, project_branch)
                if project_data:
                    projects.append(project_data)
        
        print(f"Processed {len(projects)} projects")
        return projects
    
    else:
        print(f"Failed to fetch sonar projects. Status code: {response.status_code}")
        return []



def get_project_metrics(project_key, branch):
    """
    Get metrics for a specific project and branch
    
    Args:
        project_key: The project key
        branch: The branch name
        
    Returns:
        Dictionary with project metrics
    """
    import requests
    
    # Sonar API endpoint for component measures
    sonar_api_url = Constants.SONAR_URL + "/api/measures/component"
    
    # Parameters for the API request
    params = {
        'component': project_key,
        'branch': branch,
        'metricKeys': 'coverage,ncloc,bugs,vulnerabilities,code_smells,tests'
    }
    
    # Get auth token from Constants
    auth_token = Constants.SONAR_TOKEN
    
    try:
        # Make the API request
        if auth_token:
            response = requests.get(sonar_api_url, params=params, auth=(auth_token, ''))
        else:
            response = requests.get(sonar_api_url, params=params)
        
        if response.status_code == 200:
            data = response.json()
            
            # Extract component info
            component = data.get('component', {})
            
            project_data = {
                'name': component.get('name', ''),
                'key': component.get('key', ''),
                'ut_coverage': 0,
                'loc': 0,
                'ut_loc': 0,  # Default value for UT LOC
                'bugs': 0,
                'vulnerabilities': 0,
                'code_smells': 0
            }
            
            # Extract metrics
            measures = component.get('measures', [])
            for measure in measures:
                metric = measure.get('metric')
                value = measure.get('value')
                
                if metric == 'coverage':
                    project_data['ut_coverage'] = float(value) if value else 0
                elif metric == 'ncloc':
                    project_data['loc'] = int(value) if value else 0
                elif metric == 'bugs':
                    project_data['bugs'] = int(value) if value else 0
                elif metric == 'vulnerabilities':
                    project_data['vulnerabilities'] = int(value) if value else 0
                elif metric == 'code_smells':
                    project_data['code_smells'] = int(value) if value else 0
                elif metric == 'tests':
                    # This is just a placeholder, as Sonar doesn't directly provide test LOC
                    # The actual value will be updated from the database
                    project_data['ut_loc'] = int(value) if value else 0
            
            return project_data
        
        else:
            print(f"Failed to fetch project metrics. Status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error fetching project metrics: {e}")
        return None


def get_project_metrics(project_key, branch):
    """
    Get metrics for a specific project and branch
    
    Args:
        project_key: The project key
        branch: The branch name
        
    Returns:
        Dictionary with project metrics
    """
    import requests
    
    # Sonar API endpoint for component measures
    sonar_api_url = Constants.SONAR_URL + "/api/measures/component"
    
    # Parameters for the API request
    params = {
        'component': project_key,
        'branch': branch,
        'metricKeys': 'coverage,ncloc,bugs,vulnerabilities,code_smells,tests'
    }
    
    # Get auth token from Constants
    auth_token = Constants.SONAR_TOKEN
    
    try:
        # Make the API request
        if auth_token:
            response = requests.get(sonar_api_url, params=params, auth=(auth_token, ''))
        else:
            response = requests.get(sonar_api_url, params=params)
        
        if response.status_code == 200:
            data = response.json()
            
            # Extract component info
            component = data.get('component', {})
            
            project_data = {
                'name': component.get('name', ''),
                'key': component.get('key', ''),
                'ut_coverage': 0,
                'loc': 0,
                'ut_loc': 0,  # Default value for UT LOC
                'bugs': 0,
                'vulnerabilities': 0,
                'code_smells': 0
            }
            
            # Extract metrics
            measures = component.get('measures', [])
            for measure in measures:
                metric = measure.get('metric')
                value = measure.get('value')
                
                if metric == 'coverage':
                    project_data['ut_coverage'] = float(value) if value else 0
                elif metric == 'ncloc':
                    project_data['loc'] = int(value) if value else 0
                elif metric == 'bugs':
                    project_data['bugs'] = int(value) if value else 0
                elif metric == 'vulnerabilities':
                    project_data['vulnerabilities'] = int(value) if value else 0
                elif metric == 'code_smells':
                    project_data['code_smells'] = int(value) if value else 0
                elif metric == 'tests':
                    # This is just a placeholder, as Sonar doesn't directly provide test LOC
                    # The actual value will be updated from the database
                    project_data['ut_loc'] = int(value) if value else 0
            
            return project_data
        
        else:
            print(f"Failed to fetch project metrics. Status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error fetching project metrics: {e}")
        return None

def get_feature_sonar_projects(feature_id, release_version, product, feature_merged=False):

    from dashboard.sonar.models import Sonar
    from sqlalchemy import text
    import traceback

    print(f"Starting get_feature_sonar_projects for {feature_id}, {release_version}, {product}, merged={feature_merged}")

    feature_branch = f"dev-cc-{feature_id}"
    app_branch_name = f"UT-{'merge-' if feature_merged else ''}{feature_id}"
    feature_data = None

    try:
        sql = text("""
            SELECT * FROM FEATURE_COVERAGE 
            WHERE RELEASE_VERSION = :release_version 
            AND FEAT_ID = :feat_id 
            AND SONAR_APP_BRANCH = :app_branch_name
        """)

        result = db.session.execute(sql, {
            'release_version': release_version,
            'feat_id': feature_id,
            'app_branch_name': app_branch_name
        })

        for row in result:
            feature_data = dict(row._mapping)
            break

        if not feature_data:
            print(f"No feature data found for {feature_id} in {release_version}")
            return []

        print(f"Found feature data in database: {feature_data}")

    except Exception as e:
        print(f"Error querying feature coverage: {e}")
        print(traceback.format_exc())
        return []

    projects = []
    try:
        sql = text("""
            SELECT DISTINCT organization 
            FROM SONAR 
            WHERE branch = :branch
        """)
        result = db.session.execute(sql, {'branch': feature_branch})
        organizations = [row[0] for row in result]

        print(f"Found organizations with branch {feature_branch}: {organizations}")

        product_orgs = [org for org in organizations if (
            f"mobile-cnat-{product}" in org.lower() or 
            f"mobile-{product}" in org.lower() or 
            product.lower() in org.lower()
        )] or organizations

        print(f"Filtered organizations for product {product}: {product_orgs}")

        for org in product_orgs:
            repo_list = db.session.query(Sonar.repository)\
                .filter(Sonar.organization == org, Sonar.branch == feature_branch)\
                .distinct().all()

            print(f"Found {len(repo_list)} repositories for {org} with branch {feature_branch}")

            for repo_tuple in repo_list:
                repo = repo_tuple[0]

                latest_sonar = db.session.query(Sonar)\
                    .filter_by(organization=org, repository=repo, branch=feature_branch)\
                    .order_by(Sonar.id.desc())\
                    .first()

                if latest_sonar:
                    data = latest_sonar.__dict__.copy()
                    data.pop('_sa_instance_state', None)
                    print(f"Latest sonar data for {org}/{repo}: {data}")
                    
                    # Make sure new_ut_loc is properly extracted from the data
                    ut_loc_value = data.get('new_ut_loc')
                    if ut_loc_value is None or ut_loc_value == 0:
                        # If new_ut_loc is not available or zero, try to get it from a separate query
                        try:
                            # Try a more direct query without too many conditions
                            ut_loc_query = text("""
                                SELECT * FROM (
                                    SELECT NEW_UT_LOC FROM SONAR 
                                    WHERE ORGANIZATION = :org 
                                    AND REPOSITORY = :repo 
                                    AND BRANCH LIKE :branch_pattern
                                    AND NEW_UT_LOC IS NOT NULL
                                    AND NEW_UT_LOC > 0
                                    ORDER BY ID DESC
                                ) WHERE ROWNUM <= 1
                            """)
                            
                            # Use a pattern that matches both feature branch formats
                            branch_pattern = f"%{feature_id}%"
                            
                            ut_loc_result = db.session.execute(ut_loc_query, {
                                'org': org,
                                'repo': repo,
                                'branch_pattern': branch_pattern
                            }).fetchone()
                            
                            if ut_loc_result and ut_loc_result[0] is not None:
                                ut_loc_value = ut_loc_result[0]
                                print(f"Found UT_LOC from pattern query: {ut_loc_value} for {org}/{repo}")
                            else:
                                # If still not found, try a more general query
                                ut_loc_query = text("""
                                    SELECT * FROM (
                                        SELECT NEW_UT_LOC, BRANCH FROM SONAR 
                                        WHERE ORGANIZATION = :org 
                                        AND REPOSITORY = :repo 
                                        AND NEW_UT_LOC IS NOT NULL
                                        AND NEW_UT_LOC > 0
                                        ORDER BY ID DESC
                                    ) WHERE ROWNUM <= 5
                                """)
                                
                                ut_loc_results = db.session.execute(ut_loc_query, {
                                    'org': org,
                                    'repo': repo
                                }).fetchall()
                                
                                if ut_loc_results:
                                    # Print all results for debugging
                                    print(f"Found {len(ut_loc_results)} UT_LOC results for {org}/{repo}:")
                                    for result in ut_loc_results:
                                        print(f"  Branch: {result[1]}, UT_LOC: {result[0]}")
                                    
                                    # Use the first result
                                    ut_loc_value = ut_loc_results[0][0]
                                    print(f"Using UT_LOC value: {ut_loc_value} from branch: {ut_loc_results[0][1]}")
                                else:
                                    ut_loc_value = 0
                                    print(f"No UT_LOC found in any query for {org}/{repo}, defaulting to 0")
                        except Exception as e:
                            print(f"Error querying UT_LOC: {e}")
                            ut_loc_value = 0
                    
                    # Make sure new_ut_cc is properly extracted from the data
                    ut_cc_value = data.get('new_ut_cc')
                    if ut_cc_value is None or ut_cc_value == 0:
                        # If new_ut_cc is not available or zero, try to get it from a separate query
                        try:
                            # First try to get coverage from the same record
                            ut_cc_value = data.get('new_ut_cc')
                            if ut_cc_value is None or ut_cc_value == 0:
                                # If not available in the current record, try other records for the same project
                                ut_cc_query = text("""
                                    SELECT NEW_UT_CC FROM SONAR 
                                    WHERE ORGANIZATION = :org 
                                    AND REPOSITORY = :repo 
                                    AND BRANCH = :branch
                                    AND NEW_UT_CC IS NOT NULL
                                    AND NEW_UT_CC > 0
                                    ORDER BY ID DESC LIMIT 1
                                """)
                                ut_cc_result = db.session.execute(ut_cc_query, {
                                    'org': org,
                                    'repo': repo,
                                    'branch': feature_branch
                                }).fetchone()
                                
                                if ut_cc_result and ut_cc_result[0] is not None:
                                    ut_cc_value = ut_cc_result[0]
                                    print(f"Found UT_CC from direct query: {ut_cc_value}")
                                else:
                                    # If still not found, try with a different branch pattern
                                    alt_branch = f"dev-{feature_id}"
                                    ut_cc_query = text("""
                                        SELECT * FROM (
                                            SELECT NEW_UT_CC, BRANCH FROM SONAR 
                                            WHERE ORGANIZATION = :org 
                                            AND REPOSITORY = :repo 
                                            AND BRANCH LIKE :branch
                                            AND NEW_UT_CC IS NOT NULL
                                            AND NEW_UT_CC > 0
                                            ORDER BY ID DESC
                                        ) WHERE ROWNUM <= 1
                                    """)
                                    ut_cc_result = db.session.execute(ut_cc_query, {
                                        'org': org,
                                        'repo': repo,
                                        'branch': f"%{feature_id}%"
                                    }).fetchone()
                                    
                                    if ut_cc_result and ut_cc_result[0] is not None:
                                        ut_cc_value = ut_cc_result[0]
                                        print(f"Found UT_CC from alternative branch query: {ut_cc_value}")
                                    else:
                                        ut_cc_value = 0
                                        print(f"No UT_CC found in any query, defaulting to 0")
                        except Exception as e:
                            print(f"Error querying UT_CC: {e}")
                            ut_cc_value = 0
                    
                    project = {
                        'name': repo,
                        'key': data.get('sonar_project_key', ''),
                        'organization': org,
                        'ut_coverage': float(ut_cc_value) if ut_cc_value is not None else 0,
                        'loc': int(data.get('new_loc', 0)) if data.get('new_loc') is not None else 0,
                        'ut_loc': int(ut_loc_value) if ut_loc_value is not None else 0,
                        'bugs': int(data.get('new_sa_bugs', 0)) if data.get('new_sa_bugs') is not None else 0,
                        'vulnerabilities': int(data.get('new_vulnerability', 0)) if data.get('new_vulnerability') is not None else 0,
                        'code_smells': int(data.get('new_code_smells', 0)) if data.get('new_code_smells') is not None else 0
                    }
                    
                    # Debug: Print the UT_LOC value
                    print(f"Project {repo} UT_LOC: {project['ut_loc']}")

                    projects.append(project)
                    print(f"Added project data for {org}/{repo}")

    except Exception as e:
        print(f"Error while fetching sonar project data: {e}")
        print(traceback.format_exc())
    
    print(f"Returning {len(projects)} projects for feature {feature_id}")
    return projects

