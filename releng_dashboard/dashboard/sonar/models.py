from .. import db

class Sonar(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    organization = db.Column(db.String(200))
    repository = db.Column(db.String(200))
    branch = db.Column(db.String(200))
    head_sha = db.Column(db.String(200))
    ut_passed = db.Column(db.Integer, default=None)
    ut_failed = db.Column(db.Integer, default=None)
    ut_error = db.Column(db.Integer, default=None)
    new_ut_cc = db.Column(db.Float, default=None)
    new_sa_bugs = db.Column(db.Integer, default=None)
    new_loc = db.Column(db.Integer, default=None)
    new_vulnerability = db.Column(db.Integer, default=None)
    new_code_smells = db.Column(db.Integer, default=None)
    new_uncovered_lines = db.Column(db.Integer, default=None)
    new_ut_loc = db.Column(db.Integer, default=None)

class FeatureCoverage(db.Model):
    release_version = db.Column(db.String(40), primary_key=True)
    feat_id = db.Column(db.String(40), primary_key=True)
    analysis_date = db.Column(db.Date)
    sonar_app_name = db.Column(db.String(100))
    ut_new = db.Column(db.Float)
    ft_new = db.Column(db.Float)
    systest_new = db.Column(db.Float)
    cc_new = db.Column(db.Float)
    loc_new = db.Column(db.Integer)
    ut_loc = db.Column(db.Integer)  # New column for test file LOC
    bugs = db.Column(db.Integer)
    vulnerabilities = db.Column(db.Integer)
    code_smell = db.Column(db.Integer)
    sonar_app_branch = db.Column(db.String(200))
    product = db.Column(db.String(200))
