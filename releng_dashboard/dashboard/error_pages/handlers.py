from flask import Blueprint, render_template, flash
import httplib2

error_pages = Blueprint('error_pages', __name__)


@error_pages.app_errorhandler(404)
def error_404(error):
    return render_template('error_pages/404.html'), 404

@error_pages.app_errorhandler(httplib2.socks.HTTPError)
def handle_httperror(e):
    return render_template('error_pages/504.html'), 504

@error_pages.app_errorhandler(httplib2.ServerNotFoundError)
def handle_ServerNotFoundError(e):
    return render_template('error_pages/ServerNotFoundError.html')