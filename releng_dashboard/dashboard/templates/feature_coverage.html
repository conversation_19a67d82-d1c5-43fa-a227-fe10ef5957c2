{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css"
    integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
    $(document).ready(function () {
        // Make sure the table exists before initializing
        if ($.fn.dataTable.isDataTable('#feature_coverage')) {
            $('#feature_coverage').DataTable().destroy();
        }

        // Initialize the main table with proper configuration
        var featureTable = $('#feature_coverage').DataTable({
            "order": [[0, "desc"]],
            "pageLength": 10,
            "searching": true,
            "paging": true,
            "info": true,
            "ordering": true,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "columnDefs": [
                { "orderable": true, "targets": "_all" }
            ],
            "drawCallback": function () {
                // Reinitialize popovers and tooltips after table redraw
                $('[data-toggle="popover"]').popover();
                $('[data-toggle="tooltip"]').tooltip();

                // Make sure collapse functionality works after redraw
                $('.accordion-toggle').off('click').on('click', function () {
                    var target = $(this).data('target');
                    $(target).collapse('toggle');
                });

                // Prevent clicks on the expand button from triggering row click
                $('.btn-sm').off('click').on('click', function (e) {
                    e.stopPropagation();
                });
            }
        });

        console.log("DataTable initialized");

        // Initialize popovers and tooltips
        $('[data-toggle="popover"]').popover();
        $('[data-toggle="tooltip"]').tooltip();

        // Prevent the collapse from triggering when clicking on links in the row
        $('.accordion-toggle a').on('click', function (e) {
            e.stopPropagation();
        });

        // Handle the expand/collapse button click separately
        $('.btn-sm').on('click', function (e) {
            e.stopPropagation();
            var target = $(this).data('target');
            $(target).collapse('toggle');
        });
    });
</script>

<style>
    .hiddenRow {
        padding: 0 !important;
    }
</style>

<div class="col-xs-6 col-sm-10">
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-4">Feature Code Coverage Data</h1>
            <p class="lead">Feature code coverage metrics for {{ product }}</p>
        </div>
    </div>

    <div class="form-group row">
        <label for="release_version" class="col-sm-2 col-form-label">Release Version:</label>
        <div class="col-sm-4">
            <form method="GET" action="{{ url_for('sonar.feature_coverage', product=product) }}" id="version_form">
                <select name="release_version" id="release_version" class="form-control" style="width: 150px;"
                    onchange="this.form.submit()">
                    <option value="all">All Versions</option>
                    {% for version in release_versions %}
                    <option value="{{ version }}" {% if selected_version==version %}selected{% endif %}>{{ version }}
                    </option>
                    {% endfor %}
                </select>
            </form>
        </div>
    </div>

    <table id="feature_coverage" class="table table-striped table-bordered" style="width: 100%">
        <thead class="table-primary">
            <tr style="text-align:center; background-color:#000000;color:white">
                <th scope="col">Release Version</th>
                <th scope="col">Feature ID</th>
                <th scope="col">Analysis Date</th>
                <th scope="col">Sonar Link</th>
                <th scope="col">UT Coverage</th>
                <th scope="col">Dev LOC</th>
                <th scope="col">UT LOC</th>
                <th scope="col">Bugs</th>
                <th scope="col">Vulnerabilities</th>
                <th scope="col">Code Smells</th>
            </tr>
        </thead>
        <tbody>
            {% for data in feature_data %}
            <tr style="text-align:center" class="accordion-toggle" data-toggle="collapse"
                data-target="#sonar_{{ data.feat_id }}">
                <td>{{ data.release_version }}</td>
                <td>
                    {{ data.feat_id }}
                    {% if data.sonar_app_name %}
                    <button class="btn btn-sm" type="button" data-toggle="collapse"
                        data-target="#sonar_{{ data.feat_id }}" aria-expanded="false"
                        aria-controls="sonar_{{ data.feat_id }}">
                        <i class="fas fa-caret-down"></i>
                    </button>
                    {% endif %}
                </td>
                <td>{{ data.analysis_date }}</td>
                <td>
                    {% if data.sonar_app_name and data.sonar_app_branch %}
                    <a href="https://engci-sonar-sjc.cisco.com/sonar/dashboard?branch={{ data.sonar_app_branch }}&id={{ data.sonar_app_name }}"
                        target="_blank">
                        {{ data.sonar_app_branch }}
                    </a>
                    {% else %}
                    N/A
                    {% endif %}
                </td>
                {% if data.ut_new is not none %}
                {% if data.ut_new >= 90 %}
                <td style="color:green;font-weight:bold">{{ data.ut_new }}%</td>
                {% elif data.ut_new >= 50 %}
                <td style="color:orange;font-weight:bold">{{ data.ut_new }}%</td>
                {% else %}
                <td style="color:tomato;font-weight:bold">{{ data.ut_new }}%</td>
                {% endif %}
                {% else %}
                <td>N/A</td>
                {% endif %}
                <td>{{ data.loc_new }}</td>
                <td>{{ data.ut_loc if data.ut_loc is not none else 'N/A' }}</td>
                <td>{{ data.bugs }}</td>
                <td>{{ data.vulnerabilities }}</td>
                <td>{{ data.code_smell }}</td>
            </tr>
            <tr>
                <td colspan="10" class="hiddenRow">
                    <div class="accordian-body collapse" id="sonar_{{ data.feat_id }}">
                        <table class="table table-striped table-bordered nested-table">
                            <thead>
                                <tr style="text-align:center; background-color:#f0f0f0;color:black">
                                    <th scope="col">Sonar Project</th>
                                    <th scope="col">UT Coverage</th>
                                    <th scope="col">Dev LOC</th>
                                    <th scope="col">UT LOC</th>
                                    <th scope="col">Bugs</th>
                                    <th scope="col">Vulnerabilities</th>
                                    <th scope="col">Code Smells</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if data.sonar_projects %}
                                {% for project in data.sonar_projects %}
                                <tr style="text-align:center">
                                    <td>
                                        <a href="https://engci-sonar-sjc.cisco.com/sonar/dashboard?id={{ project.key if project.key else project.name }}&branch=dev-cc-{{ data.feat_id }}&code_scope=new"
                                            target="_blank">
                                            {{ project.name }}
                                        </a>
                                    </td>
                                    {% if project.ut_coverage is not none %}
                                    {% if project.ut_coverage >= 90 %}
                                    <td style="color:green;font-weight:bold">{{ project.ut_coverage|round(2) }}%</td>
                                    {% elif project.ut_coverage >= 50 %}
                                    <td style="color:orange;font-weight:bold">{{ project.ut_coverage|round(2) }}%</td>
                                    {% else %}
                                    <td style="color:tomato;font-weight:bold">{{ project.ut_coverage|round(2) }}%</td>
                                    {% endif %}
                                    {% else %}
                                    <td>N/A</td>
                                    {% endif %}
                                    <td>{{ project.loc }}</td>
                                    <td>{{ project.ut_loc if project.ut_loc is defined else 'N/A' }}</td>
                                    <td>{{ project.bugs }}</td>
                                    <td>{{ project.vulnerabilities }}</td>
                                    <td>{{ project.code_smells }}</td>
                                </tr>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">No project details available for this feature
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% endblock %}