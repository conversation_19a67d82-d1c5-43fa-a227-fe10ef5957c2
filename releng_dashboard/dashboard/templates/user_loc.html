{% extends 'product_base.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Generate User LOC Report</h1>
                <p class="lead"> This will generate User based LOC Report between two dates for current and dependent orgs.</p>
            </div>
        </div>
        <br>

        <div class="form-group row">
            <label for="branch" class="col-md-2 control-label" style="text-align:left;"> Branch Name<font color="red"> *</font></label>
            <div class="col-sm-10">
	      {{ form.branch(size=20) }}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma(,) separated branch names
                </small>
	    </div>
        </div>

        <div class="form-group row">
            <label for="from_date" class="col-md-2 control-label" style="text-align:left;"> Start Date<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.from_date(class='datepicker') }} </div>
        </div>

        <div class="form-group row">
            <label for="to_date" class="col-md-2 control-label" style="text-align:left;"> End Date<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.to_date(class='datepicker') }} </div>
        </div>
        <div class="form-group row">
            <label for="recp_list" class="col-md-2 control-label" style="text-align:left;"> User List</font></label>
            <div class="col-sm-10">
                {{ form.user_list }}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma(,) separated cec ids or blank for all users
                </small>
            </div>
        </div>


        <div class="form-group row">
            <label for="recp_list" class="col-md-2 control-label" style="text-align:left;"> Addtional Email Recipient List </label>
            <div class="col-sm-10">
                {{ form.recp_list }}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma(,) separated cec ids
                </small>
            </div>
            <!--
            <label for="recp_list" class="col-md-2 control-label" style="text-align:left;"> {Comma seperated cec ids} </label>
            -->
        </div>

        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
        </div>
    </form>
</div>

{% endblock %}
