{% extends 'product_base.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Regenerate Feature Code Coverage Report for feature which is merged</h1>
                <p class="lead">Use this feature to regerate a new report after solving the Unit Test failures</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="release_version" class="col-sm-2 col-form-label">MAIN BRANCH VERSION</label>
            <div class="col-sm-10">
                {{form.main_version}}
                <small class="form-text text-muted">select the main branch version in which the feature was merged</small>
            </div>
        </div>
        <div class="form-group row">
            <label for="feature_list" class="col-sm-2 col-form-label">JIRA FEATURE ID</label>
            <div class="col-sm-10">
                {{form.feature_id}}
                <small class="form-text text-muted">mention jira feature id, Ex: FEAT-6101</small>
            </div>
        </div>
        <div class="form-group row">
            <label for="mail_to" class="col-sm-2 col-form-label">{{form.mail_to.label}}</label>
            <div class="col-sm-10">
                {{form.mail_to}}
                <small class="form-text text-muted">mention list of email id seperated by (,). By default you will get an email</small>
            </div>
        </div>

        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:30%")}}
            </div>
        </div>
    </form>
</div>

{% endblock %}
