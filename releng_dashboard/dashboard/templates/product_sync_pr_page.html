{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#DashboardMergePR').DataTable({
        "pageLength": 10
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
    <form method="POST">
        {% if db_merged_pr_branch_info %}
        <div class="fw-body" >
            <div class="content">
                <div class="jumbotron jumbotron-fluid">
                    <div class="container">
                        <h3 class="display-4">PR Status of the Merge # {{ merge_id }} : {{product_org}}</h3>
                    </div>
                </div>
                <table id ="DashboardMergePR" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                    <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm" scope="col">Select</th>
                        <th class="th-sm" scope="col">PR LINK</th>
                        <th class="th-sm" scope="col">PR STATUS</th>
                        <!--<th class="th-sm" scope="col">PR OWNERS</th>-->
                        <th class="th-sm" scope="col">PR MANDATORY/EXPECTED CHECK FAILURE</th>
                        <th class="th-sm" scope="col">PR SOURCE BRANCH SHA-ID </th>
                        <th class="th-sm" scope="col">ACTION</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for merge_pr_value in db_merged_pr_branch_info %}
                            <tr style="text-align:center">
                                {% if merge_pr_value['pr_status'].title() == "Ready For Merge" %}
                                    <td>
                                        <input type="checkbox" id="{{ merge_pr_value['pr_link'] }}" name="{{product}}_Select" value="{{ merge_pr_value['pr_link'] }}">
                                    </td>
                                {% else %}
                                <td>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="merge_disabled" value="disabled" disabled>
                                        <label class="form-check-label" for="merge_disabled"></label>
                                      </div>
                                </td>
                                {% endif %}
                                <td>
                                    <a href = " {{ merge_pr_value['pr_link'] }} ">
                                        {{ merge_pr_value['merge_pr_repo'] }}
                                    </a>
                                </td>
                                {% if merge_pr_value['pr_status'].title() in ['Ready For Merge', 'Merged'] %}
                                    <td><font color = "green">{{merge_pr_value['pr_status']}}</font></td>
                                {% elif merge_pr_value['pr_status'].title() == 'Merging_Is_Blocked' %}
                                    <td><font color = "orange">{{merge_pr_value['pr_status']}}</font></td>    
                                {% elif merge_pr_value['pr_status'].title() == 'Build_Failure' %}
                                    <td><font color = "red">{{merge_pr_value['pr_status']}}</font></td>
                                {% elif merge_pr_value['pr_status'].title() == 'Conflict' %}
                                    <td><font color = "red">{{merge_pr_value['pr_status']}}</font></td>
                                {% elif merge_pr_value['pr_status'].title() == 'Pending' %}
                                    <td><font color = "red">{{merge_pr_value['pr_status']}}</font></td>    
                                {% else %}
                                    <td>{{ merge_pr_value['pr_status'].title() }}</td>
                                {% endif %}
                                <!--<td>{{ merge_pr_value['pr_authors'] }}</td>-->
                                <td>
                                    {% if merge_pr_value['pr_build_failure'] in [ 'None' , 'expected', 'EXPECTED', 'UNKNOWN', 'CONFLICT', 'BUILD_FAILURE', 'NA', ' ', '', 'null'] %}
                                        {{ merge_pr_value['pr_build_failure'] }}
                                    {% else %}
                                        {% set pr_failure_list = merge_pr_value['pr_build_failure'].split(",") %}
                                                {% for pr_failure_link in pr_failure_list %}
                                                    {% set pr_failure_link_value = pr_failure_link.split('=') %}
                                                    {{ pr_failure_link_value[0] }}
                                                        <a href = "{{ pr_failure_link_value[1] }}">
                                                            {{ pr_failure_link_value[1]  }}
                                                            <br>
                                                        </a>
                                        {% endfor %}
                                    {% endif %}
                                </td>
                                <td>
                                  <a href = "https://wwwin-github.cisco.com/{{ product_org }}/{{ merge_pr_value['merge_pr_repo'] }}/tree/{{ merge_pr_value['source_branch_sha'] }} ">
                                      {{ merge_pr_value['source_branch_sha'][0:7] }}
                                  </a>
                                </td>
                                <td>
                                  {% if merge_pr_value['pr_status'] in ['OPEN', 'CONFLICT', 'BUILD_FAILURE', 'reopened', 'opened', 'review_requested', 'synchronize', 'Merge_Conflict', 'Pending', 'PENDING'] %}
                                        <form method="POST">
                                            <button type="button" class="btn btn-outline-danger" data-toggle="modal" data-target="#close{{ loop.index }}" title="Close PR"><i class="fas fa-ban"></i></button>
                                            <div class="modal fade" id="close{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h6 class="modal-title" id="close">Are you sure you want to close the PR :  <a href = " {{ merge_pr_value['pr_link'] }} ">
                                                                {{ merge_pr_value['merge_pr_repo'] }}</a></h6>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form method="POST">
                                                                {{form.csrf_token()}}
                                                                <div>
                                                                    {{ form.pr_url(value=merge_pr_value['pr_link']) }}
                                                                    {{ form.close_pr(class="btn btn-danger ")}}
                                                                </div>
                                                            </form>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <h6></h6>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    {% endif %}
                                </td>
                            </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
                {{form.csrf_token()}}
                {{ form.pr_url(value="null") }}
                {{ form.merge_home(class="btn btn-info")}}
                {{ form.refresh_pr(class="btn btn-warning")}}
                {{form.page_refresh(class="btn btn-info")}}
                {{ form.ready_for_merge(class="btn btn-success",hidden='true', id='form-merge') }}
                <button type="button" class="btn btn-success" data-toggle="modal" data-target="#mergeModal">
                    Merge PR
                </button>
        </form>
            
            <div class="modal fade" id="mergeModal" tabindex="-1" role="dialog" aria-labelledby="mergeModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="mergeModalTitle">Merge PR</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <b>Are you sure , you want to Merge PR ?</b>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" id="merge_button" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-success" id="modal-merge">Proceed</button>
                        </div>
                    </div>
                </div>

        </div>
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">Merge is in Progress / No PRs to display <br> PR status will be displayed on this page once its available .</h3>
            <form method="POST">
                {{form.csrf_token()}}
                {{ form.pr_url(value="null") }}
                {{ form.merge_home(class="btn btn-info")}}
                {{ form.refresh_pr(class="btn btn-warning")}}
                {{form.page_refresh(class="btn btn-info")}}
            </form>
        {% endif %}
</div>


<script>
    $('#modal-merge').click(function(){
        // Perform the action after modal confirm button is clicked.
        document.getElementById("merge_button").disabled = true;
        document.getElementById("modal-merge").disabled = true;
        $('#form-merge').click(); // submitting the form
    });
</script>

{% endblock %}
