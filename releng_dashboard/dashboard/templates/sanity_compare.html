{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
setTimeout(function(){
   window.location.reload(1);
}, 30000);

</script>

{% set smi_product_list = ['base-vm','cee','deployer','base-bm','cee-bm','deployer-bm'] %}
{% set common_product_list = ['app-infra','golang-lib','cnee'] %}

{% if qa_build_info %}
    <div class="fw-body" >
    			<div class="content">
            <br>
            <br>
        <h3 style="text-align:left">Generate Sanity comparision report for {{product.upper()}}</h3>
                    <br>
        <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
        <thead class="table-info">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th class="th-sm" scope="col">QA Build Number</th>
            <!--th class="th-sm" scope="col">Triggered By</th-->
            <th class="th-sm"scope="col">Branch</th>
            <!--th class="th-sm" scope="col">Images</th-->
            <th class="th-sm" scope="col">Select?</th>
            <!--th class="th-sm" scope="col">Test Reports: Pass %</th-->
        </tr>
        </thead>
        <tbody>
        {% for build in qa_build_info %}
            <tr style="text-align:center">
                <td> {{ build["build_number"] }}</td>
                <td> {{ build["branch_name"] }}</td>
                <!--td> {{ build.build_start_time }} </td>
               < {% if build.triggered_by == "cnblds" %}
                    <td class="text-secondary">scheduled run</td>
                {% else %}
                    <td><a class="text-info" href=https://directory.cisco.com/dir/reports/{{ build.triggered_by }} }}>{{ build.triggered_by }}</a></td>
                {% endif %} >
                {% if build.build_status == "Passed" %}
                    <td> {{ build.branches_branch_name }} <br> <b class="text-success"> {{ build.build_number }}</b></td>
                {% elif build.build_status == "Failed" %}
                    <td> {{ build.branches_branch_name }} <br> <b class="text-danger"> {{ build.build_number }}</b></td>
                {% else %}
                    <td> {{ build.branches_branch_name }} <br> <b class="text-warning"> {{ build.build_number }}</b></td>
                {% endif %}
                {% if build.build_status == "In_Progress" %}
                    <td>{{ build.build_status }}</td>
                {% else %}
                    < Images >
                    <td>
                        {% if build.int_artifactory %}
                            <a href = {{ build.int_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }} </a>
                        {% else %}
                            NA
                        {% endif %}
                        <br>
                        {% if build.off_artifactory %}
                            <a href = {{ build.off_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }}-offline </a>
                        {% else %}
                            NA
                        {% endif %}
                    </td>
                {% endif %} -->
                <td>
                    <input type="checkbox">
                </td>
                <!-- Reports >
                <td>
                    {% if build["loc_report"] %}
                        <a href = {{ build.loc_report }} target="_blank">LOC</a>
                    {% else %}
                        NA
                    {% endif %}
                <br>
                    {% if build.change_log %}
                        <a href = {{ build.change_log }} target="_blank">Change log</a>
                    {% else %}
                        NA
                    {% endif %}
                </td>
                <Test Reports >
                <td>
                    {% if build["wrt_report"] %}
                        {% if build["ivt_passed_percentage"] %}
                            <a href = {{ build.wrt_report }} target="_blank"> IVT : {{ build.ivt_passed_percentage }}%
                                {% if build["ivt_approved"] == "yes" %}
                                <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="IVT APPROVED"> <i class="fas fa-check"></i> </button></a>
                            {% endif %}
                        {% else %}
                            <a href = {{ build.wrt_report }} target="_blank"> IVT</a>
                        {% endif %}
                    {% else %}
                        <ln class="text-secondary">IVT : NA</ln>
                    {% endif %}
                    {% if build["test_info"] %}
                        {% for job_type, job_status in build["test_info"].items() %}
                            <br>
                            {% if job_status == "IN_PROGRESS" %}
                                <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                                <div class="spinner-border spinner-border-sm text-warning"></div>
                            {% elif job_status == "PARTIALLY_COMPLETE" %}
                                <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                            {% else %}
                                <a href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }} : {{ job_status }}% </a>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    <br>
                </td -->
                <!-- enable this when promotion is supported from dashboard
                {% if build["dh_promotion_status"] == "N" %}
                    <--this needs to changed when devhub deployment is automated ->
                    {% if user_role == "admin-dummy" %}
                        <td>
                            <!- loop.index is the default counter which is offered ->
                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#promote{{ loop.index }}">Promote</button>
                            <div class="modal fade" id="promote{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="exampleModalCenterTitle">Promote {{ build.build_number }} to? </h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form method="POST">
                                            {{form.csrf_token()}}
                                            <div class="modal-body">
                                                {{ form.qa_build_no(value=build.build_number) }}
                                                {{ form.branch(value=build.branches_branch_name) }}
                                                {{form.promote_dh(class="btn btn-info")}}
                                                {{form.promote_cco(class="btn btn-warning")}}
                                            </div>
                                        </form>
                                        <div class="modal-footer">
                                            <h6>Caution: Promotion will start once you click</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    {% elif build["ivt_approved"] == "yes" %}
                        <td>
                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#promote">Promote</button>
                            <div class="modal fade" id="promote" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Insufficient privilege </h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Build Promotion can be done only by Releng Team.</p>
                                            <p>Please raise an RER.</p>
                                            <a href="https://rtp-mitg7-gnats.cisco.com:7443/cgi-bin/5G-relengreq.pl?database=5G-Releng-Requests">link</a>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    {% else %}
                        <td></td>
                    {% endif %}
                {% elif build["dh_promotion_status"] == "I" %}
                    <td>Promotion in Progress</td>
                {% else %}
                    <td>Promoted</td>
                {% endif %} -->
            </tr>

        {% endfor %}
        </tbody>
        </table>
                    
        </div>
        {{ form.submit(class="btn btn-danger",hidden='true', id='form-submit') }}
        <button type="button" class="btn btn-info" data-toggle="modal" data-target="#editModal"> Compare builds </button>
    {% endif %}

{% endblock %}
