{% extends 'product_base.html' %}
{% block home %}
{% set common_product_list = ['app-infra','golang-lib','cnee','lbs-libraries'] %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Generate LOC Report</h1>
                <p class="lead"> This will generate LOC Report between two QA Builds or
                    Dates for current and dependent orgs.</p>
            </div>
        </div>
            <div class="form-group row">
                <div class="col-sm-10">
                    <input type="radio" name="loc_type" value="D"> LOC Between Dates</input>
                    <small class="form-text text-muted">
                        Works for any branch. Good for finding LOC for features branch.
                    </small>
                </div >
            </div>
            <div class="form-group row">
                <div class="col-sm-10">
                    <input type="radio" name="loc_type" value="U"> LOC By User</input>
                    <small class="form-text text-muted">
                        Works for any branch. Good for finding LOC by a User.
                    </small>
                </div >
            </div>
            <div class="form-group row">
                <div class="col-sm-10">
                <input type="radio" name="loc_type" value="Q"> LOC Between QA Builds</input>
                    <small class="form-text text-muted">
                        Works only for releng managed branches.
                        Good for finding LOC for a release as well.
                    </small>
                </div>
            </div>

            <div class="form-group row" >
                <div class="col-sm-10">
                    {{form.submit(class="btn btn-info", style = "width:10%")}}
                </div>
            </div>
    </form>
</div>

{% endblock %}
