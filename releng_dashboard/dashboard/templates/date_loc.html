{% extends 'product_base.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Generate LOC Report</h1>
                <p class="lead"> This will generate LOC Report between two Dates for current and dependent orgs.</p>
            </div>
        </div>
        <br>

        <div class="form-group row">
            <label for="branch" class="col-md-2 control-label" style="text-align:left;"> Branch Name* :</label>
            <div class="col-sm-10">  {{ form.branch(size=20) }} </div>
        </div>

        <div class="form-group row">
            <label for="from_date" class="col-md-2 control-label" style="text-align:left;"> Start Date* :</label>
            <div class="col-sm-10">  {{ form.from_date(class='datepicker') }} </div>
        </div>

        <div class="form-group row">
            <label for="to_date" class="col-md-2 control-label" style="text-align:left;"> End Date* :</label>
            <div class="col-sm-10">  {{ form.to_date(class='datepicker') }} </div>
        </div>


        <div class="form-group row">
            <label for="recp_list" class="col-md-2 control-label" style="text-align:left;"> Addtional Recipient List :</label>
            <div class="col-sm-10">
                {{ form.recp_list }}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma separated cec ids
                </small>
            </div>
            <!--
            <label for="recp_list" class="col-md-2 control-label" style="text-align:left;"> {Comma seperated cec ids} </label>
            -->
        </div>

        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
        </div>
    </form>
</div>

{% endblock %}
