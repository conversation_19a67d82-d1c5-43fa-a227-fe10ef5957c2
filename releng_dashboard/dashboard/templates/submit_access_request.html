{% extends 'access_control_home.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Submit Access Request</h1>
                <p class="lead"> This will create github access request</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="product_name" class="col-sm-2 col-form-label">Github Organization</label>
            <div class="col-sm-10">
                {{ product_org }}
            </div>
        </div>

        <div class="form-group row">
            <label for="userid" class="col-md-2 control-label" style="text-align:left;">CEC ID<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.userid }}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma separated cec ids
                </small>
            </div>
        </div>

        <div class="form-group row">
            <label for="repo_level" class="col-md-2 control-label" style="text-align:left;">Repositories<font color="red"> *</font></label>
            <div class="col-sm-10" disabled="false">  {{ form.select_all_repos(onchange="repo_list_check()") }} All Repos <br>
	          	<select multiple name="repo_list" id="repo_list_selected" method="GET" disabled="false" action="/">
	              {% for repo in all_repos %}
	               <option name="repo_list" value="{{repo}}">{{repo}}</option>
	              {% endfor %}
	            </select>
	            <small id="HelpBlock" class="form-text text-muted">
	                To select multiple items in a list, hold down the Ctrl (PC) or Command (Mac) key. Then click on your desired items to select.
	            </small>
							</div>
        </div>
        <script>
          function repo_list_check(){
            var checked = document.getElementById('select_all_repos').checked
            if (checked){
              //document.getElementById('repos').disabled = false
              document.getElementById('repo_list_selected').disabled = true
            } else {
              //document.getElementById('repos').disabled = true
              document.getElementById('repo_list_selected').disabled = false
            }
          }
          repo_list_check()
        </script>

        <div class="form-group row" id="repo_list">
            <label for="justification" class="col-sm-2 col-form-label" style="text-align:left;">{{ form.justification.label }}<font color="red"> *</font></label>
            <div class="col-sm-10"> {{ form.justification(required='required')}} </div>
        </div>


        <div class="form-group row">
            <label for="type_id" class="col-sm-2 col-form-label"> Access Type<font color="red"> *</font></label>
            <div class="col-sm-10">
                {{form.type_id(id="type_id")}}
            </div>
        </div>
        {% if user_role == "admin"  %}
                <div class="form-group row">
                    <label for="category_id" class="col-sm-2 col-form-label"> Action :</label>
                    <div class="col-sm-10">
                        {{form.category_id(id="category_id")}}
                    </div>
                </div>
        {% endif %}


        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
        </div>
        </form>
</div>

{% endblock %}
