{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
$(document).ready(
function() {
$('#pull_request').DataTable({
        "order": [],
        "pageLength": 5
    });
});

</script>

<script>
$(document).ready(function(){
  $('[data-toggle="popover"]').popover();
});
$(document).ready(function(){
  $('[data-toggle="tooltip"]').tooltip()
});
</script>

<div class="col-xs-6 col-sm-10">
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-4">Smoke Test Status for {{product.upper()}}</h1>
            <p> This page can be used for monitoring, triggering & aborting smoke run. If you are an admin, you may also inc/dec the priority of PR.</p>
        </div>
    </div>
    <table id="pull_request" class="table table-striped table-bordered">
        <thead class="table-primary">
        <!-- limit to col width-->
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col" style="width: 10%">PR#<br>Repository</th>
            <th scope="col" style="width: 25%">Author <br> Source ->Destination</th>
            <th scope="col" style="width: 5%">CDETS</th>
            <th scope="col" style="width: 5%">Smoke Priority</th>
            <th scope="col" style="width: 33%">Pre Commit CI Status</th>
            <th scope="col" style="width: 12%">Actions</th>
        </tr>
        </thead>
        <tbody>
        {% for pr in pr_info %}
        <tr style="text-align:center;">
            <td>
                <a href={{ pr.pr_url }}>#{{ pr.pr_number }}</a>
                <br>
                <a href=https://wwwin-github.cisco.com/{{ product_org }}/{{ pr.repository }}>{{ pr.repository }}</a>
            </td>
            <td><a href=https://directory.cisco.com/dir/reports/{{ pr.pr_author }}>{{ pr.pr_author }}</a> <br>
                {{ pr.pr_from_branch }} <i class="fas fa-long-arrow-alt-right"></i> {{ pr.pr_to_branch }}
            </td>
            <td>
                {% if pr.cdets %}
                    {% for id in pr.cdets.split(",") %}
                        <a href=https://cdetsng.cisco.com/webui/#view={{ id }}>{{ id }}</a>
                        <br>
                    {% endfor %}
                {% endif %}
            </td>
            <td>
                {% if pr.pr_to_branch in supported_branches %}
                    {{ pr.smoke_priority }}
                {% endif %}
            </td>
            <!-- why two if statement -->
            {% if pr.smoke_status %}
                <td>
                    {% if pr.smoke_pre_check_url %}
                        <a href={{ pr.smoke_pre_check_url }}>Pre Check/Init</a>
                    {% else %}
                        Pre Check/Init
                    {% endif %}
                    {% if pr.smoke_pre_check %}
                        {% if pr.smoke_pre_check == "success" %}
                            <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="Pre-Checks are successful"> <i class="fas fa-check"></i> </button>
                        {% elif pr.smoke_pre_check == "failure" %}
                            <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="Pre-Checks have Failed" data-content="{{ pr.smoke_pre_check_error }}"><i class="fas fa-times"></i></button>
                        {% elif pr.smoke_pre_check == "in-progress" %}
                            <button type="button" class="btn spinner-grow spinner-grow-sm text-warning" data-toggle="tooltip" data-placement="top" title="Pre-Checks is running"></button>
                        {% else %}
                            <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="There is a bug. Plz contact releng team"><i class="fas fa-bug"></i></button>
                        {% endif %}
                    {% endif %}

                    <i class="fas fa-angle-right"></i>
                    {% if pr.smoke_product_build_url %}
                        <a href={{ pr.smoke_product_build_url }}>Product Build</a>
                    {% else %}
                        Product Build
                    {% endif %}

                    {% if pr.smoke_product_build %}
                        {% if pr.smoke_product_build == "success" %}
                            <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="Product build is successful"> <i class="fas fa-check"></i> </button>
                        {% elif pr.smoke_product_build == "failure" %}
                            <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="Product build failed" data-content="{{ pr.smoke_product_build_error }}"><i class="fas fa-times"></i></button>
                        {% elif pr.smoke_product_build == "in-progress" %}
                            <button type="button" class="btn spinner-grow spinner-grow-sm text-warning" data-toggle="tooltip" data-placement="top" title="Product build is running"></button>
                        {% else %}
                            <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="There is a bug. Plz contact releng team"><i class="fas fa-bug"></i></button>
                        {% endif %}
                    {% endif %}
                    <i class="fas fa-angle-right"></i>
                    {% if pr.smoke_test_run_url %}
                        <a href={{ pr.smoke_test_run_url }}>Smoke Run</a>
                    {% else %}
                        Smoke Run
                    {% endif %}
                    {% if pr.smoke_test_run %}
                        {% if pr.smoke_test_run == "success" %}
                            <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="Smoke Test is successful"> <i class="fas fa-check"></i> </button>
                        {% elif pr.smoke_test_run == "failure" %}
                            <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="Smoke Test have Failed" data-content="{{ pr.smoke_test_error }}"><i class="fas fa-times"></i></button>
                        {% elif pr.smoke_test_run == "in-progress" %}
                            <button type="button" class="btn spinner-grow spinner-grow-sm text-warning" data-toggle="tooltip" data-placement="top" title="Smoke Test is running"></button>
                        {% elif pr.smoke_test_run == "in-queue" %}
                            <button type="button" class="btn btn-outline-info btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="In Queue" data-content="Waiting for cluster to be allocated"><i class="fas fa-sliders-h"></i></button>
                        {% elif pr.smoke_test_run == "error" %}
                            <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="Error"><i class="fas fa-exclamation-circle"></i></button>
                        {% else %}
                            <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="popover" data-placement="top" data-trigger="focus" title="There is a bug. Plz contact releng team"><i class="fas fa-bug"></i></button>
                        {% endif %}
                    {% endif %}
                    {% if pr.smoke_pre_check_error %}
                        <p style="color:red;">Error : {{ pr.smoke_pre_check_error }}</p>
                    {% endif %}
                    {% if pr.smoke_product_build_error %}
                        <p style="color:red;">Error : {{ pr.smoke_product_build_error }}</p>
                    {% endif %}
                    {% if pr.smoke_test_error %}
                        <p style="color:red;">Error : {{ pr.smoke_test_error }}</p>
                    {% endif %}
                    <!--
                    {% if pr.smoke_status in ['in-progress'] %}
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width:50%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">50%</div>
                        </div>
                    {% endif %}-->
                </td>
            {% else %}
                <td></td>
            {% endif %}
            {% if pr.pr_to_branch in supported_branches %}
                <td>
                    {% if not pr.smoke_status in ['in-progress', 'in-queue', 'success'] %}
                        <button type="button" class="btn btn-outline-success" data-toggle="modal" data-target="#run{{ loop.index }}" title="Start Smoke"><i class="fas fa-play-circle"></i></button>
                        <div class="modal fade" id="run{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h6 class="modal-title" id="run">Are you sure you want to run smoke for feature branch :  {{ pr.pr_from_branch }} </h6>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form method="POST">
                                            {{form.csrf_token()}}
                                            <div>
                                                {{ form.pr_number(value=pr.pr_number) }}
                                                {{ form.repository(value=pr.repository) }}
                                                {{ form.pr_from_branch(value=pr.pr_from_branch) }}
                                                {{ form.pr_to_branch(value=pr.pr_to_branch) }}
                                                {{ form.pr_smoke_priority(value=pr.smoke_priority) }}
                                                {{ form.start_smoke(class="btn btn-success"  )}}

                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <h6></h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    {% if pr.smoke_status in ['in-progress', 'in-queue'] %}
                        <button type="button" class="btn btn-outline-danger" data-toggle="modal" data-target="#abort{{ loop.index }}" title="Abort Smoke"><i class="fas fa-ban"></i></button>
                        <div class="modal fade" id="abort{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h6 class="modal-title" id="abort">Are you sure you want to abort smoke for feature branch :  {{ pr.pr_from_branch }} </h6>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form method="POST">
                                            {{form.csrf_token()}}
                                            <div>
                                                {{ form.pr_number(value=pr.pr_number) }}
                                                {{ form.repository(value=pr.repository) }}
                                                {{ form.pr_from_branch(value=pr.pr_from_branch) }}
                                                {{ form.pr_to_branch(value=pr.pr_to_branch) }}
                                                {{ form.pr_smoke_priority(value=pr.smoke_priority) }}
                                                {{ form.abort_smoke(class="btn btn-danger ")}}
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <h6></h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    {% if not pr.smoke_test_run in ['in-progress'] %}
                        {% if user_role in ['admin'] %}
                            {% if not pr.smoke_priority in ['critical'] %}
                                <button type="button" class="btn btn-outline-info" data-toggle="modal" data-target="#inc{{ loop.index }}" title="Increase Priority"><i class="fas fa-arrow-circle-up"></i></button>
                                <div class="modal fade" id="inc{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h6 class="modal-title" id="inc">Are you sure you want to Inc Priority for feature branch :  {{ pr.pr_from_branch }} </h6>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form method="POST">
                                                    {{form.csrf_token()}}
                                                    <div>
                                                        {{ form.pr_number(value=pr.pr_number) }}
                                                        {{ form.repository(value=pr.repository) }}
                                                        {{ form.pr_from_branch(value=pr.pr_from_branch) }}
                                                        {{ form.pr_to_branch(value=pr.pr_to_branch) }}
                                                        {{ form.pr_smoke_priority(value=pr.smoke_priority) }}
                                                        {{form.inc_priority(class="btn btn-success")}}
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <h6></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        {% endif %}

                        {% if not pr.smoke_priority in ['low'] %}
                            <button type="button" class="btn btn-outline-secondary" data-toggle="modal" data-target="#dec{{ loop.index }}" title="Decrease Priority"><i class="fas fa-arrow-circle-down"></i></button>
                            <div class="modal fade" id="dec{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h6 class="modal-title" id="dec">Are you sure you want to Dec Priority for feature branch :  {{ pr.pr_from_branch }} </h6>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <form method="POST">
                                                {{form.csrf_token()}}
                                                <div>
                                                    {{ form.pr_number(value=pr.pr_number) }}
                                                    {{ form.repository(value=pr.repository) }}
                                                    {{ form.pr_from_branch(value=pr.pr_from_branch) }}
                                                    {{ form.pr_to_branch(value=pr.pr_to_branch) }}
                                                    {{ form.pr_smoke_priority(value=pr.smoke_priority) }}
                                                    {{form.dec_priority(class="btn btn-secondary")}}
                                                </div>
                                            </form>
                                        </div>
                                        <div class="modal-footer">
                                            <h6></h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}
                </td>
            {% else %}
                <td></td>
            {% endif %}
        </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}


