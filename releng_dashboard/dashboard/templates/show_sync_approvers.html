{% extends 'product_base.html' %}
{% block home %}


<div class="container">
    <br>
    <p class="h3"> Sync request approvers for {{product}} </p>
    <br>
    <table id="approvers" class="table table-striped table-bordered" style="width: 30%">
        <thead class="table-primary">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col">User Name</th>
            <th scope="col">Email</th>
        </tr>
        </thead>
        <tbody>
        {% for tt_approver in tt_approver_list %}
        <tr>
            <td><a href=https://directory.cisco.com/dir/reports/{{ tt_approver.users_cec_id }}>{{ tt_approver.users_cec_id }}</a></td>
            <td>{{tt_approver['users_cec_email']}}</td>
        </tr>
        {% endfor %}
        </tbody>
    </table>
    <br>
</div>
{% endblock %}
