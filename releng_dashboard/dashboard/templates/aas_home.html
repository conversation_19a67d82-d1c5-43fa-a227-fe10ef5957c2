{% extends 'base.html' %}
{% block content %}

<div id="carouselExampleInterval" class="carousel slide" data-ride="carousel">
  <div class="carousel-inner">
    <div class="carousel-item active" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Download offline & online image.</h1>
          <p>Go to Product home page to know more</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Trigger QA Build for your Product & know it's progress</h1>
          <p>Go to product home page -> QA Build & Release -> Trigger QA build</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Know about past Released Builds</h1>
          <p>Go to product home page -> Released Builds</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>All Test reports at one place</h1>
          <p>Go to product home page -> Quality Statistics</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Create and Approve throttle requests</h1>
          <p>Go to product home page -> Branch Operations -> Throttle tracker</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Delete Stale Github branches</h1>
          <p>Go to product home page -> Operations to delete stale branches</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Github Access Management</h1>
          <p>Go to product home page -> Github Operations -> Access Requests </p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Generate LOC Report</h1>
          <p>Between QA builds or between dates</p>
        </div>
      </div>
    </div>
  </div>
  <a class="carousel-control-prev" href="#carouselExampleInterval" role="button" data-slide="prev">
    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
    <span class="sr-only">Previous</span>
  </a>
  <a class="carousel-control-next" href="#carouselExampleInterval" role="button" data-slide="next">
    <span class="carousel-control-next-icon" aria-hidden="true"></span>
    <span class="sr-only">Next</span>
  </a>
</div>

<nav class="navbar navbar-expand-lg navbar-dark" style="background-color:#000000;">
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
    aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>

  <div class="collapse navbar-collapse" id="navbarSupportedContent">
    <ul class="navbar-nav">
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="false">
          <font color="white" size="4">On-Prem</font>
        </a>
        <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
          {% for branch in branches %}
          <a class="dropdown-item" href="/branch/{{ branch }}">{{ branch }}</a>
          {% endfor %}
        </div>
      </li>
      <!--div class="collapse navbar-collapse" id="navbarSupportedContent">
  <ul class="navbar-nav"-->
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="false">
          <font color="white" size="4">As a Service</font>
        </a>
        <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
          {% for aas_product in aas_products %}
          <a class="dropdown-item" href="/aas_products/{{ aas_product }}">{{ aas_product }}</a>
          {% endfor %}
        </div>
      </li>
    </ul>
  </div>
</nav>

<div class="container-fluid">
  <br>
  <br>
  <div class="row">
    <div class="col"></div>
    <div class="col-8">
      <br>
      <h2 style="text-align:center" class="featurette-heading">{{ aas_product }} Test Execution Summary : {{ aas_branch
        }}</h2></br>
      <style>
        table {
          border-collapse: collapse;
          width: 100%;
        }

        th.thick-border,
        td.thick-border {
          border-right: 3px solid #56C1A1;
        }
      </style>
      <table id="qa_build" class="table table-striped table-bordered" style="border: 1px solid black; width:100%;">
        <colgroup>
          <col>
        </colgroup>
        <colgroup span="6"></colgroup>
        <colgroup span="6"></colgroup>
        <tbody>
          <tr style="text-align:center; background-color:#56C1A1; color:white">
            <th colspan="7" scope="colgroup" style="text-align:center; border:3px">Functional Test Results</th>
          </tr>
          <tr>
            <th rowspan="2" width="100" style="text-align:center">Product</th>
            <th rowspan="2" scope="colgroup" style="text-align:center">Date</th>
            <th rowspan="2" scope="colgroup" style="text-align:center">Build Number</th>
            <th colspan="2" scope="colgroup" style="text-align:center">Sanity</th>
            <th colspan="2" scope="colgroup" style="text-align:center">Regression</th>

          </tr>
          <tr>

            <th width="100" scope="col">Total TC</th>
            <th width="100" scope="col">Pass %</th>
            <th width="100" scope="col">Total TC</th>
            <th width="100" scope="col">Pass %</th>
          </tr>
          {% for data in tests_info %}
          <tr>
            {% if 'product' in data %}
            <td rowspan="3">{{ data['product'] }}</td>
            {% endif %}
            <td>{{ data['build_start_time'] }}</td>
            <td>{{ data['build_number'] }}</td>

            {% if data['sanity_total_TC'] <= 0 %} <td>
              </td>
              <td></td>
              {% else %}
              <td>{{ data['sanity_total_TC'] }}</td>
              {% if data['sanity_passed_percentage'] < 80 %} <td style="color:red;">{{
                data['sanity_passed_percentage']}}%</td>
                {% else %}
                <td style="color:green;">{{ data['sanity_passed_percentage'] }}%</td>
                {% endif %}
                {% endif %}
                {% if data['reg_total_TC'] <= 0 %} <td>
                  </td>
                  {% else %}
                  <td>{{ data['reg_total_TC'] }}</td>
                  {% endif %}
                  {% if 'reg_passed_percentage' in data %}
                  {% if data['reg_passed_percentage'] <= 0 %} <td>
                    </td>
                    {% elif data['reg_passed_percentage'] < 80 %} <td style="color:red;">{{data['reg_passed_percentage']
                      }}%</td>
                      {% else %}
                      <td style="color:green;">{{ data['reg_passed_percentage'] }}%</td>
                      {% endif %}
                      {% else %}
                      <td></td>
                      {% endif %}
          </tr>
          {% endfor %}


        </tbody>
      </table>
    </div>
    <div class="col"></div>
  </div>

  <div class="container-fluid">
    <br>
    <col>
    <div class="row">
      <div class="col"></div>
      <div class="col-10">
        <br>
        <table id="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:80%;">
          <colgroup span="2"></colgroup>
          <colgroup span="2"></colgroup>
          <tr style="text-align:center; background-color:#56C1A1; color:white">
            <th colspan="7" scope="colgroup" style="text-align:center; border:3px">{{ aas_product }} System Test Results
            </th>
          </tr>
          <tr>
            <th rowspan="2" width="150" style="text-align:center">Test Date</th>
            <th rowspan="2" width="130" style="text-align:center">Build Number</th>
            <th rowspan="2" scope="colgroup" style="text-align:center">NF Details</th>
            <th colspan="2" scope="colgroup" style="text-align:center">Sanity</th>
            <th colspan="2" scope="colgroup" style="text-align:center">Longevity</th>
          </tr>
          <tr>
            <th width="100" scope="col">Total TC</th>
            <th width="100" scope="col">Pass %</th>
            <th width="100" scope="col">Total TC</th>
            <th width="100" scope="col">Pass %</th>
          </tr>
          {% for data in st_tests_info %}
          <tr>
            {% if data['sanity_start_time'] %}
            <td>{{ data['sanity_start_time'] }}</td>
            {% else %}
            <td>{{ data['regression_start_time'] }}</td>
            {% endif %}
            <td>{{ data['branch'] }}</td>
            <td>
              {% for nf_data in data['nf_details'] %}
              {{nf_data}}
              </br>
              {% endfor %}
            </td>

            {% if not data['sanity_total_TC'] %}
            <td></td>
            <td></td>
            {% else %}
            <td>{{ data['sanity_total_TC'] }}</td>
            {% if data['sanity_passed_percentage'] < 80 %} <td style="color:red;">{{
              data['sanity_passed_percentage']}}%</td>
              {% else %}
              <td style="color:green;">{{ data['sanity_passed_percentage'] }}%</td>
              {% endif %}
              {% endif %}

              {% if not data['reg_total_TC'] %}
              <td></td>
              <td></td>
              {% else %}
              <td>{{ data['reg_total_TC'] }}</td>
              {% if 'reg_passed_percentage' in data %}
              {% if data['reg_passed_percentage'] < 80 %} <td style="color:red;">{{data['reg_passed_percentage']}}%</td>
                {% else %}
                <td style="color:green;">{{ data['reg_passed_percentage'] }}%</td>
                {% endif %}
                {% endif %}
                {% endif %}
          </tr>
          {% endfor %}
        </table>
      </div>
    </div>
  </div>
</div>
</br>
</br>
</br>


<footer class="container">
  <p class="float-right"><a href="#">Back to top</a></p>
  <p>&copy; RelEng MABU</p>
</footer>

{% endblock %}