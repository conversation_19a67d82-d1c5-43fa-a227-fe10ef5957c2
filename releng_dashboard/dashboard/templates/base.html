<!DOCTYPE html>
<html lang="en">
<head>
    <title>5G RelEng Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>
    <link rel="shortcut icon" href="../static/5G_favicon.ico" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
    <script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark " style="background-color:#000000;">
    <a class="navbar-brand" href="/home">
        <img src="/static/cisco_logo.gif" width="70" height="40">
    </a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav">
            {% if username %}
            <li class="nav-item" >
                <a class="nav-link" href="/amf"><font color="white" size="4">AMF</font></a>
            </li>
            <li class="nav-item" >
                <a class="nav-link" href="/bng"><font color="white" size="4">BNG</font></a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <font color="white" size="4">CCG</font>
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                    <a class="dropdown-item" href="/ccg">CN</a>
                    <a class="dropdown-item" href="/smf">SMF</a>
                    <a class="dropdown-item" href="/sgw">SGW</a>
                </div>
            </li>
            <li class="nav-item" >
                <a class="nav-link" href="/chf"><font color="white" size="4">CHF</font></a>
            </li>
	    <li class="nav-item">
                <a class="nav-link" href="/cni"><font color="white" size="4">CNI</font></a>
            </li>
            <li class="nav-item" >
                <a class="nav-link" href="/cnvpc"><font color="white" size="4">cnVPC</font></a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <font color="white" size="4">COMMON</font>
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                    <a class="dropdown-item" href="/app-infra">app-infra</a>
                    <a class="dropdown-item" href="/golang-lib">golang-lib</a>
                    <a class="dropdown-item" href="/cnee">infrastructure</a>
                </div>
            </li>
            <li class="nav-item" >
                <a class="nav-link" href="/dvpc"><font color="white" size="4">DVPC</font></a>
	    </li>
	    <li class="nav-item" >
                <a class="nav-link" href="/kpm"><font color="white" size="4">KPM</font></a>
            </li>
            <li class="nav-item" >
                <a class="nav-link" href="/lfs"><font color="white" size="4">LFS</font></a>
            </li>
	    <li class="nav-item" >
                <a class="nav-link" href="/nrf"><font color="white" size="4">NRF</font></a>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuL" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <font color="white" size="4">NSO-SMI</font>
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                    <a class="dropdown-item" href="/smi-nso">smi-nso-cfp</a>
                    <a class="dropdown-item" href="/up-recovery">up-recovery</a>
                </div>
            </li>

            <li class="nav-item" >
                <a class="nav-link" href="/pats"><font color="white" size="4">PATS</font></a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <font color="white" size="4">POLICY</font>
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                    <a class="dropdown-item" href="/cpc">CPC</a>
                    <a class="dropdown-item" href="/pcf">PCF</a>
                </div>
            </li>	
            <li class="nav-item" >
                <a class="nav-link" href="/rcm"><font color="white" size="4">RCM</font></a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenu" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <font color="white" size="4">SMI-CNDP</font>
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                    <a class="dropdown-item" href="/base-bm">base-bm</a>
                    <a class="dropdown-item" href="/cee-bm">cee-bm</a>
                    <a class="dropdown-item" href="/deployer-bm">deployer-bm</a>
                </div>
            </li>
            <li class="nav-item" >
                <a class="nav-link" href="/staros"><font color="white" size="4">STAROS</font></a>
            </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                        <font color="white" size="4">ULB</font>
                    </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                            <a class="dropdown-item" href="/ulb">lbs-apps</a>
                            <a class="dropdown-item" href="/lbs-libraries">lbs-libraries</a>
                        </div>
            </li>
            <li class="nav-item" >
                <a class="nav-link" href="/upf"><font color="white" size="4">UPF</font></a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="inactiveOrgsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <font color="white" size="4">Inactive Orgs</font>
                </a>
                <div class="dropdown-menu" aria-labelledby="inactiveOrgsDropdown">
                    <a class="dropdown-item" href="/pgw">PGW</a>
                    <a class="dropdown-item" href="/sepp">SEPP</a>
                </div>
            </li>

            {% endif %}
        </ul>

        <ul class="navbar-nav ml-auto">
            {% if username %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <font color="white" size="4">User Guide</font>
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                        <a class="dropdown-item" href="https://wiki.cisco.com/display/MITG/Pre-Commit+CI+User+Guide">Pre Commit CI</a>
                        <a class="dropdown-item" href="https://wiki.cisco.com/display/MITG/Throttle+Tracker+user+guide">Throttle Tracker</a>
                        <a class="dropdown-item" href="https://wiki.cisco.com/display/MITG/07_REST+API">REST API Supported</a>
                    </div>
                </li>
            {% endif %}
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <font color="white" size="4">Contact Us</font>
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                    <a class="dropdown-item" href="mailto:<EMAIL>">Email</a>
                    <a class="dropdown-item" href="https://rtp-mitg7-gnats.cisco.com:7443/cgi-bin/5G-relengreq.pl?database=5G-Releng-Requests">RER</a>
                </div>
            </li>
	     {% if username %}
	         <li class="nav-item" >
		     <a class="nav-link" href="/home"><span class="glyphicon glyphicon-log-out"></span><font color="white" size="4">{{ username }}</font></a>
		 </li>
	      {% endif %}
        </ul>
    </div>
</nav>

{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{message}}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        {% endfor %}
    {% endif %}
{% endwith %}

{% block content %}
{% endblock %}

</body>
</html>
