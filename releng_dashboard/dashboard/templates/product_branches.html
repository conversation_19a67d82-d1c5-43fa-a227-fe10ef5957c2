{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#qa_build').DataTable({
        "order": [],
        "pageLength": 5
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
    {% if branch_info %}
        <div class="fw-body" >
            <div class="content">
            <br>
            <br>
                {% if show_active_button %}
                    <h3 style="text-align:left">Branches for {{product.upper()}} - Active & Inactive</h3>
                {% else %}
                    <h3 style="text-align:left">Active branches for {{product.upper()}}</h3>
                {% endif %}
                <br>
                <br>
            <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white;">
                        <th class="th-sm" scope="col" style="vertical-align:middle;">Branch Name ( Link to Builds )</th>
                        <th class="th-sm" scope="col" style="vertical-align:middle;">Branch Status</th>
                        <th class="th-sm" scope="col" style="vertical-align:middle;">Current Version</th>
                        <th class="th-sm" scope="col" style="vertical-align:middle; width: 33%">Description</th>
                        <th class="th-sm"scope="col" style="vertical-align:middle;">Parent Branch<br>Branch Point</th>
                        <th class="th-sm" scope="col" style="vertical-align:middle;">Branch Creation Date </th>
                        <th class="th-sm" scope="col" style="vertical-align:middle;">RER #</th>
                    </tr>
                </thead>
                <tbody>
                {% for branch in branch_info %}
                <tr style="text-align:center">
                    <td> <a href="/builds/{{product}}/{{branch.branch_name}}">{{ branch.branch_name }}</a> </td>
                    <td>{{ branch.branch_status }}</td>
                    <td> {{ branch.branch_version }} </td>
                    <td>{{ branch.branch_description }}</td>
                    <td> {{ branch.parent_branch }} <br>
                    {% if branch.parents_branch_point %}
                        {{ branch.parents_branch_point }}
                    {% endif %}
                    </td>
                    <td>{{ branch.branch_creation_date }}</td>
                    {% if branch.rer_number %}
                        {% if branch.pc_rer %}
                            <td>
                                <a href = https://rtp-mitg7-gnats.cisco.com:7443/cgi-bin/relengreq.pl?database=Releng-Requests&cmd=view+audit-trail&pr={{ branch.rer_number }}>RER # {{ branch.rer_number }}</a>
                            </td>
                        {% elif branch.rer_number %}
                            <td>
                                <a href = https://rtp-mitg7-gnats.cisco.com:7443/cgi-bin/5G-relengreq.pl?database=5G-Releng-Requests&cmd=view+audit-trail&pr={{ branch.rer_number }}>RER # {{ branch.rer_number }}</a>
                            </td>
                        {% endif %}
                    {% else %}
                        <td></td>
                    {% endif %}
                </tr>
                {% endfor %}
                </tbody>
            </table>
                <br>
                <form method="POST">
                    {{form.hidden_tag()}}
                    {% if show_active_button %}
                        {{form.submit_active(class="btn btn-info", style = "width:30%")}}
                    {% else %}
                        {{form.submit_all(class="btn btn-info", style = "width:30%")}}
                    {% endif %}
                </form>
        </div>
    {% endif %}
        </div>
</div>
{% endblock %}
