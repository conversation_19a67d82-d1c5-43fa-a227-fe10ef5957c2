{% extends 'product_base.html' %}
{% block home %}
<style>

</style>
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Access Management</h1>
                <p class="lead"> This will add users who do manage feature access of the organization</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="product_name" class="col-sm-2 col-form-label">ORGANIZATION :</label>
            <div class="col-sm-10">
                {{ product_org }}
            </div>
        </div>
        <div class="form-group row">
            <label for="user" class="col-sm-2 col-form-label" style="text-align:left;"> CEC ID :</label>
            <div class="col-sm-10">  {{ form.user(size=50) }} </div>
        </div>
        <div class="form-group row">
            <label for="role" class="col-sm-2 col-form-label"> ROLE :</label>
            <div class="col-sm-10">
                {% for roles in role_choices %}
                <div>
                    <input type="radio" id="{{roles}}" name="role" value="{{roles}}" onchange="checkClickFunc()" />
                    <label for="{{roles}}">{{roles}}</label>
                </div>
                {% endfor %}
            </div>
        </div>
        <div class="form-group row">
            <label for="repo_level" class="col-md-2 control-label" style="text-align:left;">REPOSITORIES :</label>
            <div class="col-sm-10" disabled="false">  {{ form.select_all_repos(onchange="repo_list_check()") }} All Repos <br>
                <select multiple name="repo_list" id="repo_list_selected" method="GET" disabled="false" action="/">
                    {% for repo in all_repos %}
                    <option name="repo_list" value="{{repo}}">{{repo}}</option>
                    {% endfor %}
                </select>
                <small id="HelpBlock" class="form-text text-muted">
                    To select multiple items in a list, hold down the Ctrl (PC) or Command (Mac) key. Then click on your desired items to select.<br>
                    For existing user, repos will be appended in case of update.
                </small>
            </div>
        </div>
        <script>


          function repo_list_check(){
            var checked = document.getElementById('select_all_repos').checked
            if (checked){
              //document.getElementById('repos').disabled = false
              document.getElementById('repo_list_selected').disabled = true
            } else {
              //document.getElementById('repos').disabled = true
              document.getElementById('repo_list_selected').disabled = false
            }
          }
          repo_list_check()
            function checkClickFunc()
                {
                 var select1 = document.getElementById('ORG_OWNER');
                 //var select2 = document.getElementById('DEV_MANAGER');
                 if (select1.checked == true)
                    {
                         document.getElementById('select_all_repos').checked = true
                         document.getElementById('select_all_repos').disabled = true
                         document.getElementById('repo_list_selected').disabled = true
                    }
                 else
                    {
                        document.getElementById('select_all_repos').checked = false
                        document.getElementById('select_all_repos').disabled = false
                        document.getElementById('repo_list_selected').disabled = false
                    }
                }
        </script>

        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
        </div>
        </form>
</div>

{% endblock %}
