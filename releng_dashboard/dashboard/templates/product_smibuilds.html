{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#smi_build').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
} );

</script>

<div class="col-xs-6 col-sm-9">
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-4">{{product.upper()}} Build Status</h1>
        </div>
    </div>
    {% if smi_build_info %}
        <div class="fw-body" >

            <table id ="smi_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
            <thead class="table-info">
            <tr style="text-align:center; background-color:#000000;color:white">
                <th class="th-sm"scope="col">Repo</th>
                <th class="th-sm" scope="col">Branch</th>
                <th class="th-sm" scope="col">Status</th>
                <th class="th-sm" scope="col">Job URL</th>
            </tr>
            </thead>
            <tbody>
            {% for build in smi_build_info %}
                <tr style="text-align:center">
                    <th scope="row">{{ build.repo }}
                    </th>
                    <td>{{ build.branch }}
                    </td>
                    <td><font color={{ build.font }}>{{ build.build_status }}</font></td>
                    <td>
                       <a href = {{ build.job_url }}/ target="_blank"> Job Url </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
            </table>
        </div>

    {% endif %}

{% endblock %}