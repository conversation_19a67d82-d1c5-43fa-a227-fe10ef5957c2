{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
    $(document).ready(function() {
    $('#MYID_ACCESS_REQUESTS').DataTable({
            "order": [[ 0, "desc" ]],
            "pageLength": 10
        });
    } );

</script>


<div class="col-xs-6 col-sm-9">
    <br>
    <div class="row">
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"> GitHub + Artifactory + Sonar Access </h5>
                    <p class="card-text">Submit access request to get access to below services<br>
                    <ul><li>Github</li></ul>
                    <ul><li>Artifactory</li></ul>
                    <ul><li>Sonar</li></ul>
                    Note: This option is usually used by Dev and QA teams </p>
                    <a href="/join_myid_group/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Artifactory Access Only </h5>
                    <p class="card-text">Submit access request to get access to below service<br>
                    <ul><li>Artifactory</li></ul>
                    Note: This request is usually placed to get access to download only artifacts and does not give access to GitHub nor Sonar </p>
                    <a href="/join_myid_artifactory/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Sonar Access Only </h5>
                    <p class="card-text">Submit access request to get access to below service<br>
                    <ul><li>Sonar</li></ul>
                    Note: This request is usually placed to get access to view sonar metrics only and does not give access to GitHub nor artifacts </p>
                    <a href="/join_myid_sonar/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
    </div>


<div class="fw-body" >
    <div class="content">
        <br>
        <br>
        {% if show_active_button %}
        <h3 style="text-align:left">All Access Requests for : {{product_org}}  </h3>
        {% else %}
        <h3 style="text-align:left">Pending Access Requests for : {{product_org}}  </h3>
        {% endif %}
        <br>


        <table id ="MYID_ACCESS_REQUESTS" class="table table-striped table-bordered" style="border: 1px solid gray;width:110%;">
            <thead class="table-info">
            <tr style="text-align:center; background-color:#000000;color:white">
                <th scope="col" style="width: 5%">ID</th>
                <th scope="col" style="width: 10%">CEC ID</th>
                <th scope="col" style="width: 10%">MYID Group Name</th>
                <th scope="col" style="width: 15%"> Submit Date  </th>
                <th scope="col" style="width: 15%"> Submitter  </th>
                <th scope="col" style="width: 15%">Justification </th>
                <th scope="col" style="width: 10%">Approver</th>
                <th scope="col" style="width: 10%">Actions </th>
                <th scope="col" style="width: 10%">Releng DB Status </th>
            </tr>
            </thead>
            <tbody>
            {% for request in record_list %}
            <tr style="text-align:center">
                <th scope="row">{{request['id']}}</th>
                <td><a href=https://directory.cisco.com/dir/reports/{{request['USERID']}}>{{request['USERID']}}</a></td>
                <td>{{request['MYID_GRP_NAME']}}</td>
                <td>{{request['SUBMIT_DATE']}}</td>
                <td>{{request['SUBMITTER']}}</td>
                <td>{{request['JUSTIFICATION']}}</td>
                <td> {% if request['STATUS'] in  ['Approved','Denied'] and request['APPROVER'] %}
                    {{ request['APPROVER'] }}
                    {% else %}
                    {% endif %}
                    <br>
                    {% if request['STATUS'] in  ['Pending'] and (user_role == "ORG_OWNER" or user_role == "RELENG") %}
                <td>
                <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#edit{{ loop.index }}" title="approve"><i class="fas fa-edit"></i></button>
                    <div class="modal fade" id="edit{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h6 class="modal-title" id="submit">Are you sure you want to edit request number : {{request['id']}} for CEC-ID : {{ request['USERID'] }} ? </h6>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <form method="POST">
                                    {{form.csrf_token()}}
                                    <div class="modal-body">
                                        <label> Comments </label>
                                        <div>
                                            {{form.approver_comment(required='required') }}
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <input type="submit" name="approve" value="Approve" class="btn btn-success">
                                        <input type="submit" name="deny" value="Deny" class="btn btn-danger">
                                    </div>
                                    {{form.id(value=request['id'] ) }}
                                    {{form.userid(value=request['USERID']) }}
                                    {{form.myidgrpname(value=request['MYID_GRP_NAME']) }}
                                </form>
                            </div>
                        </div>
                    </div>
                </td>
                {% else %}
                <td> </td>
                {% endif %}
                {% if request['STATUS'] == 'Pending' %}
                        <td><a style="color:orange">{{request['STATUS']}}</font></td>
                {% elif request['STATUS'] == 'Approved' %}
                        <td><a style="color:green">{{request['STATUS']}}</font></td>
                {% elif request['STATUS'] == 'Denied' %}
                        <td><a style="color:red" >{{request['STATUS']}}</font></td>
                {% else %}
                <td>{{request['STATUS']}}</td>
                {% endif %}
            </tr>
            {% endfor %}
            </tbody>
        </table>
        <small id="HelpBlock" class="form-text">
            Note: Releng DB Status (<a style="color:orange">Pending<a style="color:black">/<a style="color:green">Approved<a style="color:black">/<a style="color:red">Denied<a style="color:black">) would be updated every night 10PM IST<br>
            <a style="color:orange">Pending <a style="color:black"> - Access Approval Pending<br>
                <a style="color:green">Approved <a style="color:black"> - Access Granted<br>
                    <a style="color:red">Denied <a style="color:black"> - Access Denied
        <br><br> </small>
        <form method="POST">
            {{form.hidden_tag()}}
            {% if show_active_button %}
            {{form.submit_active(class="btn btn-info", style = "width:30%")}}
            {% else %}
            {{form.submit_all(class="btn btn-info", style = "width:30%")}}
            {% endif %}
        </form>

        </div>
    </div>
</div>
{% endblock %}