{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script>
$(document).ready(function(){
  $('[data-toggle="popover"]').popover();
});
$(document).ready(function(){
  $('[data-toggle="tooltip"]').tooltip()
});
</script>

<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous">
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" rel="stylesheet" />
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>


{% if build_info %}
<div class="col-xs-5 col-sm-8">
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-4">Sanity compare report for {{product.upper()}}</h1>
        </div>
    </div>

  
    
    <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
        <!-- limit to col width-->
        
            
        
            <tr style="text-align:center; background-color:black;color:white">
            <th scope="col" style="width: 20%">FT Suit Name</th>
            {% for build in builds_to_compare %}
            <th scope="col" style="width: 10%"> {{build}}</th>
            {% endfor %}
            <!--th scope="col" style="width: 5%">Smoke Priority</th>
            <th scope="col" style="width: 33%">Pre Commit CI Status</th>
            <th scope="col" style="width: 12%">Actions</th-->
            </tr>
            <tbody>
            
            {% for suit in functional_suites %}
            <tr style="text-align:left">
            <td>
            {{ suit }}

  <button class="btn outline:none" type="button" data-toggle="collapse" data-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample"> <i class="fas fa-caret-down"></i></button>

<div class="collapse" id="collapseExample">
  <div class="card card-body">
    Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.
  </div>
</div>
            </td>
            {% for build_number in builds_to_compare %}
            {% for build in builds %}
            {% if build_number == build["build_number"] and suit == build["functional_suite"] %}
                <td>
                Total Count: {{ build["total_feature_file_count"] }}
            
                <br>
                Pass Count: {{ build["passed_case_count"] }}
                <br>
            
                Fail Count: {{ build["failed_case_count"] }}
                </td>
            
            {% endif %}
            {% endfor %}
            
        {% endfor %}

 </tr>           
{% endfor %}
</tbody>
</table>
</div>
{% endif %}
{% endblock %}

    