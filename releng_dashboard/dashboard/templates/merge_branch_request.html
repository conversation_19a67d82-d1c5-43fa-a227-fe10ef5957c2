{% extends 'product_base.html' %}
{% block home %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
    $(document).ready(function() {
    $('#merge_branch').DataTable({
        "scrollX": true,
        "scrollY": 500,
        "bPaginate": false,
        });
    $('.dataTables_length').addClass('bs-select');
    } );
</script>

<div class="col-xs-6 col-sm-9">
    <form method="post" >
        {{form.hidden_tag()}}
        {% if repo_info %}
            <div class="fw-body" >
            <div class="content">
                <div class="jumbotron jumbotron-fluid">
                    <div class="container">
                        <h1 class="display-4">Submit a Sync Branch Request</h1>
                        <p id="HelpBlock" class="form-text text-muted">
                            <font color="green">
                              Following will happen as a part of sync:
                              <br> &ensp; 1. Create a temporary branch from source branch.
                              <br> &ensp; 2. Create a PR from temp branch to destination branch.
                              <br> &ensp; 3. Make Branch Reference changes if needed in base.go.mod, requirements.yaml etc files.
                              <br> &ensp; 4. If auto merge option is enabled. When all the PR checks are met, PR will be merged.
                              <br> &ensp; 5. If not, developers would need to merge it manually.
                              <br> &ensp; 6. if there is a merge conflict.
                              <br> &emsp;&emsp; if files in conflict are less then use Github UI
                              <br> &emsp;&emsp; else resolve conflicts locally & push to temp branch.
                            </font>
                        </p>
                    </div>
                </div>

		            </div>
                <div class="form-group row">
                    <label for="source_branch" class="col-sm-2 col-form-label" style="text-align:left;"> Github organziation <font color="red"> *</font></label>
                    <div class="col-sm-10">  {{ product_org }} </div>
                </div>
                <div class="form-group row">
                    <label for="source_branch" class="col-sm-2 col-form-label" style="text-align:left;"> Source Branch <font color="red"> *</font></label>
                    <div class="col-sm-10">  {{ form.source_branch(size=20) }} </div>
                </div>
                <div class="form-group row">
                    <label for="destination_branch" class="col-sm-2 col-form-label" style="text-align:left;"> Destination Branch <font color="red"> *</font></label>
                    <div class="col-sm-10">  {{ form.destination_branch(size=20) }} </div>
                </div>
                <div class="form-group row">
                    <label for="repo_level" class="col-md-2 control-label" style="text-align:left;">Repositories<font color="red"> *</font></label>
                    <div class="col-sm-10" disabled="false">  {{ form.select_all_repos(onchange="repo_list_check()") }} All Repos <br>
        	          	<select multiple name="repo_list" id="repo_list_selected" method="GET" disabled="false" action="/">
        	              {% for repo in repo_info %}
        	              <option name="repo_list" value="{{repo}}">{{repo}}</option>
        	              {% endfor %}
        	            </select>
        	            <small id="HelpBlock" class="form-text text-muted">
        	                To select multiple items in a list, hold down the Ctrl (PC) or Command (Mac) key. Then click on your desired items to select.
        	            </small>
        							</div>
                </div>
                <script>
                  function repo_list_check(){
                    var checked = document.getElementById('select_all_repos').checked
                    if (checked){
                      //document.getElementById('repos').disabled = false
                      document.getElementById('repo_list_selected').disabled = true
                    } else {
                      //document.getElementById('repos').disabled = true
                      document.getElementById('repo_list_selected').disabled = false
                    }
                  }
                  repo_list_check()
                </script>
                <div class="form-group row">
                    <label for="dry_run" class="col-sm-2 col-form-label" style="text-align:left;">Dry Run</label>
                    <div class="col-sm-10">  {{ form.dry_run }} </div>
                </div>
                <!--<div class="form-group row">
                    <label for="auto_merge" class="col-sm-2 col-form-label" style="text-align:left;"> Merge PR when checks have passed</label>
                    <div class="col-sm-10">  {{ form.auto_merge(disabled=True) }}</div>
                </div>-->
                <div class="form-group row">
                    <label for="notify_by_email" class="col-sm-2 col-form-label" style="text-align:left;"> Additonal users to be notified</label>
                    <div class="col-sm-10">  {{ form.notify_by_email }}
                        <small class="form-text text-muted">
                            Comma separated cec ids
                        </small>
                    </div>
                </div>

               <div class="form-group row" >
                    <div class="col-sm-10">
                        {{form.submit(class="btn btn-info", style = "width:15%", value = "Sync Branch")}}
                    </div>
                </div>


            </div>
            </div>
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">Merge Branch : {{product_org}}</h3>
        {% endif %}
    </form>

</div>
{% endblock %}
