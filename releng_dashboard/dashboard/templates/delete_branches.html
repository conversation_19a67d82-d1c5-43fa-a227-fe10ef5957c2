{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#deleted_branches').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
    <br>
    <div class="row">
      <div class="col-sm-3">
          <div class="card">
              <div class="card-body">
                  <h5 class="card-title">Deleted Branches Data</h5>
                  <p class="card-text">List of branches deleted via Releng Automation</p>
                  <a href="/db_deleted_branches/{{product}}" class="btn btn-info">Click Here >></a>
              </div>
          </div>
      </div>
    </div>
    <form method="post" >
        {{form.hidden_tag()}}
        {% if del_branch_info %}
            <div class="fw-body" >
            <div class="content">
                <br>
                <br>
                <h3 style="text-align:left">Stale Branches (30 days or older) : {{product_org}}</h3>
                <table id ="deleted_branches" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                    <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm"scope="col">Branch Name</th>
                        <th class="th-sm" scope="col">Repo Name</th>
                        <th class="th-sm" scope="col">Last Modified By</th>
                        <th class="th-sm" scope="col">Last Commit Date</th>
                        <th class="th-sm" scope="col">Auto Deletion Date</th>
                        <th class="th-sm" scope="col">Protection Status</th>
                        <th class="th-sm" scope="col">Select</th>

                    </tr>
                    </thead>
                    <tbody>
                    {% for branch in del_branch_info %}
                        <tr style="text-align:center">
                            <th scope="row">{{ branch.branch_name }}</th>
                            <td>{{ branch.repo }}</td>
                            <td>{{ branch.last_user }}</td>
                            <td>{{ branch.lastcommitdate }}</td>
                            {% if branch.branch_status == "obsolete" or branch.branch_status == "expired"  %}
                            <td><font color = "red">{{ branch.expiry_date }}</font></td>
                            {% else %}
                            <td><font color = "black">{{ branch.expiry_date }}</font></td>
                            {% endif %}
                            <td>{{ branch.protection_status }}</td>
                            <td>
                                <input type="checkbox" id="{{branch.branch_name}}__sep__{{branch.repo}}__sep__{{branch.head_sha}}" name="{{product}}_Select" value="{{branch.branch_name}}__sep__{{branch.repo}}__sep__{{branch.head_sha}}__sep__{{branch.expiry_date}}__sep__{{branch.branch_status}}__sep__{{branch.protection_status}}">
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                <br>
                <b>*Protected Branches</b> (Protection Status = Y) can NOT be deleted from Dashboard. Please reach out to Releng to get protection removed.
                <br>
                <br>
<!--
                <div class="form-group row">
                    <label for="github_token" class="col-sm-2 col-form-label">
                        <a href="https://help.github.com/en/github/authenticating-to-github/creating-a-personal-access-token-for-the-command-line">
                            GitHub Access Token
                        </a>
                    </label>
                    <div class="col-sm-10">
                        {{form.github_token}}
                        <br>
                    </div>
                </div>
-->
                {{ form.submit(class="btn btn-danger",hidden='true', id='form-submit') }}
                <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#editModal">
                    Delete Branches
                </button>
                {{ form.renew(class="btn btn-danger",hidden='true', id='form-renew') }}
                <button type="button" class="btn btn-success" data-toggle="modal" data-target="#renewModal">
                    Extend Branches
                </button>
            </div>
            </div>
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">No Stale Branches Found For : {{product_org}}</h3>
        {% endif %}
    </form>
    <!-- Button trigger modal -->


    <!-- Modal -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalTitle">Delete Branch(es) Permanently</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <b>Are you sure ?</b>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="delete_button" data-dismiss="modal">Cancel</button>
                    <!--{{form.submit(class="btn btn-danger")}}-->
                    <button type="button" class="btn btn-danger" id="modal-confirm">Proceed</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="renewModal" tabindex="-1" role="dialog" aria-labelledby="renewModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="renewModalTitle">Extend Branches</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <b>Are you sure , you want to extend auto deletion date by 1 month for selected branches?</b>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="renew_button" data-dismiss="modal">Cancel</button>
                <!--{{form.submit(class="btn btn-danger")}}-->
                <button type="button" class="btn btn-danger" id="modal-extend">Proceed</button>
            </div>
        </div>
    </div>
</div>

</div>

<script>
$('#modal-confirm').click(function(){
    // Perform the action after modal confirm button is clicked.
    document.getElementById("delete_button").disabled = true;
    document.getElementById("modal-confirm").disabled = true;
    $('#form-submit').click(); // submitting the form
});
$('#modal-extend').click(function(){
    // Perform the action after modal confirm button is clicked.
    document.getElementById("renew_button").disabled = true;
    document.getElementById("modal-extend").disabled = true;
    $('#form-renew').click(); // submitting the form
});
</script>
{% endblock %}
