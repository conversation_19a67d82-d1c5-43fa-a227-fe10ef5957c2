{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script>
    function trigger_corona(trigger_corona_scan) {
        var predecessor_id = document.getElementById("predecessor_id");
        
        var predecessor_id_radio = document.getElementById("predecessor_id_option1");
        var predecessor_id_input_radio = document.getElementById("predecessor_id_input_option2");
        var predecessor_id_content = document.getElementById("predecessor_id");
        var predecessor_id_input_content = document.getElementById("predecessor_id_input");

        if (trigger_corona_scan.checked) {
            predecessor_id.style.display = "block";
            predecessor_id_radio.style.display = "block";
            predecessor_id_input_radio.style.display = "block";

        } else {
            predecessor_id.style.display = "none";
            predecessor_id_radio.style.display = "none";
            predecessor_id_input_radio.style.display = "none";
        }
        
    
        predecessor_id_radio.addEventListener("change", function() {
        predecessor_id_content.style.display = "block";
        predecessor_id_input_content.style.display = "none";
        });

        predecessor_id_input_radio.addEventListener("change", function() {
        predecessor_id_content.style.display = "none";
        predecessor_id_input_content.style.display = "block";
        });
    }
</script>

<div class="col-xs-6 col-sm-9">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Corona Scan for QA Build</h1>
                <p class="lead"> This will trigger Corona scan for the specified QA Build</p>
            </div>
        </div>

        <div class="form-group row">
            <label for="product" class="col-sm-2 col-form-label" style="text-align:left;"> PRODUCT : <font color="red"> *</font></label>
            <div class="col-sm-10">  {{ product }} </div>
        </div>

        <div class="form-group row">
            <label for="qa_build_version" class="col-sm-2 col-form-label" style="text-align:left;"> QA Build Number :<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.qa_build_number(size=20) }} 
                <small class="form-text text-muted">
                    QA build number to be scanned for Corona (Comma separated QA build numbers Ex: 2099.30.m0.i44,2099.30.m0.i43 )
                    <br>
                </small>
            </div>
        </div>
        
        <div class="form-group row" id="users_to_notify">
            <label for="users_to_notify"  class="col-sm-2 col-form-label">Users to Notify:</label>
            <div class="col-sm-10"> {{form.users_to_notify(id="users_to_notify")}} <br>
            <small  class="form-text text-muted"> 
                 (Comma separated Cisco mails ID's of users to be Notified about this corona scan) 
            </small>
            </div>
        </div>
        
        <div class="form-group row">
            <label for="trigger_corona_scan" class="col-sm-2 col-form-label">Add Predecessor ID</label>
            <div class="col-sm-10">
                <input type="checkbox" name="trigger_corona_scan" id="trigger_corona_scan" onchange="trigger_corona(this)" default='false'{% if not trigger_corona_scan %}default="false"{% endif %}>    
		        <div class="col-sm-10" id="predecessor_id_option1" style="display:none">
                      <input type="radio" name="predecessor_type" value="predecessor_id_option1" id="predecessor_id_option1" checked>
                      <label for="predecessor_id_option1">Select Predecessor IDs from previous releases</label>
                       
                </div>
                <div class="col-sm-10" id="predecessor_id_input_option2" style="display:none">
                      <input type="radio" name="predecessor_type" value="predecessor_id_input_option2" id="predecessor_id_input_option2">
                      <label for="predecessor_id_input_option2">Enter Predecessor ID</label>
                </div>
                <div class="col-sm-10" id="predecessor_id" style="display:none">
                {% if predecessor_id_choices and predecessor_id_choices != 'none' %}
                    <label for="predecessor_id" class="col-sm-6 col-form-label" >Predecessor ID</label>
                    <div class="col-sm-10">
                        <select id="predecessor_id" name="predecessor_id">
                        {% for pre_id  in predecessor_id_choices %}
                            <option value="{{ pre_id }}">{{ pre_id }}</option>
                        {% endfor %}
                        </select>
                    	<small class="form-text text-muted">
                        	Ex: QA_Build Number,corona_id,release_type,release_version
                    	</small>
                    </div>
                {% endif %}
            	</div>
            	<div class="col-sm-10" id="predecessor_id_input" style="display:none">
                    <label for="predecessor_id_input" class="col-sm-6 col-form-label">Predecessor ID</label>
                    <div class="col-sm-10">
                    {{form.predecessor_id_input(id="predecessor_id_input")}}
                    <small class="form-text text-muted">
                        Predecessor ID must be from
                        {% if product == 'amf' %}Access and Mobility Management Function<a href=https://corona.cisco.com/products/8002> AMF Corona Link </a><br>{% endif %}
                    {% if product == 'bng' %}Broadband Network Gateway Software<a href=https://corona.cisco.com/products/6808> BNG Corona Link </a><br>{% endif %}
                    {% if product == 'pcf' %}Policy Control Function Software<a href=https://corona.cisco.com/products/4860> PCF Corona Link </a><br>{% endif %}
		    {% if product == 'cpc' %}Converged Policy and Charging<a href=https://corona.cisco.com/products/15440> CPC Corona Link </a><br>{% endif %}
            {% if product == 'cni' %}Cisco Network Insights<a href=https://corona.cisco.com/products/15964> CNI Corona Link </a><br>{% endif %}
                    {% if product in ['smf','ccg','sgw','kpm'] %}Session Management Function Software<a href=https://corona.cisco.com/products/4861> SMF/CCG/SGW/KPM Corona Link </a><br>{% endif %}
                    {% if product in ['upf','rcm','cnvpc'] %}User Plane Function Software<a href=https://corona.cisco.com/products/4862> UPF/RCM/CNVPC Corona Link </a><br> {% endif %}
                    </small>
                   </div>
                </div>
           </div>
           </div>
            <div class="form-group row" >
            <div class="col-sm-10">
                    {{form.submit(class="btn btn-info", style = "width:35%", value = "Trigger Corona Scan")  }}
            </div>
        </div>
    </form>
</div>

{% endblock %}
