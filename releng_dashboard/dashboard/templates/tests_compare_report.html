{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">
<script>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/css/bootstrap.min.css"></link></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/js/bootstrap.min.js"></script>
<script>
$(document).ready(function(){
  $('[data-toggle="popover"]').popover();
});
$(document).ready(function(){
  $('[data-toggle="tooltip"]').tooltip()
});
</script>
<script language="javascript">
$(document).ready(
function() {
$('#functional_suite').DataTable({
        "order": [[ 0, "desc" ]],
    });
});

</script>
<style>
.hiddenRow {
    padding: 0 !important;
}
</style>

{% if build_info %}
<div class="col-xs-5 col-sm-8">
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-4">Sanity compare report for {{product.upper()}}</h1>
        </div>
    </div>

  
    
    <table id="functional_suite" class="table table-striped table-bordered">
        <!-- limit to col width-->
            <thread>
              <tr style="text-align:center; background-color:black;color:white">
                <th scope="col" style="width: 20%">Functional Suite</th>
                {% for build in builds_to_compare %}
                  <th scope="col" style="width: 10%"> {{build}}</th>
                {% endfor %}
              </tr>
            </thread>
            <tbody>
            
            {% for suit in functional_suites %}
              {% if suit != "Total"%}
                <tr style="text-align:left" class="accordion-toggle" data-toggle='collapse' data-target='#{{suit}}'>
                <td>
                  <button class="btn outline:none" type="button" toggle="true" data-toggle="collapse" data-target="#{{suit}}" aria-expanded="false" aria-controls="#{{suit}}"><i class="fas fa-caret-down"></i></button>
              {% else %}
                <tr style="text-align:left">
                <td>
              {% endif %}    
                <a href="/test_report/functional_suite/{{suit}}">{{ suit }}</a>
              </td>
              {% for build_number in builds_to_compare %}
                
                {% for build in builds %}
                  {% if build_number == build["build_number"] and suit == build["functional_suite"] %}
                    {% set result = "true" %}
                                   
                    {% if (build["total_feature_file_count"]) not in ("none","",None)  %}
                       <td>
                        {% if ((build["passed_case_count"] and build["total_feature_file_count"]) not in ("none","",None))  %}
                           {% if build["total_feature_file_count"] > build["passed_case_count"] %}
                               <p style="color:green;">Pass Count: {{ build["passed_case_count"] }}</p>
                           {% else %}
                               <p style="color:green;">Pass Count: {{ build["passed_case_count"] }}</p>
                           {% endif %}
                        {% else %}
                           Pass Count: {{ build["passed_case_count"] }}
                        {% endif %}
                        {% if build['failed_case_count'] and build['failed_case_count'] > 0 %}
                           <p style="color:red;">Fail Count: {{ build["failed_case_count"] }}</p>
                        {% elif (build['total_feature_file_count'] - build['passed_case_count']) > 0 %}
                           <p style="color:red;">Fail Count: {{ build['total_feature_file_count'] - build['passed_case_count']}}</p>
                        {% else %}
                           <p style="color:green;">Fail Count: {{ build['total_feature_file_count'] - build['passed_case_count']}}</p>
                        {% endif %}
                          Total Count: {{ build["total_feature_file_count"] }}
                       </td>
                    {% else %}
                      <td style="color:orange"><b>No Result</b></td>
                    {% endif %}
                    
                 {% endif %}
                {% endfor %}
              {% endfor %}
                
              </tr>
              <tr>
                <td colspan="100%" class="hiddenRow" >
                  <div class="accordian-body collapse" id={{suit}}>
                    <table id="functional_suite" class="table table-striped table-bordered">
                      {% for test in tests_info %}    
                        {% if suit in test %}        
                          <thread>
                            <tr style="text-align:center; background-color:whitesmoke;color:black" colspan ="100%">
                            <th scope="col" style="width: 20%">Testcase Name</th>
                            {% for build in builds_to_compare %}
                              <th scope="col" style="width: 10%"> {{build}}</th>
                            {% endfor %}
                            </tr>
                          </thread>
                          <tbody>
                            {% for testlist in test[suit] %}
                            {% for testname in testlist.split(',') %}  
                              <tr style="text-align:left" colspan ="100%">
                                <td>
                                  {{ testname | string }}
                                </td>
                              {% for build in builds_to_compare %} 
                                {% for row in build_info %}
                                  {% if ((build == row["build_number"]) and (suit == row["functional_suite"])) %} 
                                    {% if (testname in (row["pass_test_case"] | string)) %}
                                      <td style="color:green"><b>PASSED</b></td>
                                    {% elif (testname in (row["fail_test_case"] | string)) %} 
                                      <td style="color:red"><b>FAILED</b></td>
                                    {% else %}
                                      <td style="color:orange"><b>No Result</b></td>
                                    {% endif %}
                                                 
                                 {% endif %}
                                {% endfor %}
                                {% endfor %}
                              {% endfor %}
                              </tr> 
                            {% endfor %}
                          </tbody>
                        {% endif %}
                      {% endfor %}
                    </table>
                  </div>
                </td>
               </tr>
           </tbody>


{% endfor %}
{% endif %}
</table>
</div>
{% endblock %}

    
