{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
setTimeout(function(){
   window.location.reload(1);
}, 30000);

</script>

{% set smi_product_list = ['base-vm','cee','deployer','base-bm','cee-bm','deployer-bm'] %}
{% set no_build_products = ['app-infra','golang-lib','cnee','smf','sgw','staros','lbs-libraries'] %}
{% set packet_core_product_list = ['upf','staros'] %}
<div class="col-xs-6 col-sm-9">
    <!-- {{ current_builds }} -->
    <br>
    <div class="row">

        {% if product not in no_build_products %}
            <div class="col-sm-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Trigger QA Build</h5>
                        <p class="card-text">Trigger/Monitor QA Builds </p>
                        <a href="/qabuild/{{product}}" class="btn btn-info">Click Here >></a>
                    </div>
                </div>
            </div>
        {% endif %}
        {% if product not in smi_product_list %}
        {% if product not in packet_core_product_list %}
            <div class="col-sm-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Generate LOC Report</h5>
                        <p class="card-text">Between QA builds or between dates </p>
                        <a href="/loc/{{product}}" class="btn btn-info">Click Here >></a>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Throttle Tracker</h5>
                        <p class="card-text">Process Throttle Requests</p>
                        <a href="/throttle_tracker_home/{{product}}" class="btn btn-info">Click Here >></a>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Service Access</h5>
                        <p class="card-text">Process Github,Artifactory,Sonar Access requests</p>
                        <a href="/submit_myid_req/{{product}}" class="btn btn-info">Click Here >></a>
                    </div>
                </div>
            </div>
        {% endif %}
        {% endif %}
    </div>
    {% if current_builds %}
        {% for build_type, build_list in current_builds.items() %}
            {% if build_list %}
                <br/>
                <h3 style="text-align:left"> {{build_type}} in Progress (last 24 hours) for {{product}} </h3>
                <br>
                <table id="current_build" class="table table-striped table-sm" style="border: 1px solid gray;">
                <thead class="table-info">
                <tr style="text-align:center; background-color:#000000;color:white">
                    <th class="th-sm" scope="col" width="30">Build</th>
                        <th class="th-sm" scope="col" width="30">Started At</th>
                        <th class="th-sm" scope="col" width="30">Started By</th>
                        <th class="th-sm" scope="col" width="30">Build Status</th>
                        <th class="th-sm" scope="col" width="30">Notes</th>
                        <th class="th-sm" scope="col" width="300">Progress</th>
                        <th class="th-sm" scope="col" width="30">~Time Remaining</th>
                    </tr>
                </thead>
                <tbody>
                    {% for build in build_list %}
                        {% if build.build_number %}
                            <tr style="text-align:center">
                                <td><a href={{ build.build_url }}>#{{ build.build_number }}</a></td>
                                <td>{{ build.start_time }}</td>
                                <td><a href=https://directory.cisco.com/dir/reports/{{ build.triggered_by }} }}>{{ build.triggered_by }}</a></td>
                                <td>{{ build.status }}</td>
                                {% if build["status"] in ["FAILURE","ABORTED","UNSTABLE"] %}
                                    {% if build["notes"] %}
                                        {% if "html" in build["notes"] %}
                                            <td>{{ build["notes"].split(":",1)[0] }}
                                            <a href={{build["notes"].split(":",1)[1]}}> this report</a>
                                            </td>
                                        {% else %}
                                            <td>{{ build["notes"] }}
                                        {% endif %}
                                    {% else %}
                                        <td> N/A </td>
                                    {% endif %}
                                    <td> N/A </td>
                                    <td> N/A </td>
                                {% elif  build.progress %}
                                    <td> N/A </td>
                                    {% if build["progress"] < 100 %}
                                        <td>
                                            <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width:{{ build.progress }}%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">{{ build["progress"] }}%</div>
                                            </div>
                                        </td>
                                        {% if build["eta"] == 0 %}
                                            <td>few seconds</td>
                                        {% else %}
                                            <td>{{ build["eta"] }} mins</td>
                                        {% endif %}
                                    {% else %}
                                        <td>
                                            <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="progressbar" style="width:100%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">{{ build["progress"] }}%</div>
                                            </div>
                                        </td>
                                        <td>Taking more time than estimated</td>
                                    {% endif %}
                                {% else %}
                                    <td> N/A </td>
                                    <td> N/A </td>
                                    <td> N/A </td>
                                {% endif %}
                            </tr>
                        {% else %}
                            <tr style="text-align:center">
                                <td scope="col" width="30"> <font color = "red"> N/A </font></td>
                                <td>{{ build["start_time"] }}</td>
                                <td><a href=https://directory.cisco.com/dir/reports/{{ build["triggered_by"] }} }}>{{ build["triggered_by"] }}</a></td>
                                <td scope="col" width="30">{{ build["status"] }}</td>
                                <td scope="col" width="300"> <font color = "red"> {{ build["notes"] }} </font> </td>
                                <td scope="col" width="30"> <font color = "red"> N/A </font> </td>
                                <td scope="col" width="30"> <font color = "red"> N/A </font> </td>
                            </tr>
                        {% endif %}
                    {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        {% endfor %}
    {% endif %}

    {% if qa_build_info %}
    <div class="fw-body" >
    			<div class="content">
            <br>
            <br>
        <h3 style="text-align:left">Regular Builds for {{product.upper()}}</h3>
        <h5 style="text-align:left;color:grey">To view old builds,click
        <a href=/builds/{{product}}/all>here</a></h5>
                    <br>
        <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
        <thead class="table-info">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th class="th-sm" scope="col">Build Start Time</th>
            <th class="th-sm" scope="col">Triggered By</th>
            <th class="th-sm"scope="col">Branch<br> Build #</th>
            {% if product in packet_core_product_list %}
                <th class="th-sm" scope="col">StarOS Build #</th>
            {% endif %}
            <th class="th-sm" scope="col">Images</th>
            <th class="th-sm" scope="col">Reports</th>
            <th class="th-sm" scope="col">Test Reports: Pass %</th>
        </tr>
        </thead>
        <tbody>
        {% for build in qa_build_info %}
            <tr style="text-align:center">
                <td> {{ build.build_start_time }} </td>
                {% if build.triggered_by == "cnblds" %}
                    <td class="text-secondary">scheduled run</td>
                {% else %}
                    <td><a class="text-info" href=https://directory.cisco.com/dir/reports/{{ build.triggered_by }} }}>{{ build.triggered_by }}</a></td>
                {% endif %}
                {% if build.build_status == "Passed" %}
                    <td> {{ build.branches_branch_name }} <br> <b class="text-success"> {{ build.build_number }}</b></td>
                {% elif build.build_status == "Failed" %}
                    <td> {{ build.branches_branch_name }} <br> <b class="text-danger"> {{ build.build_number }}</b></td>
                {% else %}
                    <td> {{ build.branches_branch_name }} <br> <b class="text-warning"> {{ build.build_number }}</b></td>
                {% endif %}
                {% if product in packet_core_product_list %}
                    {% if build.star_os_build %}
                      <td>
                        <a href={{ build.source_url }} target="_blank"> {{ build.star_os_build }} </a>
                      </td>
                    {% else %}
                      <td></td>
                    {% endif %}
                {% endif %}
                {% if build.build_status == "In_Progress" %}
                    <td>{{ build.build_status }}</td>
                {% else %}
                    <!-- Images -->
                    <td>
                        {% if build.int_artifactory %}
                            <a href = {{ build.int_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }} </a>
                        {% else %}
                            NA
                        {% endif %}
                        <br>
                        {% if product != 'upf' %}
                          {% if build.off_artifactory %}
                            <a href = {{ build.off_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }}-offline </a>
                          {% else %}
                            NA
                          {% endif %}
                          <br>
                          {% if build.cdl_version %}
                            CDL:  <a href ={{ releng_cdl_artifactory }}{{ build.cdl_version }}-offline/ target="_blank"> {{ build.cdl_version }} </a>
                          {% endif %}
                        {% endif %}
                    </td>
                {% endif %}
                <!-- Reports -->
                <td>
                    {% if build["loc_report"] %}
                        <a href = {{ build.loc_report }} target="_blank">LOC</a> |
                    {% endif %}
                    {% if build.change_log %}
                        <a href = {{ build.change_log }} target="_blank">Change log</a>
                    {% endif %}
                    {% if build.sonar_report %}
                        <br>
 			<a href = {{ build.sonar_report }} target="_blank">Sonar Report</a>
                    {% endif %}
                    {% if build.corona_report %}
                        <br>
                        <a href = {{ build.corona_report }} target="_blank">Corona Report</a>
                    {% endif %}
                    

                {% if build["cdets_list"] %}
                        <!--
                        <a href="#" data-toggle="tooltip" data-html="true" title={{ build.cdets_list }}>CDETS List</a>
                        {% set ir_report = build.change_log.split('/')[:-1] | join('/') %}
                        {% set ir_report = ir_report + '/Update_IR_Report.html' %}
                        {% if build.ir_report_link %}
                            <a href = {{ ir_report }} target="_blank">CDETS List</a>
                        {% else %}
                            <a href="#" data-toggle="tooltip" title="{{ build.cdets_list }}">CDETS List</a>
                        {% endif %}-->
                {% endif %}
                
                {% if build["staros_build_info"] %}
                    <br>
                    <a href = {{ build.staros_build_info }} target="_blank">StarOS_Build_Number</a>
                {% endif %}

                </td>
                <!-- Test Reports -->
                <td>
                    {% if build["wrt_report"] %}
                        {% if build["ivt_passed_percentage"] %}
                            <a href = {{ build.wrt_report }} target="_blank"> IVT : {{ build.ivt_passed_percentage }}%
                                {% if build["ivt_approved"] == "yes" %}
                                <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="IVT APPROVED"> <i class="fas fa-check"></i> </button></a>
                            {% endif %}
                        {% else %}
                            <a href = {{ build.wrt_report }} target="_blank"> IVT</a>
                        {% endif %}
			<br>
                    {% endif %}

                    {% if build["test_info"] %}
                        {% for job_type, job_status in build["test_info"].items() %}
                            {% if job_status == "IN_PROGRESS" %}
                                <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                                <div class="spinner-border spinner-border-sm text-warning"></div>
                            {% elif job_status == "PARTIALLY_COMPLETE" %}
                                <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                            {% else %}
                                <a href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }} : {{ job_status }}% </a>
                            {% endif %}
                            <br>
                        {% endfor %}
                    {% endif %}
                </td>

                <!-- enable this when promotion is supported from dashboard
                {% if build["dh_promotion_status"] == "N" %}
                    <--this needs to changed when devhub deployment is automated ->
                    {% if user_role == "admin-dummy" %}
                        <td>
                            <!- loop.index is the default counter which is offered ->
                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#promote{{ loop.index }}">Promote</button>
                            <div class="modal fade" id="promote{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="exampleModalCenterTitle">Promote {{ build.build_number }} to? </h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form method="POST">
                                            {{form.csrf_token()}}
                                            <div class="modal-body">
                                                {{ form.qa_build_no(value=build.build_number) }}
                                                {{ form.branch(value=build.branches_branch_name) }}
                                                {{form.promote_dh(class="btn btn-info")}}
                                                {{form.promote_cco(class="btn btn-warning")}}
                                            </div>
                                        </form>
                                        <div class="modal-footer">
                                            <h6>Caution: Promotion will start once you click</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    {% elif build["ivt_approved"] == "yes" %}
                        <td>
                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#promote">Promote</button>
                            <div class="modal fade" id="promote" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Insufficient privilege </h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Build Promotion can be done only by Releng Team.</p>
                                            <p>Please raise an RER.</p>
                                            <a href="https://rtp-mitg7-gnats.cisco.com:7443/cgi-bin/5G-relengreq.pl?database=5G-Releng-Requests">link</a>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    {% else %}
                        <td></td>
                    {% endif %}
                {% elif build["dh_promotion_status"] == "I" %}
                    <td>Promotion in Progress</td>
                {% else %}
                    <td>Promoted</td>
                {% endif %} -->
                {% endfor %}
        </tbody>
        </table>
                    NA - Not Available
                </div>
    {% endif %}

    {% if cc_qa_build_info %}
    <div class="fw-body" >
                        <div class="content">
                <br>
                <br>
                <h3 style="text-align:left">Instrumented Builds for {{product.upper()}}</h3>
                <h5 style="text-align:left;color:grey">To view old builds,click
                <a href=/builds/{{product}}/all>here</a></h5>
                            <br>
                <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                <thead class="table-info">
                <tr style="text-align:center; background-color:#000000;color:white">
                    <th class="th-sm" scope="col">Build Start Time</th>
                    <th class="th-sm" scope="col">Parent Branch<br> Parent Build #</th>
                    <th class="th-sm"scope="col">Branch<br> Build #</th>
                    <th class="th-sm" scope="col">Images</th>
                    <th class="th-sm" scope="col">Reports</th>
                    <th class="th-sm" scope="col">Test Reports: Pass %</th>
                </tr>
                </thead>
                <tbody>
                {% for build in cc_qa_build_info %}
                    <tr style="text-align:center">
                        <td> {{ build.build_start_time }} </td>
                        <td> {{ build.cc_parent_branch }} <br>
                        {% if build.parents_branch_point %}
                        {{ build.parents_branch_point }}
                        {% endif %}
                        {% if build.build_status == "Passed" %}
                            <td> {{ build.branches_branch_name }} <br> <b class="text-success"> {{ build.build_number }}</b></td>
                        {% elif build.build_status == "Failed" %}
                            <td> {{ build.branches_branch_name }} <br> <b class="text-danger"> {{ build.build_number }}</b></td>
                        {% else %}
                            <td> {{ build.branches_branch_name }} <br> <b class="text-warning"> {{ build.build_number }}</b></td>
                        {% endif %}
                        {% if build.build_status == "In_Progress" %}
                            <td>{{ build.build_status }}</td>
                        {% else %}
                        <!-- Images -->
                            <td>
                                {% if build.int_artifactory %}
                                    <a href = {{ build.int_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }} </a>
                                {% else %}
                                    NA
                                {% endif %}
                                <br>
                                {% if product != 'upf' %}
                                  {% if build.off_artifactory %}
                                    <a href = {{ build.off_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }}-offline </a>
                                  {% else %}
                                    NA
                                  {% endif %}
                                  <br>
                                  {% if build.cdl_version %}
                                    CDL:  <a href ={{ releng_cdl_artifactory }}{{ build.cdl_version }}-offline/ target="_blank"> {{ build.cdl_version }} </a>
                                  {% endif %}
                                {% endif %}
                            </td>
                        {% endif %}
                        <!-- Reports -->
                        <td>
                            {% if build["loc_report"] %}
                                <a href = {{ build.loc_report }} target="_blank">LOC</a> |
                            {% endif %}
                            {% if build.change_log %}
                                <a href = {{ build.change_log }} target="_blank">Change log</a>
                            {% endif %}
                            {% if build.sonar_report %}
                                <br>
                                <a href = {{ build.sonar_report }} target="_blank">Sonar Report</a>
                            {% endif %}
                            {% if build.corona_report %}
                                <br>
                                <a href = {{ build.corona_report }} target="_blank">Corona Report</a>
                            {% endif %}


                            {% if build["cdets_list"] %}
                            {% endif %}

                            {% if build["staros_build_info"] %}
                                <br>
                                <a href = {{ build.staros_build_info }} target="_blank">StarOS_Build_Number</a>
                            {% endif %}

                        </td>
                        <!-- Test Reports -->
                        <td>
                            {% if build["wrt_report"] %}
                            {% if build["ivt_passed_percentage"] %}
                                <a href = {{ build.wrt_report }} target="_blank"> IVT : {{ build.ivt_passed_percentage }}%
                                {% if build["ivt_approved"] == "yes" %}
                                    <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="IVT APPROVED"> <i class="fas fa-check"></i> </button></a>
                            {% endif %}
                            {% else %}
                                <a href = {{ build.wrt_report }} target="_blank"> IVT</a>
                            {% endif %}
                            <br>
                            {% endif %}

                            {% if build["test_info"] %}
                                {% for job_type, job_status in build["test_info"].items() %}
                                    {% if job_status == "IN_PROGRESS" %}
                                        <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                                        <div class="spinner-border spinner-border-sm text-warning"></div>
                                    {% elif job_status == "PARTIALLY_COMPLETE" %}
                                        <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                                    {% else %}
                                        <a href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }} : {{ job_status }}% </a>
                                    {% endif %}
                                    <br>
                                {% endfor %}
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
                </table>
                                    NA - Not Available
    </div>
    {% endif %}
{% endblock %}
