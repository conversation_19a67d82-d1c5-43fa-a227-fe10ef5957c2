{% extends 'base.html' %}
{% block content %}

<div id="carouselExampleInterval" class="carousel slide" data-ride="carousel">
  <div class="carousel-inner">
    <div class="carousel-item active" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Download offline & online image.</h1>
          <p>Go to Product home page to know more</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Trigger QA Build for your Product & know it's progress</h1>
          <p>Go to product home page -> QA Build & Release -> Trigger QA build</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Know about past Released Builds</h1>
          <p>Go to product home page -> Released Builds</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>All Test reports at one place</h1>
          <p>Go to product home page -> Quality Statistics</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Create and Approve throttle requests</h1>
          <p>Go to product home page -> Branch Operations -> Throttle tracker</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Delete Stale Github branches</h1>
          <p>Go to product home page -> Operations to delete stale branches</p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Github Access Management</h1>
          <p>Go to product home page -> Github Operations -> Access Requests </p>
        </div>
      </div>
    </div>
    <div class="carousel-item" data-interval="2000">
      <img src="/static/background_cisco.jpg" class="d-block w-100" alt="..." style="width:100%; height: 250px;">
      <div class="container">
        <div class="carousel-caption text-left d-none d-md-block">
          <h1>Generate LOC Report</h1>
          <p>Between QA builds or between dates</p>
        </div>
      </div>
    </div>
  </div>
  <a class="carousel-control-prev" href="#carouselExampleInterval" role="button" data-slide="prev">
    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
    <span class="sr-only">Previous</span>
  </a>
  <a class="carousel-control-next" href="#carouselExampleInterval" role="button" data-slide="next">
    <span class="carousel-control-next-icon" aria-hidden="true"></span>
    <span class="sr-only">Next</span>
  </a>
</div>

<nav class="navbar navbar-expand-lg navbar-dark" style="background-color:#000000;">
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
  </button>


<div class="collapse navbar-collapse" id="navbarSupportedContent">
  <ul class="navbar-nav">
<li class="nav-item dropdown">
  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
      <font color="white" size="4">On-Prem</font>
  </a>
  <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
    {% for branch in branches %}
      <a class="dropdown-item" href="/branch/{{ branch }}">{{ branch }}</a>
      {% endfor %}
  </div>
</li>
<!--div class="collapse navbar-collapse" id="navbarSupportedContent">
  <ul class="navbar-nav"-->
<li class="nav-item dropdown">
  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
      <font color="white" size="4">As a Service</font>
  </a>
  <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">  
        {% for aas_product in aas_products %}
            <a class="dropdown-item" href="/aas_products/{{ aas_product }}">{{ aas_product }}</a>
        {% endfor %}
  </div>
</li>
</ul>
</div>
</nav>

  <!--nav class="navbar navbar-expand-lg navbar-dark" style="background-color:#000000;">
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav">
            {% for branch in branches %}
                <li class="nav-item">
                    <a class="nav-link" href="/branch/{{ branch }}"><font color="white" size="4">{{ branch }}</font></a>
                </li>
            {% endfor %}
        </ul>
    </div>
</nav-->

<div class="container-fluid">
    <br>
    <br>
    <div class="row">
        <div class="col"></div>
        <div class="col-10">
            <br>
            <h2 style="text-align:center" class="featurette-heading">Test Execution Summary : {{ selected_branch }}</h2>
<style>
  table {
    border-collapse: collapse;
    width: 100%;
  }

  th.thick-border, td.thick-border {
    border-right: 3px solid #56C1A1;
  }
</style>      
<table id="qa_build" class="table table-striped table-bordered" style="border: 1px solid black; width:100%;">
        <col>
        <colgroup span="8"></colgroup>
        <colgroup span="8"></colgroup>
        <tr style="text-align:center; background-color:#56C1A1; color:white">
          <th rowspan="3" width="100" style="text-align:center">Product</th>
          <th class="thick-border" colspan="6" scope="colgroup" style="text-align:center border:3px">Functional Test Results</th>
          <th colspan="6" scope="colgroup" style="text-align:center">System Test Results</th>
        </tr>
        <tr>
          <th colspan="3" scope="colgroup" style="text-align:center">Sanity</th>
          <th class="thick-border" colspan="3" scope="colgroup" style="text-align:center">Regression</th>
          <th colspan="3" scope="colgroup" style="text-align:center">Sanity</th>
          <th colspan="3" scope="colgroup" style="text-align:center">Longevity</th>
        </tr>
        <tr>
          <th width="250" scope="col">Build Details</th>
          <th width="100" scope="col">Total TC</th>
          <th width="100" scope="col">Pass %</th>
          <th width="250" scope="col">Build Details</th>
          <th width="100" scope="col">Total TC</th>
          <th class="thick-border" width="100" scope="col">Pass %</th>
          <th width="250" scope="col">Build Details</th>
          <th width="100" scope="col">Total TC</th>
          <th width="100" scope="col">Pass %</th>
          <th width="250" scope="col">Build Details</th>
          <th width="100" scope="col">Total TC</th>
          <th width="100" scope="col">Pass %</th>
        </tr>
      {% for data in tests_info %}
      <tr>
        <td>{{ data['product'] }}</td>
        <td><font color="blue">{{ data['sanity_build_number'] }}</font></br>
            {{ data['sanity_start_time'] }}</td>
        <td>{{ data['sanity_total_TC'] }}</td>
        {% if data['sanity_passed_percentage'] < 80 %}
          <td style="color:red;">{{data['sanity_passed_percentage']}}%</td>
        {% else %}
          <td style="color:green;">{{data['sanity_passed_percentage']}}%</td>
        {% endif %}
        <td><font color="blue">{{ data['reg_build_number'] }}</font></br>
          {{ data['reg_start_time'] }}
        </td>
        <td>{{ data['reg_total_TC'] }}</td>
        {% if data['reg_passed_percentage'] <= 0 %}    
          <td class="thick-border"></td>
        {% elif data['reg_passed_percentage'] < 80 %}
          <td class="thick-border" style="color:red;">{{data['reg_passed_percentage']}}%</td>
       {% else %}
          <td class="thick-border" style="color:green;">{{data['reg_passed_percentage']}}%</td>
       {% endif %}
         <td><font color="blue">{{ data['ivt_build_number'] }}</font></br>
        {{ data['ivt_start_time'] }}</td>
        <td>{{ data['ivt_total_TC'] }}</td>
       {% if data['ivt_passed_percentage'] <= 0 %}    
         <td></td>
       {% elif data['ivt_passed_percentage'] < 80 %}
         <td style="color:red;">{{data['ivt_passed_percentage']}}%</td>
       {% else %}
         <td style="color:green;">{{data['ivt_passed_percentage']}}%</td>
       {% endif %}
       <td><font color="blue">{{ data['longevity_build_number'] }}</font></br>
        {{ data['longevity_start_time'] }}</td>
        <td>{{ data['longevity_total_TC'] }}</td>
       {% if data['longevity_passed_percentage'] <= 0 %}
        <td></td>
       {% elif data['longevity_passed_percentage'] < 80 %}
         <td style="color:red;">{{data['longevity_passed_percentage']}}%</td>
       {% else %}
         <td style="color:green;">{{data['longevity_passed_percentage']}}%</td>
       {% endif %}
      </tr>
      {% endfor %}
    </table>   
  </div> <div class="col"></div>
  </div>

<hr class="featurette-divider">
  <div class="row featurette">
  <div class="col"></div>
    <div class="col-8">
  <h2 style="text-align:center" class="featurette-heading">Test Execution Overview</h2></br></br>
  <!--script src="https://cdn.jsdelivr.net/npm/chart.js@3.3.0/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0-rc"></script-->
<!--script src="https://cdn.anychart.com/js/latest/anychart.min.js"></script-->
<script src="https://cdn.anychart.com/js/latest/anychart.min.js"></script>
<div id="container" style="width: 100%; height: 400px;"></div>
<script>
var result = {{ result_dataset|tojson|safe }};
anychart.onDocumentLoad(function(){
  var dataSet = anychart.data.set(result);

  var chart = anychart.column();
  
  chart.column(dataSet.mapAs({value:1,x:0})).name('FT Sanity').fill('#239B56');
  chart.column(dataSet.mapAs({value:2,x:0})).name('FT Reg.').fill('#2E86C1');
  chart.column(dataSet.mapAs({value:3,x:0})).name('ST Sanity').fill('#E67E22');
  chart.column(dataSet.mapAs({value:4,x:0})).name('ST Reg.').fill('#F4D03F');
  
  chart.grid(0, {layout:'vertical'});
  chart.barGroupsPadding(3);
  chart.legend(true); 
  chart.animation(true);
  chart.labels(true);
  chart.yScale().maximum(100);
  chart.container('container');
  chart.draw();
});
</script>

</div>
<div class="col"></div>
</div>
</div>
</nav>


  <hr class="featurette-divider">
  <div class="row featurette"><div class="col"></div><div class="col-3"> <h2 class="featurette-heading">Test Execution Trends</h2> </div> <div class="col"></div></div> 
  <br>
  <div class="row featurette">
    <div class="col-6">
      <iframe src="https://{{grafana_host}}:3000/d-solo/HgleHiqMk/cn-product-sanity-pass-percentage?orgId=1&from=now-30d&to=now&var-product=ccg&var-product=amf&var-branch={{ selected_branch }}&panelId=3" width="90%" height="300px" frameborder="0"></iframe>
    </div>
    <div class="col-6">
      <iframe src="https://{{grafana_host}}:3000/d-solo/HgleHiqMk/cn-product-sanity-pass-percentage?orgId=1&from=now-30d&to=now&var-product=ccg&var-product=amf&var-branch={{ selected_branch }}&panelId=4" width="90%" height="300px" frameborder="0"></iframe>
    </div> 
    </div>
    <div class="row featurette">
   <div class="col-6">
     <iframe src="https://{{grafana_host}}:3000/d-solo/HgleHiqMk/cn-product-sanity-pass-percentage?orgId=1&var-product=ccg&var-product=amf&var-branch={{ selected_branch }}&panelId=6&from=now-30d&to=now" width="90%" height="300px" frameborder="0"></iframe>
   </div>
   <div class="col-6">
    <iframe src="https://{{grafana_host}}:3000/d-solo/HgleHiqMk/cn-product-sanity-pass-percentage?orgId=1&var-product=ccg&var-product=amf&var-branch={{ selected_branch }}&panelId=7&from=now-30d&to=now" width="90%" height="300px" frameborder="0"></iframe>
  </div>
  </div>
<br><br>
<footer class="container">
  <p class="float-right"><a href="#">Back to top</a></p>
  <p>&copy; RelEng MABU</p>
</footer>

{% endblock %}
