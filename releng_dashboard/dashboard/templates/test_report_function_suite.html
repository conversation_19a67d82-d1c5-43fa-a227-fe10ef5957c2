{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
$(document).ready(
function() {
$('#functional_suite').DataTable({
        "order": [[ 0, "desc" ]]
    });
});

</script>

<div class="col-xs-6 col-sm-9">
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-4">{{ functional_suite }}</h1>
        </div>
    </div>
    <table id="functional_suite" class="table table-striped table-bordered" style="width: 100%">
        <thead class="table-primary">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col">Start Time</th>
            <th scope="col">Product</th>
            <th scope="col">Branch<br> Build #</th>
            <th scope="col">Job Type</th>
            <th scope="col">Links</th>
            <th scope="col">Pass Count<br>Fail Count</th>
            <th scope="col">Total Count<br> Pass Percentage</th>
        </tr>
        </thead>
        <tbody>
        {% for sanity_report in functional_suite_data %}
        <tr>
            <td>{{sanity_report['start_time']}}</td>
            <td>{{sanity_report['products_product_name']}}</td>
            <td>{{sanity_report['branch_name']}}<br> {{sanity_report['build_number']}} </td>
            <td>{{sanity_report['job_type']}}</td>
            {% if sanity_report['status'] != "IN_PROGRESS" %}
            <td>
                {% if sanity_report.get('wrt_report') and sanity_report['wrt_report'].startswith('https://mitg-webtool.cisco.com/') %}
                    <a href = {{sanity_report['wrt_report']}} target="_blank"> WRT Report </a>
                <br>
                {% endif %}
                {% if sanity_report['jenkins_job'] %}
                    <a href = {{sanity_report['jenkins_job']}} target="_blank"> Execution Link </a>
                {% endif %}
            </td>
            <td style="text-align:left">
                Pass Count: {{sanity_report['passed_case_count']}}
                <br>
                {% if sanity_report['failed_case_count'] %}
                Fail Count: {{sanity_report['failed_case_count']}}
                {% else %}
                Fail Count: {{ sanity_report['total_feature_file_count'] - sanity_report['passed_case_count']}}
                {% endif %}
            </td>
            <td>Total Count: {{sanity_report['total_feature_file_count']}}
                <br>
                {% if sanity_report["passing_criteria"] %}
                {% if sanity_report["passing_criteria"] > sanity_report["passed_percentage"] %}
                <p style="color:red;">Pass Percentage:{{sanity_report['passed_percentage']}}%</p>
                {% else %}
                <p style="color:green;">Pass Percentage:{{sanity_report['passed_percentage']}}%</p>
                {% endif %}
                {% else %}
                Pass Percentage: {{sanity_report['passed_percentage']}}%
                {% endif %}
            </td>
            {% else %}
            <td> </td>
            <td></td>
            <td> IN PROGRESS</td>
            {% endif %}
        </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
