{% extends 'product_base.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Generate LOC Report</h1>
                <p class="lead"> This will generate LOC Report between two QA Builds for current and dependent orgs.</p>
            </div>
        </div>

        <br>

        <div class="form-group row">
            <label for="start_branch_name" class="col-sm-2 col-form-label"> From Branch Name :</label>
            <div class="col-sm-10" style="padding-top: 7px;">
                {{form.start_branch_name(id="start_branch_name")}}
            </div>
        </div>

        <div class="form-group row">
            <label for="start_build" class="col-md-2 control-label" style="text-align:left;"> From QA Build * :</label>
            <div class="col-sm-10" style="padding-top: 7px;">
                {{ form.start_build(size=20) }}
                <small class="form-text text-muted">
                    e.g. 2021.02.1.i214 . 'first' means 1st build on the branch.
                </small>
            </div>

        </div>

        <div class="form-group row">
            <label for="end_branch_name" class="col-sm-2 col-form-label"> To Branch Name :</label>
            <div class="col-sm-10" style="padding-top: 7px;">
                {{form.end_branch_name(id="end_branch_name")}}
            </div>
        </div>

        <div class="form-group row">
            <label for="end_build" class="col-md-2 control-label" style="text-align:left;"> To QA Build :</label>
            <div class="col-sm-10" style="padding-top: 7px;">
                {{ form.end_build(size=20) }}
                <small class="form-text text-muted">
                    e.g. 2021.02.2.i258 . 'latest' means most recent build on the branch.
                </small>
            </div>
        </div>

        <div class="form-group row">
            <label for="recp_list" class="col-md-2 control-label" style="text-align:left;"> Addtional Recipient List :</label>
            <div class="col-sm-10">
                {{ form.recp_list }}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma separated cec ids.
                </small>
            </div>
        </div>
        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
        </div>
        <div class="form-group row" >
            <div class="col-sm-10">
                * If the branch is main , the 'first' build means 1st build on the current release cadence of the branch.
            </div>
        </div>
    </form>
</div>

{% endblock %}
