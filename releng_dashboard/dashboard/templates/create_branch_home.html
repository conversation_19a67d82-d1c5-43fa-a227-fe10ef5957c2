{% extends 'product_base.html' %}
{% block home %}
{% set common_product_list = ['app-infra','golang-lib','cnee','lbs-libraries'] %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Create Branch Home Page</h1>
                <p class="lead"> Use this utility to create branch for the product {{ product}} or only in {{ product_org }} </p>
            </div>
        </div>
            <div class="form-group row">
                <div class="col-sm-10">
                    <input type="radio" name="create_branch_type" value="O"> Create branch in {{ product_org }} </input>
                    <small class="form-text text-muted">
                        To be used by development team for dev / test branch creation
                    </small>
                </div >
            </div>
            {% if product not in no_qa_product_list %}
              <div class="form-group row">
                  <div class="col-sm-10">
                  <input type="radio" name="create_branch_type" value="P"> Create branch in {{ product_org }} & it's dependent github orgs </input>
                      <small class="form-text text-muted">
                          To be used by dev managers & tech leads for dev branches which needs QA build support along with other releng features.
                      </small>
                  </div>
              </div>
            {% endif %}
            <div class="form-group row" >
                <div class="col-sm-10">
                    {{form.submit(class="btn btn-info", style = "width:10%")}}
                </div>
            </div>
    </form>
</div>

{% endblock %}
