{% extends 'product_base.html' %}
{% block home %}

<div class="col-xs-6 col-sm-9">
    <form method="post" >
        {{form.hidden_tag()}}
				<div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Create Branch in {{product_org}}</h1>
                <p class="lead"> Create branch & do branch reference changes </p>
            </div>
        </div>
				<div class="form-group row">
            <label for="branch" class="col-md-2 control-label" style="text-align:left;"> Branch Name<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.branch(size=20, onkeyup="this.value = this.value.toLowerCase();") }}
							<small id="HelpBlock" class="form-text text-muted">
	                Branch name should start with dev- or test- & needs to be in lower case
	            </small>
						</div>
        </div>
				<div class="form-group row">
            <label for="branch" class="col-sm-2 col-form-label" style="text-align:left;"> Parent Branch<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.parent_branch(size=20) }}
							<small id="HelpBlock" class="form-text text-muted">
									parent branch name
							</small>
						</div>
        </div>
        <div class="form-group row">
            <label for="repo_level" class="col-md-2 control-label" style="text-align:left;">Repositories<font color="red"> *</font></label>
            <div class="col-sm-10" disabled="false">  {{ form.select_all_repos(onchange="repo_list_check()") }} All Repos <br>
	          	<select multiple name="repo_list" id="repo_list_selected" method="GET" disabled="false" action="/">
	              {% for repo in repo_info %}
	              <option name="repo_list" value="{{repo}}">{{repo}}</option>
	              {% endfor %}
	            </select>
	            <small id="HelpBlock" class="form-text text-muted">
	                To select multiple items in a list, hold down the Ctrl (PC) or Command (Mac) key. Then click on your desired items to select.
	            </small>
							</div>
        </div>
        <script>
          function repo_list_check(){
            var checked = document.getElementById('select_all_repos').checked
            if (checked){
              //document.getElementById('repos').disabled = false
              document.getElementById('repo_list_selected').disabled = true
            } else {
              //document.getElementById('repos').disabled = true
              document.getElementById('repo_list_selected').disabled = false
            }
          }
          repo_list_check()
        </script>
            <div class="form-group row">
                <label for="branch" class="col-sm-2 col-form-label" style="text-align:left;">Change Branch Reference </label>
                <div class="col-sm-10"> {{ form.create_branch_ref }}
                  <small id="HelpBlock" class="form-text text-muted">
                      There are certain files which have branch name reference like base.go.mod.
                      <br> If you wish to change those file, enable this option
                  </small>
                </div>
            </div>
            <div class="form-group row">
                <label for="cdets_id" class="col-sm-2 col-form-label" style="text-align:left;">CDETS_ID </label>
                <div class="col-sm-10"> {{ form.cdets_id }}
                    <small id="HelpBlock" class="form-text text-muted">
                        You may enter the CDETS ID to be used for change reference commits
                        <br> This is an optional field
                    </small>
                </div>
            </div>
        <div class="form-group row" >
                {{form.submit(class="btn btn-info", style = "width:15%", id="create_button", value = "Create Branch")}}
        </div>
    </form>
</div>
{% endblock %}
