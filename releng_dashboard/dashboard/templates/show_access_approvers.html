{% extends 'product_base.html' %}
{% block home %}

<div class="col-xs-6 col-sm-9">

    <br>
    <div class="row">
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Access Control Home</h5>
                    <p class="card-text">Access Control Home Page </p>
                    <a href="/access_control_home/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Submit Request</h5>
                    <p class="card-text">Submit Access Request</p>
                    <a href="/create_access_request/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>

        <!--div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Process Request</h5>
                    <p class="card-text">Approve/Deny Access Request</p>
                    <a href="/approve_access_request/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div-->
    </div>
    <br>
    <br>
    <p class="h2"> Approvers for github org : {{product_org}} </p>
    <br><br><br>

    <p class="h5"> For READ & WRITE requests : Below approvers will be contacted </p>
    <br>
    <table id="approvers" class="table table-striped table-bordered" style="width: 30%">
        <thead class="table-primary">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col" style="min-width:200px">CEC ID</th>
            <th scope="col" style="min-width:250px">USER NAME</th>
            <th scope="col">ROLE</th>
        </tr>
        </thead>
        <tbody>
        {% for approver in access_approver_list %}
        <tr>
            <td><a href=https://directory.cisco.com/dir/reports/{{approver['CEC_ID']}}>{{ approver['CEC_ID'] }}</a></td>
            <td>{{approver['CEC_FULLNAME']}}</td>
            <td>{{approver['ROLE']}}</td>
        </tr>
        {% endfor %}
        </tbody>
    </table>
    <br>

</div>
{% endblock %}
