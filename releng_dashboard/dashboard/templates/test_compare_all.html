{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
$(document).ready(
function() {
$('#functional_suite').DataTable({
        "order": [[ 0, "desc" ]],
    });
});

</script>
<script>
 $(document).ready(function () {
   $("input[name={{product}}_CompareBuilds]").change(function () {
      var maxAllowed = 3;
      var cnt = $("input[name={{product}}_CompareBuilds]:checked").length;
      if (cnt > maxAllowed) 
      {
         $(this).prop("checked", "");
         alert('Maximum ' + maxAllowed + ' builds are allowed for comparision!');
     }
  });
});
</script>


<div class="col-xs-6 col-sm-10">
    <form method="POST">
    {{form.hidden_tag()}}
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-4">Generate Tests Compare Report</h1>
            <p class="lead"> This will generate tests comparision report for selected builds.</p>
        </div>
    </div>
    {% if sanity_result %}
        <div class="col-xs-6 col-sm-10">
            <table id="functional_suite" class="table table-striped table-bordered" style="width: 95%">
                <thead class="table-primary">
                <tr style="text-align:center; background-color:#000000;color:white">
                    <th scope="col">Start Time</th>
                    <th scope="col">Branch</th>
                    <th scope="col">Build #</th>
                    <th scope="col">Job Type</th>
                    <th scope="col">Pass Count<br>Fail Count</th>
                    <th scope="col">Total Count<br> Pass Percentage</th>
                    <th scope="col">Select ?</th>
                </tr>
                </thead>
                <tbody>
                {% for sanity_report in sanity_result %}
                <tr style="text-align:center">
                {% if sanity_report['start_time'] %}
                    <td>{{ sanity_report['start_time']}}</td>
                {% else %}
                    <td> NA </td>
                {% endif %}
                    <td>{{sanity_report['branch_name']}}</td> 
                    <td>{{sanity_report['build_number']}} </td>
                    <td>{{sanity_report['job_type']}}</td>
                    <td style="text-align:left">
                        {% if sanity_report['passed_case_count'] %}
                        Pass Count: {{sanity_report['passed_case_count']}} 
                        {% endif %}
                        <br>
                        {% if sanity_report['failed_case_count'] %}
                          Fail Count: {{sanity_report['failed_case_count']}}
                        {% endif %}
                    </td>
                    {% if (sanity_report['total_feature_file_count'] )not in (None,"","none") %}
                    <td>Total Count: {{sanity_report['total_feature_file_count']}}
                    <br>
                        {% if ((sanity_report["passing_criteria"] not in (None,"","none")) and (sanity_report["passed_percentage"] not in (None,"","none"))) %}
                            {% if sanity_report["passing_criteria"] > sanity_report["passed_percentage"] %}
                                <p style="color:red;">Pass Percentage:{{sanity_report['passed_percentage']}}%</p>
                            {% else %}
                                <p style="color:green;">Pass Percentage:{{sanity_report['passed_percentage']}}%</p>
                            {% endif %}
                        {% else %} 
                            Pass Percentage: {{sanity_report['passed_percentage']}}%
                        {% endif %}
                        </td>
                    {% else %}
                        <td></td>
                    {% endif %} 
                    <td>
                        <input type="checkbox" id="{{ sanity_report['build_number'] }}" name="{{product}}_CompareBuilds" value="{{ sanity_report['build_number'] }}">
                    </td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        </br>
        {{ form.submit(class="btn btn-info",style = "width:10%") }}
              
    {% else %}
        </br>
        </br>
        <h3 style="text-align:left">Sanity results not available for : {{product}}</h3>   
    {% endif %}
</form>
</div>
{% endblock %}
