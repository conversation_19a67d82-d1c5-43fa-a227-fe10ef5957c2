{% extends 'product_base.html' %}
{% block home %}

<div class="container">
    <br>
    <p class="h3"> Throttle request approvers for {{product}} </p>
    <br>
    <table id="approvers" class="table table-striped table-bordered" style="width: 30%">
        <thead class="table-primary">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col" style="min-width:200px">CEC ID</th>
            <th scope="col" style="min-width:250px">USER NAME</th>
            <th scope="col">ROLE</th>
        </tr>
        </thead>
        <tbody>
        {% for approver in tt_approver_list %}
                <tr>
                    <td><a href=https://directory.cisco.com/dir/reports/{{approver['CEC_ID']}}>{{ approver['CEC_ID'] }}</a></td>
                    <td>{{ approver['CEC_FULLNAME'] }}</td>
                    <td>{{ approver['ROLE'] }}</td>
                </tr>
        {% endfor %}
        </tbody>

    </table>
    <br>
</div>
{% endblock %}
