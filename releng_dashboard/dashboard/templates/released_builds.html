{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#qa_build').DataTable({
        "order": [],
        "pageLength": 10
    });
} );
</script>

{% set beta_release_list = ['Engineering-drop','EFT'] %}

<div class="col-xs-6 col-sm-9">
    {% if releases %}
        <div class="fw-body" >
            <div class="content">
                <br>
                <br>
                <h3 style="text-align:left">Released Builds for {{product}}</h3>
                <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                    <thead class="table-info">
                        <tr style="text-align:center; background-color:#000000;color:white">
                            <th class="th-sm" scope="col">Date <br> RER # </th>
                            <th class="th-sm"scope="col">Release Number <br> Release Type</th>
                            <th class="th-sm" scope="col">Posting Type</th>
                            <th class="th-sm" scope="col">Customer</th>
                            <th class="th-sm" scope="col">Images</th>
                            {% if product == 'upf' %}
                                <th class="th-sm" scope="col">StarOS Build #<br> Branch Name</th>
                            {% else %}
                                <th class="th-sm" scope="col">QA Build #<br> Branch Name</th>
                            {% endif %}

                            <th class="th-sm" scope="col">Reports</th>
                            <th class="th-sm" scope="col">Test Reports: Pass %</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for build in releases %}
                        <tr style="text-align:center">
                            {% if build.promoted_on %}
                                <td> {{ build.promoted_on }} <br>
                                {% if build.rer_number %}
                                    {% if product == 'staros' %}
                                        <a href = https://rtp-mitg7-gnats.cisco.com:7443/cgi-bin/relengreq.pl?debug=&database=Releng-Requests&cmd=view+audit-trail&cmd=view&pr={{ build.rer_number }}>RER # {{ build.rer_number }}</a>
                                    {% else %}
                                        <a href = https://rtp-mitg7-gnats.cisco.com:7443/cgi-bin/5G-relengreq.pl?database=5G-Releng-Requests&cmd=view+audit-trail&pr={{ build.rer_number }}>RER # {{ build.rer_number }}</a>
                                    {% endif %}
                                </td>
                                {% else %}
                                   </td>
                                {% endif %}
                            {% else %}
                                 <td> </td>
                            {% endif %}

                            <td> {{ build.release_number }} <br> {{ build.release_type }}</td>

                            <!---Release Posted on <br> Release Posting Type -->
                            <!---Customer -->
                            <!-- Images -->
                                 <td> CCO
                                 {% if build.cco_url %}
                                     {% if build.release_type in beta_release_list %}
                                        <br>Beta Posting </td>
                                        <td> 
                                        {% for customer in build.customer.split(",") %}
                                           {{ customer }}
                                           <br>
                                        {% endfor %}
					</td>
                                     {% else %}
                                        <br>CCO Only </td>
                                        <td> ALL </td>
                                     {% endif %}
                                    <td>
                                        <a href = {{ build.cco_url }}/ target="_blank"> {{ build.builds_products_product_name }}-{{ build.release_number }} </a>
                                    </td>
                                 {% else %}
                                        <br> Hidden with ACL</td>
                                        <td>
                                        {% for customer in build.customer.split(",") %}
                                           {{ customer }}
                                        {% endfor %}
                                        </td>
                                        <td> NA </td>
                                 {% endif %}


                            {% if build.star_os_build %}
                                <td>
                                    <a href = {{ build.source_url }} target="_blank"> {{ build.star_os_build }} </a>
 				                <br>
				                    {{ build.branches_branch_name }}
                                </td>
                            {% else %}
                                <td> {{ build.builds_build_number }}
				                <br>
				                {{ build.branches_branch_name }}
				                </td>
                            {% endif %}

                            <!-- Reports -->
                            <td>
                                {% if build["loc_report"] %}
                                    <a href = {{ build.loc_report }} target="_blank">LOC</a> |
                                {% endif %}
                                {% if build.change_log %}
                                    <a href = {{ build.change_log }} target="_blank">Change log</a>
                                {% endif %}
                                {% if build.sonar_report %}
                                    <br>
                                    <a href = {{ build.sonar_report }} target="_blank">Sonar Report</a>
                                {% endif %}
                                {% if build.corona_report %}
                                    <br>
                                    <a href = {{ build.corona_report }} target="_blank">Corona Report</a>
                                {% endif %}
                            </td>
                            <!-- Test Reports -->
                            <td>
                                {% if build["wrt_report"] %}
                                    {% if build["ivt_passed_percentage"] %}
                                        <a href = {{ build.wrt_report }} target="_blank"> IVT : {{ build.ivt_passed_percentage }}%
                                            {% if build["ivt_approved"] == "yes" %}
                                            <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="IVT APPROVED"> <i class="fas fa-check"></i> </button></a>
                                        {% endif %}
                                    {% else %}
                                        <a href = {{ build.wrt_report }} target="_blank"> IVT</a>
                                    {% endif %}
                                    <br>
                                {% endif %}
                                {% if build["test_info"] %}
                                    {% for job_type, job_status in build["test_info"].items() %}
                                        {% if job_status == "IN_PROGRESS" %}
                                            <a class="text-secondary" href="/test_report/{{product}}/{{ build.builds_build_number }}/{{ job_type }}">{{ job_type }}</a>
                                            <div class="spinner-border spinner-border-sm text-warning"></div>
                                        {% elif job_status == "PARTIALLY_COMPLETE" %}
                                            <a class="text-secondary" href="/test_report/{{product}}/{{ build.builds_build_number }}/{{ job_type }}">{{ job_type }}</a>
                                        {% else %}
                                            <a href="/test_report/{{product}}/{{ build.builds_build_number }}/{{ job_type }}">{{ job_type }} : {{ job_status }}% </a>
                                        {% endif %}
                                        <br>
                                    {% endfor %}
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
