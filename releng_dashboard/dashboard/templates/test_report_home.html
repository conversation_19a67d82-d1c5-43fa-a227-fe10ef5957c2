{% extends 'product_base.html' %}
{% block home %}
{% set common_product_list = ['app-infra','golang-lib','cnee','lbs-libraries'] %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Generate Tests Compare Report</h1>
                <p class="lead"> This will generate tests comparision report for selected builds.</p>
            </div>
        </div>
            <div class="form-group row">
            <label for="job_type" class="col-sm-2 col-form-label"><b> Job Type :</b></label>
            <div class="col-sm-8">
            {{form.job_type(id="job_type")}}
            </div>
            </div> <br>
            <div class="form-group row">
            <label for="branch_name" class="col-sm-2 col-form-label"><b> Branch Name :</b></label>
            <div class="col-sm-8">
            {{form.branch_name(id="branch_name")}}
            </div>
            </div> <br>
            <div class="form-group row" >
                <div class="col-sm-10">
                    {{form.submit(class="btn btn-info", style = "width:10%")}}
                </div>
            </div>
    </form>
</div>

{% endblock %}