{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#qa_build').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
        {% if db_del_branch_info %}
        <div class="fw-body" >
            <div class="content">
                <br>
                <br>
                <h3 style="text-align:left">Branches Deleted Via Dashboard : {{product_org}}</h3>
                <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                    <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm"scope="col">Branch Name</th>
                        <th class="th-sm" scope="col">Repo</th>
                        <th class="th-sm" scope="col">Deleted By </th>
                        <th class="th-sm" scope="col">Deleted Date</th>
                        <th class="th-sm" scope="col">HEAD SHA</th>                            
                    </tr>
                    </thead>
                    <tbody>
                    {% for branch in db_del_branch_info %}
                        <tr style="text-align:center">
                            <th scope="row">{{ branch.branch_name }}</th>
                            <td>{{ branch.repo }}</td>
                            <td>{{ branch.deleted_by }}</td>
                            <td>{{ branch.deleted_date }}</td>
                            <td>{{ branch.head_sha }}</td>                                
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>

            </div>
        </div>
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">No branches have been deleted via dashboard : {{product_org}}</h3>
        {% endif %}
</div>

{% endblock %}
