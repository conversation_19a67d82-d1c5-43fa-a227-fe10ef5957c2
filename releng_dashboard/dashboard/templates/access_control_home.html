
{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
$(document).ready(function() {
$('#ACCESS_REQUESTS').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
} );


 // function showHideRow(row, note, approver, approver_comment) {
  function showHideRow(row, note) {
      $("#" + row).toggle();
      row_content = ' '
      if (note !== 'None') {
        row_content = note +'<br/>' 
      }
      //if(approver_comment !=='None'){
	 // row_content = row_content +'<b>'+approver +'</b>:' +approver_comment
      //}
      
      //alert(row_content)      
      //document.getElementById('td_'+row).innerHTML = note
      if (row_content !== 'None') {
	  document.getElementById('td_'+row).innerHTML = row_content
      }
  }

 function closeDialog(id) {
 //alert(id)
 // this.form.submit()		    
 $('#'+id).modal('hide')
} 				      
</script>

<div class="col-xs-6 col-sm-9">
    <br>
    <div class="row">
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Submit Request</h5>
                    <p class="card-text">Submit Access Request</p>
                    <a href="/create_access_request/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Show Approvers </h5>
                    <p class="card-text">List of Approvers </p>
                    <a href="/show_access_approvers/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>

        <div class="fw-body" >
            <div class="content">
                <br>
                <br>
                {% if show_active_button %}
                    <h3 style="text-align:left">All Access Requests for : {{product_org}}  </h3>
                {% else %}
                    <h3 style="text-align:left">Pending Access Requests for : {{product_org}}  </h3>
                {% endif %}
                <br>
                <br>

                <table id ="ACCESS_REQUESTS" class="table table-striped table-bordered" style="border: 1px solid gray;width:110%;">
                    <thead class="table-info">
                        <tr style="text-align:center; background-color:#000000;color:white">
                            <th scope="col" style="width: 5%">ID</th>
                            <th scope="col" style="width: 10%">CEC ID</th>
                            <th scope="col" style="width: 15%"> Submit Date  </th>
                            <th scope="col" style="width: 10%"> Submitter </th>
                            <th scope="col" style="width: 10%"> Repo </th>
                            <th scope="col" style="width: 5%"> Access Type </th>
                            <th scope="col" style="width: 15%">Comments </th>
                            <th scope="col" style="width: 10%">Approver</th>
                            <th scope="col" style="width: 10%">Status </th>
                            <th scope="col" style="width: 10%">Edit Request</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for request in req_list %}
                            <tr style="text-align:center" onclick="showHideRow({{request['id']}}, '{{note_dic[request['id']]}}');">
                                <th scope="row">{{request['id']}}</th>
                                <td><a href=https://directory.cisco.com/dir/reports/{{request['USERID']}}>{{request['USERID']}}</a></td>
                                <td>{{request['SUBMIT_DATE']}}</td>
                                <td><a href=https://directory.cisco.com/dir/reports/{{request['SUBMITTER']}}>{{request['SUBMITTER']}}</a></td>
                                <td>{{request['REPO']}}</td>
                                <td>{{request['TYPE_ID']}}</td>
                                {% if note_dic[request['id']] != null %}
                                    <td align="left">  {{request['JUSTIFICATION'] + "  ... +"   }}</td>
                                {% else %}
                                    <td>{{request['JUSTIFICATION']}}</td>
                                {% endif %}
                                <td>
                                    {% for id in request['APPROVER'].split(",") %}
                                        <a href=https://directory.cisco.com/dir/reports/{{ id }}>{{ id }}</a>
                                        <br>
                                    {% endfor %}
                                </td>

                                {% if request['STATUS'] == 'Pending' %}
                                    <td><font color = "orange">{{request['STATUS']}}</font></td>
                                {% elif request['STATUS'] == 'Approved' %}
                                    <td><font color = "green">{{request['STATUS']}}</font></td>
                                {% elif request['STATUS'] == 'Denied' %}
                                    <td><font color = "red">{{request['STATUS']}}</font></td>
                                {% else %}
                                    <td>{{request['STATUS']}}</td>
                                {% endif %}
                                {% if request['STATUS'] in  ['Pending', 'Need Info'] %}
                                    <td>
                                        <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#edit{{ loop.index }}" title="approve"><i class="fas fa-edit"></i></button>
                                        <div class="modal fade" id="edit{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h6 class="modal-title" id="submit">Are you sure you want to edit request number : {{request['id']}} for CEC-ID : {{ request['USERID'] }} ? </h6>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    <form method="POST">
                                                        {{form.csrf_token()}}
                                                        <div class="modal-body">
                                                            <label> Comments </label>
                                                            <div>
                                                                {{form.approver_comment(required='required') }}
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                          {% if user_role == "ORG_OWNER" or user_role == "RELENG" %}
							        {% if request['STATUS'] == 'Need Info' %}
							              {{form.add_info(class="btn btn-dark")}}
							        {% else %}
							              {{form.req_info(class="btn btn-info")}}
							        {% endif %}
                                                                {{form.approve(class="btn btn-success")}}
                                                                {{form.deny(class="btn btn-danger")}}
                                                                {{form.cancel(class="btn btn-warning")}}
                                                            {% else %}
                                                                {{form.add_info(class="btn btn-dark")}}
                                                            {% endif %}
                                                            {{form.id(value=request['id'] ) }}
                                                            {{form.userid(value=request['USERID']) }}
                                                            {{form.type_id(value=request['TYPE_ID']) }}
                                                            {{form.category_id(value=request['CATEGORY_ID']) }}
                                                            {{form.req_submitter(value=request['SUBMITTER']) }}
                                                            {{form.approver_list(value=request['APPROVER']) }}
                                                            {{form.repo(value=request['REPO']) }}
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </td>
                                {% else %}
                                    <td> </td>
                                {% endif %}
                            </tr>
                            <tr id="{{request['id']}}" class="hidden_row" style="display: none;">
                                <td id='td_{{request['id']}}' align="left" colspan=10>{{request['JUSTIFICATION']}}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <form method="POST">
                    {{form.hidden_tag()}}
                    {% if show_active_button %}
                    {{form.submit_active(class="btn btn-info", style = "width:30%")}}
                    {% else %}
                    {{form.submit_all(class="btn btn-info", style = "width:30%")}}
                    {% endif %}
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
