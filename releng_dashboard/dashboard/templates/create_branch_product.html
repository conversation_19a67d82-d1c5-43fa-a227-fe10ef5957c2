{% extends 'product_base.html' %}
{% block home %}

<div class="col-xs-6 col-sm-9">
    <form method="post" >
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Create branch for product {{ product.upper()}}</h1>
                <p id="HelpBlock" class="form-text text-muted">
                    <font color="green">
                           1. Branch will be created in {{ product_org }} & it's dependent orgs
                        <br><b>Based on options selected :</b>>
                        <br> 2. Developers would need to create a PR to push changes to this branch
                        <br> 3. SA, S6 roll up, CDETS, Unit Test will be added as mandatory PR checks
                        <br> 4. QA build will be enabled on this branch.
                        <br> 5. Branch created will be protected from auto deletion till branch is expired
                    </font>
                </p>
            </div>
        </div>
				<div class="form-group row">
            <label for="branch" class="col-md-2 control-label" style="text-align:left;"> {{form.branch_name.label}}<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.branch_name(size=20, onkeyup="this.value = this.value.toLowerCase();") }}
							<small id="HelpBlock" class="form-text text-muted">
	                Branch name should start with dev- /test- & needs to be in lower case
	            </small>
						</div>
        </div>
        <div class="form-group row">
            <label for="branch" class="col-sm-2 col-form-label" style="text-align:left;">{{form.branch_description.label}}<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.branch_description(size=60) }}
							<small id="HelpBlock" class="form-text text-muted">
									Add the purpose of the branch as branch description
							</small>
						</div>
        </div>
        <div class="form-group row">
            <label for="branch_name" class="col-sm-2 col-form-label">{{form.parent_branch_name.label}}<font color="red"> *</font></label>
            <div class="col-sm-10">
                {{form.parent_branch_name(id="parent_branch_name")}}
            </div>
        </div>
        <div class="form-group row">
            <label for="branch" class="col-sm-2 col-form-label" style="text-align:left;"> {{form.parent_QA_build_no.label}} <font color="red"> *</font> </label>
            <div class="col-sm-10">  {{ form.parent_QA_build_no(size=20) }}
							<small id="HelpBlock" class="form-text text-muted">
									Here latest means latest QA build available. If you want to create branch from HEAD of branch plz create a QA build before proceeding.
							</small>
						</div>
        </div>

        <div class="form-group row">
            <label for="enable_branch_protection" class="col-sm-2 col-form-label" style="text-align:left;"> Enable branch protection </label>
            <div class="col-sm-10">
                <input class="form-check-input" type="checkbox" name="enable_branch_protection" id="enable_branch_protection" default='false' >
                <small id="HelpBlock" class="form-text text-muted">
                    Select this option if you want to protect the branch and need PR to be raised to commit to this branch.
                </small>
            </div>
        </div>

        <div class="form-group row">
            <label for="trigger_qa_build" class="col-sm-2 col-form-label" style="text-align:left;"> Enable QA Builds </label>
            <div class="col-sm-10">
                <input class="form-check-input" type="checkbox" name="trigger_qa_build" id="trigger_qa_build" default='false' >
                <small id="HelpBlock" class="form-text text-muted">
                    Select this option if you ability to trigger QA build. (SELECT this ONLY if you foresee release getting out from this branch.)
                </small>
            </div>
        </div>

        <div class="form-group row">
            <label for="branch" class="col-sm-2 col-form-label" style="text-align:left;"> {{form.expiry_date.label}}</label>
            <div class="col-sm-10">  {{ form.expiry_date(class='datepicker') }}
            <small id="HelpBlock" class="form-text text-muted">
									If not entered, 1 year from now will be taken as default expiry date.
							</small>
            </div>
				</div>

        <div class="form-group row">
            <label for="cdets_id" class="col-sm-2 col-form-label" style="text-align:left;">CDETS_ID </label>
            <div class="col-sm-10"> {{ form.cdets_id }}
                <small id="HelpBlock" class="form-text text-muted">
                    You may enter the CDETS ID to be used for change reference commits
                    <br> This is an optional field
                </small>
            </div>
        </div>
        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:15%", id="create_button", value = "Create Branch")}}
            </div>
        </div>
    </form>
</div>
{% endblock %}
