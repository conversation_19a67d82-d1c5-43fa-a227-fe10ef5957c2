{% extends 'access_control_home.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Submit MYID Join Request</h1>
                <p class="lead"> This will submit MYID join request to have Artifactory access</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="product_name" class="col-sm-2 col-form-label">Github Organization</label>
            <div class="col-sm-10">
                {{ product_org }}
            </div>
        </div>
        <div class="form-group row">
            <label for="userid" class="col-md-2 control-label" style="text-align:left;">CEC ID<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.userid(required='required') }}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma separated cec ids
                </small>
            </div>
        </div>

        <div class="form-group row">
            <label for="type_id" class="col-sm-2 col-form-label"> Access Type<font color="red"> *</font></label>
            <div class="col-sm-10">
                download
            </div>
        </div>
        <div class="form-group row" id="repo_list">
            <label for="justification" class="col-sm-2 col-form-label" style="text-align:left;">{{ form.justification.label }}<font color="red"> *</font></label>
            <div class="col-sm-10"> {{ form.justification(required='required')}} </div>
        </div>
        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
        </div>
</div>

{% endblock %}