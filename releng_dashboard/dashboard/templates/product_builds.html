{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#qa_build').DataTable({
        "order": [],
        "pageLength": 10
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
    {% if qa_build_info %}
        <div class="fw-body" >
            <div class="content">
            <br>
            <br>
            {% if show_all_button %}
                <h3 style="text-align:left">Latest 100 Builds for {{product.upper()}}</h3>
            {% else %}
                <h3 style="text-align:left">All Builds for {{product.upper()}}</h3>
            {% endif %}
            <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm" scope="col">Build Start Time</th>
                        <th class="th-sm" scope="col">Triggered By</th>
                        <th class="th-sm"scope="col">Branch<br> Build #</th>
                        {% if product == 'upf' %}
                            <th class="th-sm" scope="col">StarOS Build #</th>
                        {% endif %}
                        <th class="th-sm" scope="col">Images</th>
                        <th class="th-sm" scope="col">Reports</th>
                        <th class="th-sm" scope="col">Test Reports: Pass %</th>
                    </tr>
                </thead>
                <tbody>
                {% for build in qa_build_info %}
                    <tr style="text-align:center">
                        <td> {{ build.build_start_time }} <td>
                        {% if build.triggered_by == "cnblds" %}
                            <ln class="text-secondary">scheduled run</ln>
                        {% else %}
                            <ln><a class="text-info" href=https://directory.cisco.com/dir/reports/{{ build.triggered_by }}>{{ build.triggered_by }}</a></ln>
                        {% endif %}
                        </td>
                        {% if build.build_status == "Passed" %}
                            <td> {{ build.branches_branch_name }} <br> <b class="text-success"> {{ build.build_number }}</b></td>
                        {% elif build.build_status == "Failed" %}
                            <td> {{ build.branches_branch_name }} <br> <b class="text-danger"> {{ build.build_number }}</b></td>
                        {% else %}
                            <td> {{ build.branches_branch_name }} <br> <b class="text-danger"> {{ build.build_number }}</b></td>
                        {% endif %}
                        {% if product == 'upf' %}
                          {% if build.star_os_build %}
                            <td>
                              <a href={{ build.source_url }} target="_blank"> {{ build.star_os_build }} </a>
                            </td>
                            {% else %}
                              <td></td>
                            {% endif %}
                        {% endif %}
                        <!-- Images -->
                        <td>
                            {% if build.int_artifactory %}
                                <a href = {{ build.int_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }} </a>
                            {% else %}
                                <ln class="text-secondary">NA</ln>
                            {% endif %}
                            <br>
                            {% if product != 'upf' %}
                                {% if build.off_artifactory %}
                                    <a href = {{ build.off_artifactory }}/ target="_blank"> {{ build.products_product_name }}-{{ build.build_number }}-offline </a>
                                {% else %}
                                    <ln class="text-secondary">NA</ln>
                                {% endif %}
                                <br>
                                {% if build.cdl_version %}
                                    CDL:  <a href ={{ releng_cdl_artifactory }}{{ build.cdl_version }}-offline/ target="_blank"> {{ build.cdl_version }} </a>
                                {% endif %}
                            {% endif %}
                        </td>
                        <!-- Reports -->
                        <td>
                            {% if build["loc_report"] %}
                                <a href = {{ build.loc_report }} target="_blank">LOC</a>
                            {% else %}
                                <ln class="text-secondary">NA</ln>
                            {% endif %}
                              |  
                            {% if build.change_log %}
                                <a href = {{ build.change_log }} target="_blank">Change log</a>
                            {% else %}
                                <ln class="text-secondary">NA</ln>
                            {% endif %}
                            {% if build.sonar_report %}
                               <br> <a href = {{ build.sonar_report }} target="_blank">Sonar Report</a>
                            {% endif %}
           		    <!--
                            {% if build["cdets_list"] %}
                                <br>
                                <a href="#" data-toggle="tooltip" title="{{ build.cdets_list }}">CDETS List</a>
                            {% endif %}
                            -->
                            <br>
                            {% if build.corona_report %}
                                <a href = {{ build.corona_report }} target="_blank">Corona Report</a>
                            {% endif %}
                            <!--{% if build["cdets_list"] %}
                                <br>
                                {% set ir_report = build.change_log.split('/')[:-1] | join('/') %}
                                {% set ir_report = ir_report + '/Update_IR_Report.html' %}
                                {% if build.ir_report_link %}
                                    <a href = {{ ir_report }} target="_blank">CDETS List</a>
                                {% else %}
                                    <a href="#" data-toggle="tooltip" title="{{ build.cdets_list }}">CDETS List</a>
                                {% endif %}
                            {% endif %}-->

                            {% if build["staros_build_info"] %}
                            <br>
                            <a href = {{ build.staros_build_info }} target="_blank">StarOS_Build_Number</a>
                            {% endif %}
                        </td>
                        <!-- Test Reports -->
                        <td>
                            {% if build["wrt_report"] %}
                                {% if build["ivt_passed_percentage"] %}
                                    <a href = {{ build.wrt_report }} target="_blank"> IVT : {{ build.ivt_passed_percentage }}%
                                    {% if build["ivt_approved"] == "yes" %}
                                        <button type="button" class="btn btn-outline-success btn-sm" data-toggle="tooltip" data-placement="top" title="IVT APPROVED"> <i class="fas fa-check"></i> </button></a>
                                    {% endif %}
                                {% else %}
                                    <a href = {{ build.wrt_report }} target="_blank"> IVT</a>
                                {% endif %}
                                <br>
                            {% endif %}
                            {% if build["test_info"] %}
                                {% for job_type, job_status in build["test_info"].items() %}
                                    {% if job_status == "IN_PROGRESS" %}
                                        <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                                        <div class="spinner-border spinner-border-sm text-warning"></div>
                                    {% elif job_status == "PARTIALLY_COMPLETE" %}
                                        <a class="text-secondary" href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }}</a>
                                    {% else %}
                                        <a href="/test_report/{{product}}/{{ build.build_number }}/{{ job_type }}">{{ job_type }} : {{ job_status }}% </a>
                                    {% endif %}
                                <br>
                                {% endfor %}
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
		                    NA - Build not available (deleted / not generated)
            <br> <br>
            <form method="POST">
                {{form.hidden_tag()}}
                {% if show_all_button %}
                    {{form.show_all(class="btn btn-info", style = "width:30%")}}
                {% else %}
                    {{form.show_100(class="btn btn-info", style = "width:30%")}}
                {% endif %}
            </form>
            <br>
        </div>
    {% endif %}
        </div>
</div>
{% endblock %}
