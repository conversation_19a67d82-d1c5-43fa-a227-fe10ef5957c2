{% extends 'access_control_home.html' %}
{% block home %}
{% set access_req_qa_pdt = ['amf','smf','pcf','bng','chf','nrf','sgw'] %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Submit MYID Join Request</h1>
                <p class="lead"> This will submit MYID join request to have GitHub, Sonar and Artifactory access</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="product_name" class="col-sm-2 col-form-label">Github Organization</label>
            <div class="col-sm-10">
                {{ product_org }}
            </div>
        </div>
        <div class="form-group row">
            <label for="userid" class="col-md-2 control-label" style="text-align:left;">CEC ID<font color="red"> *</font></label>
            <div class="col-sm-10">  {{ form.userid(required='required')}}
                <small id="HelpBlock" class="form-text text-muted">
                    Comma separated cec ids
                </small>
            </div>
        </div>

        <div class="form-group row">
            <label for="access_level" class="col-sm-2 col-form-label"> Access level<font color="red"> *</font></label>
            <div class="col-sm-10">
                {% if product_org == "mobile-cnat-golang-lib" %}
                <input type="radio" name="access_level" value="D" id="app-infra-option">
                <label for="app-infra-option">&nbsp; Other &ensp;</label>
                <select multiple name="repo_list" disabled="false">
                    {% for repo in dev_repos %}
                    <option name="repo_list" value="{{repo}}">{{repo}}</option>
                    {% endfor %}
                </select>
                <small id="HelpBlock" class="form-text text-muted"> &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
                    List of repos you would get access to.This is read-only
                </small>
                <br>
                <input type="radio" name="access_level" value="Q" id="app-infra-option">
                <label for="app-infra-option">&nbsp; app-infra &ensp;</label>
                {% else %}
                <input type="radio" name="access_level" value="D">&nbsp; Dev &ensp;
                <select multiple name="repo_list" disabled="false">
                    {% for repo in dev_repos %}
                    <option name="repo_list" value="{{repo}}">{{repo}}</option>
                    {% endfor %}
                </select></input>
                <small id="HelpBlock" class="form-text text-muted"> &emsp;&emsp;&emsp;&emsp;&emsp;
                    List of repos you would get access to.This is read-only
                </small>
                <br>
                <br>
                {% if product in access_req_qa_pdt %}
                <input type="radio" name="access_level" value="Q">&nbsp; QA &ensp;</input>
                <select multiple name="repo_list" disabled="false">
                    {% for repo in qa_repos %}
                    <option name="repo_list" value="{{repo}}">{{repo}}</option>
                    {% endfor %}
                </select>
                <small id="HelpBlock" class="form-text text-muted"> &emsp;&emsp;&emsp;&emsp;&emsp;
                    List of repos you would get access to.This is read-only
                </small>

                {% endif %}
                {% endif %}
            </div>
        </div>
        <div class="form-group row">
            <label for="type_id" class="col-sm-2 col-form-label"> Access Type<font color="red"> *</font></label>
            <div class="col-sm-10">
                {{form.type_id(id="type_id")}}
            </div>
        </div>
        <div class="form-group row" id="repo_list">
            <label for="justification" class="col-sm-2 col-form-label" style="text-align:left;">{{ form.justification.label }}<font color="red"> *</font></label>
            <div class="col-sm-10"> {{ form.justification(required='required')}} </div>
        </div>
        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
        </div>
</div>
<script>
    function hidefun(appinfra) {
     var type_id_opt = document.getElementById("type_id");
     if (appinfra=='Q') {
      var type_id_option = document.getElementById("type_id").value;
      $("#type_id [value='read']").hide();
     }
     else {
      $("#type_id [value='read']").show();
     }
    }
</script>
{% endblock %}