{% extends 'base.html' %}
{% block content %}
{% set smi_product_list = ['base-vm','cee','deployer','base-bm','cee-bm','deployer-bm'] %}
{% set no_build_products = ['app-infra','golang-lib','cnee','smf','sgw','lbs-libraries'] %}
{% set common_products = ['app-infra','golang-lib','cnee','lbs-libraries'] %}
{% set packet_core_product_list = ['upf','staros'] %}
{% set no_access_req_list = ['pgw','udc','upf'] %}
{% set up_recovery_skip_op = ['up-recovery','smi-nso'] %}
{% set up_recovery = ['up-recovery'] %}

<div class="row">
    <div class="col-xs-6 col-sm-2">
    <nav class="navbar navbar-light bg-light">
        <div id="navbarSupportedContent">
            <ul class="navbar-nav">
                <li class="nav-item" >
                    <a class="nav-link" href="/released_builds/{{product}}"><font color="black"size="3">Released builds</font></a>
                </li>
                <div class="dropdown-divider"></div>
                {% if product not in common_products %}
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="qaBuild" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            QA Build & Test
                        </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                            {% if product not in no_build_products %}
                                <a class="dropdown-item" href="/qabuild/{{product}}">Trigger QA build</a>
                            {% endif %}
                                <a class="dropdown-item" href="/builds/{{product}}/all">Search old QA builds</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="/test_compare_home/{{product}}">Compare Test Results</a>
                        </div>
                    </li>
                {% endif %}
                <li class="nav-item dropdown active">
                    <a class="nav-link dropdown-toggle" href="#" id="devBuilds" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        Dev Build & Test
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                        {% if product not in no_build_products %}
                            <a class="dropdown-item" href="/private_offline_build/{{product}}">Private offline build</a>
                        {% endif %}
                        {% if product not in packet_core_product_list %}
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/sonarrrun/{{product}}">Execute Sonar</a>
                        {% if product not in no_build_products %}
                            <a class="dropdown-item" href="/pull_request/{{product}}">Execute Smoke Test</a>
                        {% endif %}
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/corona_scan_home/{{product}}">Corona Scan</a>
                        {% endif %}
                    </div>
                </li>
                <div class="dropdown-divider"></div>
              {% if product not in smi_product_list %}
                {% if product != "staros" %}
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="locOperation" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Quality Statistics
                        </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <a class="dropdown-item" href="http://iotdevinsights.cisco.com/#/mobility">IoT Quality Metrics</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-header"><font color="green"size="3">Grafana Dashboards</font></a>
                            <a class="dropdown-item" href="/sanity/{{product}}">Test</a>
                            <a class="dropdown-item" href="/sonarstats/{{product}}">Sonar</a>
                            <a class="dropdown-item" href="/pr_stats/{{product}}">Pull Request</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="sonarOperation" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Quality Reports
                        </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <a class="dropdown-item" href="/loc/{{product}}">LOC</a>
                            <a class="dropdown-item" href="/clocreport/{{product}}">CLOC</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="/sonarreport/{{product}}">Sonar</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="/featureccreporthome/{{product}}">Feature Level Coverage</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="/cdets_diff/{{product}}">Main vs Rel Branch CDETS Diff</a>
                        </div>
                    </li>
                    <div class="dropdown-divider"></div>
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="githubOperation" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Github Operations
                        </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <a class="dropdown-item" href="/bypass_pr_check/{{product}}">Bypass PR Check</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="branchOperation" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Branch Operations
                        </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                          {% if product not in no_build_products %}
                            <a class="dropdown-item" href="/branches/{{product}}">Releng Managed Branches</a>
                          {% endif %}
                          {% if product not in packet_core_product_list and product not in up_recovery_skip_op %}
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="/createbranchhome/{{product}}">Create Branch</a>
                            <a class="dropdown-item" href="/product_sync_homepage/{{product}}">Sync Branch</a>
                            <a class="dropdown-item" href="/deletebranches/{{product}}">Delete Branches</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="/throttle_tracker_home/{{product}}">Throttle Tracker</a>
                            {% endif %}
                        </div>
                    </li>
                {% if product not in up_recovery %}
                <div class="dropdown-divider"></div>
                <li class="nav-item" >
                    <a class="nav-link" href="/manage_dashboard_access/{{product}}"><font color="black"size="3">Dashboard Access</font></a>
                </li>
                <li class="nav-item dropdown active">
                    <a class="nav-link dropdown-toggle" href="#" id="myIDOperation" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        Service Access Via MyID
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                        <a class="dropdown-header"><font color="green"size="3">Github,Artifactory and Sonar</font></a>
                        <a class="dropdown-item" href="/show_myid_groups/{{product}}">Show MYID Groups</a>
                        <a class="dropdown-item" href="/submit_myid_req/{{product}}">Submit/Track Request</a>
                    </div>
                </li>
                {% endif %}
                {% else %}
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="branchOperation" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Branch Operations
                        </a>
                        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <a class="dropdown-item" href="/branches/{{product}}">Releng Managed Branches</a>
                        </div>
                    </li>
                {% endif %}
              {% endif %}
            </ul>
        </div>
    </nav>
    </div>
    {% block home %}
    {% endblock %}
</div>


{% endblock %}
