{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script language="javascript">
$(document).ready(function() {
$('#DashboardSyncRequests').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 2
    });
    $('body').tooltip({selector: '[data-toggle="tooltip"]'
    });
    $('#DashboardMergeData').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
    $('body').tooltip({selector: '[data-toggle="tooltip"]'
    });    
});
</script>

<div class="col-xs-6 col-sm-9">
    <br>
    <div class="row">
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Submit Branch Sync Request</h5>
                    <p class="card-text">Submit Branch Sync Request</p>
                    <a href="/mergebranchrequest/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Show Sync Approvers </h5>
                    <p class="card-text">List of Sync Approvers </p>
                    <a href="/sync_approver/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
    </div>
        {% if current_builds %}
            {% for build_type, build_list in current_builds.items() %}
                {% if build_list %}
                    <br/>
                    <h3 style="text-align:left"> {{build_type}} in Progress (last 24 hours) for {{product}} </h3>
                    <br>
                    <table id="current_build" class="table table-striped table-sm" style="border: 1px solid gray;">
                    <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm" scope="col" width="30">Build</th>
                            <th class="th-sm" scope="col" width="30">Started At</th>
                            <th class="th-sm" scope="col" width="30">Started By</th>
                            <th class="th-sm" scope="col" width="30">Build Status</th>
                            <th class="th-sm" scope="col" width="30">Notes</th>
                            <th class="th-sm" scope="col" width="300">Progress</th>
                            <th class="th-sm" scope="col" width="30">~Time Remaining</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for build in build_list %}
                            {% if build.build_number %}
                                <tr style="text-align:center">
                                    <td><a href={{ build.build_url }}>#{{ build.build_number }}</a></td>
                                    <td>{{ build.start_time }}</td>
                                    <td><a href=https://directory.cisco.com/dir/reports/{{ build.triggered_by }} }}>{{ build.triggered_by }}</a></td>
                                    <td>{{ build.status }}</td>
                                    {% if build["status"] in ["FAILURE","ABORTED","UNSTABLE"] %}
                                        {% if build["notes"] %}
                                            {% if "html" in build["notes"] %}
                                                <td>{{ build["notes"].split(":",1)[0] }}
                                                <a href={{build["notes"].split(":",1)[1]}}> this report</a>
                                                </td>
                                            {% endif %}
                                        {% else %}
                                            <td> N/A </td>
                                        {% endif %}
                                        <td> N/A </td>
                                        <td> N/A </td>
                                        <td> N/A </td>
                                    {% elif  build.progress %}
                                        <td> N/A </td>
                                        {% if build["progress"] < 100 %}
                                            <td>
                                                <div class="progress">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width:{{ build.progress }}%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">{{ build["progress"] }}%</div>
                                                </div>
                                            </td>
                                            {% if build["eta"] == 0 %}
                                                <td>few seconds</td>
                                            {% else %}
                                                <td>{{ build["eta"] }} mins</td>
                                            {% endif %}
                                        {% else %}
                                            <td>
                                                <div class="progress">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="progressbar" style="width:100%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">{{ build["progress"] }}%</div>
                                                </div>
                                            </td>
                                            <td>Taking more time than estimated</td>
                                        {% endif %}
                                    {% else %}
                                        <td> N/A </td>
                                        <td> N/A </td>
                                        <td> N/A </td>
                                    {% endif %}
                                </tr>
                            {% else %}
                                <tr style="text-align:center">
                                    <td scope="col" width="30"> <font color = "red"> N/A </font></td>
                                    <td>{{ build["start_time"] }}</td>
                                    <td><a href=https://directory.cisco.com/dir/reports/{{ build["triggered_by"] }} }}>{{ build["triggered_by"] }}</a></td>
                                    <td scope="col" width="30">{{ build["status"] }}</td>
                                    <td scope="col" width="300"> <font color = "red"> {{ build["notes"] }} </font> </td>
                                    <td scope="col" width="30"> <font color = "red"> N/A </font> </td>
                                    <td scope="col" width="30"> <font color = "red"> N/A </font> </td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                        </tbody>
                    </table>
                {% endif %}
            {% endfor %}
        {% endif %}

        <!-- Only Pending/Denied Sync Requests -->
        {% if db_merged_branch_info %}
        <div class="fw-body" >
            <div class="content">
                <br>
                <h3 style="text-align:left">Pending/Denied Sync Requests for : {{product_org}}  </h3>
                <br>
                <table id ="DashboardSyncRequests" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                    <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm" scope="col">Merge ID</th>
                        <th class="th-sm" scope="col">Repo List</th>
                        <th class="th-sm" scope="col">Start Time</th>
                        <th class="th-sm" scope="col">Triggered By <br> Source -> Destination</th>
                        <th class="th-sm" scope="col">Approval Status</th>
                        <th class="th-sm" scope="col">Approver</th>
                        <th class="th-sm" scope="col">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for merge_data_value in db_merged_branch_info %}
                        {%if merge_data_value.approval_status in ['Pending','Denied'] %}
                            <tr style="text-align:center">
                                <td>            
                                        {% if merge_data_value.dry_run == '0' %}
                                            {{ merge_data_value['merge_id'] }}
                                        {% endif %}
                                </td>
                                <td>
                                    <a href="#" data-toggle="tooltip" title="{{ merge_data_value.merge_repo }}">Repo List</a>
                                        <div class="tooltip bs-tooltip-top" role="tooltip">
                                            {{ merge_data_value.merge_repo }}
                                        </div>

                                </td>
                                <td>{{ merge_data_value.start_time }}</td>
                                <td><a href=https://directory.cisco.com/dir/reports/{{ merge_data_value.triggered_by }}>{{ merge_data_value.triggered_by }}</a> <br>
                                    {{ merge_data_value.source_branch  }} <i class="fas fa-long-arrow-alt-right"></i> {{ merge_data_value.destination_branch }}
                                </td>
                                {% if merge_data_value['approval_status'] == 'Pending' %}
                                    <td><font color = "orange">{{merge_data_value['approval_status']}}</font></td>
                                {% elif merge_data_value['approval_status'] == 'Denied' %}
                                    <td><font color = "red">{{merge_data_value['approval_status']}}</font></td>    
                                {% endif %}
                                <td>{{ merge_data_value.approver }}</td>
                                <td>
                                        {% if user_role == "RELENG" or user_role == "ORG_OWNER" or user_role == "DEV_MANAGER"   %}
                                            {% if merge_data_value.approval_status == 'Pending' %}
                                                <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#edit{{ loop.index }}" title="Approve"><i class="fas fa-edit"></i></button>
                                                    <div class="modal fade" id="edit{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h6 class="modal-title" id="submit">Are you sure you want to Approve/Deny sync request for MERGE-ID : {{ merge_data_value['merge_id'] }}</h6>
                                                                    <button type="button" class="approve" data-dismiss="modal" aria-label="Approve">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <form method="POST">
                                                                        {{form.csrf_token()}}
                                                                        <div class="modal-body">
                                                                            <div>
                                                                                <label>Approve/Deny the merge sync request</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            {{form.merge_id(value=merge_data_value['merge_id']) }}
                                                                            {{form.approve(class="btn btn-success")}}
                                                                            {{form.deny(class="btn btn-danger")}}
                                                                        </div>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                            {% endif %}
                                        {% endif %}
                                </td>
                            </tr>  
                            {%endif%}      
                    {% endfor %}
                    </tbody>
                </table>
        
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">No Sync Requests Pending via dashboard for {{product_org}}</h3>
        {% endif %}


        <!-- Approved Sync Requests -->
        {% if db_merged_branch_info %}
        <div class="fw-body" >
            <div class="content">
                <br>
                <h3 style="text-align:left"> Merge Requests for : {{product_org}}  </h3>
                <br>
                <table id ="DashboardMergeData" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                    <thead class="table-info">
                    <tr style="text-align:center; background-color:#000000;color:white">
                        <th class="th-sm" scope="col">Merge ID</th>
                        <th class="th-sm" scope="col">Repo List</th>
                        <th class="th-sm" scope="col">Start Time</th>
                        <th class="th-sm" scope="col">Triggered By <br> Source -> Destination</th>
                        <th class="th-sm" scope="col">Merge Status</th>
                        <th class="th-sm" scope="col">Jenkins Build URL</th>
                        <th class="th-sm" scope="col">Report</th>
                        <th class="th-sm" scope="col">Approval Status</th>
                        <th class="th-sm" scope="col">Approver</th>
                        <th class="th-sm" scope="col">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for merge_data_value in db_merged_branch_info %}
                        {%if merge_data_value.approval_status in ['Approved','-'] %}
                            <tr style="text-align:center">
                                <td>            
                                        {% if merge_data_value.dry_run == '0' and merge_data_value.merge_status != 'JOB_UNDERWAY' and merge_data_value.merge_status != 'NO_CHANGE' %}
                                            <a href="/pr_info/{{ merge_data_value['merge_id'] }}/{{ product }}" > {{ merge_data_value['merge_id'] }}</a>
                                            <br>
                                            {% else %}
                                                {{ merge_data_value['merge_id'] }}
                                        {% endif %}
                                </td>
                                <td>
                                    <a href="#" data-toggle="tooltip" title="{{ merge_data_value.merge_repo }}">Repo List</a>
                                        <div class="tooltip bs-tooltip-top" role="tooltip">
                                            {{ merge_data_value.merge_repo }}
                                        </div>

                                </td>
                                <td>{{ merge_data_value.start_time }}</td>
                                <td><a href=https://directory.cisco.com/dir/reports/{{ merge_data_value.triggered_by }}>{{ merge_data_value.triggered_by }}</a> <br>
                                    {{ merge_data_value.source_branch  }} <i class="fas fa-long-arrow-alt-right"></i> {{ merge_data_value.destination_branch }}
                                </td>
                                {% if merge_data_value.merge_status == 'MERGED' %}
                                    <td><font color = "green">{{merge_data_value['merge_status']}}</font></td>
                                {% else %}
                                    <td>{{ merge_data_value.merge_status }}</td>
                                {% endif %}
                                <td>
                                    {% if merge_data_value['jenkins_build_url'] != None %}
                                        <a href="{{ merge_data_value.jenkins_build_url }}">Jenkins Build # {{ merge_data_value.jenkins_build_url.split("/")[-2] }}</a><br>
                                        {{ merge_data_value.build_status }}
                                    {% endif %}
                                </td>
                                <td>
                                  {% if merge_data_value.merge_status != 'JOB_UNDERWAY'%}
                                    {% if merge_data_value.dry_run == '1' %}
                                      {% if merge_data_value['merge_report'] != None %}
                                              <a href="{{ merge_data_value['merge_report'] }}"> DryRun Report </a>
                                      {% endif %}
                                    {% elif merge_data_value.dry_run == '0' %}
                                      {% if merge_data_value['merge_report'] != None %}
                                              <a href="{{ merge_data_value['merge_report'] }}"> Merge Report </a>
                                      {% endif %}
                                    {% endif %}
                                  {% endif %}
                                </td>
                                {% if merge_data_value['approval_status'] == 'Pending' %}
                                    <td><font color = "orange">{{merge_data_value['approval_status']}}</font></td>
                                {% elif merge_data_value['approval_status'] == 'Approved' %}
                                    <td><font color = "green">{{merge_data_value['approval_status']}}</font></td>
                                {% else %}
                                    <td>{{merge_data_value['approval_status']}}</td>
                                {% endif %}
                                <td>{{ merge_data_value.approver }}</td>
                                <td>
                                    {% if merge_data_value.merge_status in ['PROGRESS', 'IN_PROGRESS', 'IN-PROGRESS', 'ABORTED', 'PARTIALLY_CLOSED','NA'] and merge_data_value.dry_run == '0' %}
                                        <button type="button" class="btn btn-outline-danger" data-toggle="modal" data-target="#close{{ loop.index }}" title="Cancel Merge"><i class="fas fa-ban"></i></button>
                                        <div class="modal fade" id="close{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                            <div class="modal-dialog modal-dialog-centered" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h6 class="modal-title" id="close">Are you sure you want to cancel the merge. {{ merge_data_value['merge_id']}}</h6>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <form method="POST">
                                                            {{form.csrf_token()}}
                                                            <div class="modal-body">
                                                                <label> It will close all the PRs which were created as a part of merge </label>
                                                            </div>
                                                        <div class="modal-footer">
                                                            {{ form.merge_id(value=merge_data_value['merge_id']) }}
                                                            {{ form.cancel_merge(class="btn btn-danger ")}}
                                                        </div>
                                                        </form>
                                                    </div>
                                                </div>
                                                </div>
                                            </div>

                                        {%if merge_data_value.approval_status not in ('Pending') %}
                                            <button type="button" class="btn btn-outline-warning" data-toggle="modal" data-target="#refresh{{ loop.index }}" title="Refresh Merge"><i class="fas fa-registered"></i></button>
                                                <div class="modal fade" id="refresh{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                    <div class="modal-dialog modal-dialog-centered" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h6 class="modal-title" id="refresh">Are you sure you want to refresh the merge status. {{ merge_data_value['merge_id']}}</h6>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <form method="POST">
                                                                    {{form.csrf_token()}}
                                                                    <div class="modal-body">
                                                                    <div>
                                                                        <label> Refresh the merge status and all the PRs which were created as a part of merge</label>
                                                                    </div>
                                                                    </div>
                                                                <div class="modal-footer">
                                                                    {{ form.merge_id(value=merge_data_value['merge_id']) }}
                                                                    {{ form.refresh_merge(class="btn btn-warning ")}}
                                                                </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                        </div>
                                                    </div>
                                        {% endif%}
                                        {% if user_role == "RELENG" or user_role == "ORG_OWNER" or user_role == "DEV_MANAGER"   %}
                                            {% if merge_data_value.approval_status == 'Pending' %}
                                                <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#edit{{ loop.index }}" title="Approve"><i class="fas fa-edit"></i></button>
                                                    <div class="modal fade" id="edit{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h6 class="modal-title" id="submit">Are you sure you want to Approve/Deny sync request for MERGE-ID : {{ merge_data_value['merge_id'] }}</h6>
                                                                    <button type="button" class="approve" data-dismiss="modal" aria-label="Approve">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <form method="POST">
                                                                        {{form.csrf_token()}}
                                                                        <div class="modal-body">
                                                                            <div>
                                                                                <label>Approve/Deny the merge sync request</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            {{form.merge_id(value=merge_data_value['merge_id']) }}
                                                                            {{form.approve(class="btn btn-success")}}
                                                                            {{form.deny(class="btn btn-danger")}}
                                                                        </div>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                            {% endif %}
                                        {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                        {%endif%}        
                    {% endfor %}
                    </tbody>
                </table>
            </div>
            <form method="POST">
                    <div>
                        {{ form.refresh_merge_home(class="btn btn-warning ")}}
                    </div>
            </form>
        </div>
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">No branches have been merged via dashboard for {{product_org}}</h3>
        {% endif %}
</div>

{% endblock %}
