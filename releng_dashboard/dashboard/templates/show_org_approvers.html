{% extends 'product_base.html' %}
{% block home %}

<script language="javascript">
$(document).ready(function() {
$('#approvers').DataTable({
        "order": [[ 0, "asc" ]],
        "pageLength": 20
    });
} );
</script>
<form method="post">
    {{form.hidden_tag()}}
    <div class="container">
        <br>
        <p class="h3"> Dashboard Access Management for Organization :  {{product_org}} </p>
        <br>
        <table id="approvers" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
            <thead class="table-primary">
            <tr style="text-align:center; background-color:#000000;color:white">
                <th scope="col" style="min-width:200px">CEC ID</th>
                <th scope="col" style="min-width:250px">USER NAME</th>
                <th scope="col" style="min-width:300px">REPOSITORIES</th>
                <th scope="col">ROLE</th>
                {% if user_role == "RELENG" or user_role == "ORG_OWNER"%}
                <th scope="col">Select</th>
                {% endif %}
            </tr>
            </thead>
            <tbody>
            {% for approver in approver_list %}
            <tr>
                <td><a href=https://directory.cisco.com/dir/reports/{{approver['CEC_ID']}}>{{ approver['CEC_ID'] }}</a></td>
                <td>{{ approver['CEC_FULLNAME'] }}</td>
                <td>{{ approver['REPO_NAME'] }}</td>
                <td>{{ approver['ROLE'] }}</td>
                {% if user_role == "RELENG" %}
                    <td><input type="checkbox" id=user{{ loop.index }} name="{{product_org}}_Select" value="{{approver['CEC_ID']}}__sep__{{approver['ROLE']}}__sep__{{product_org}}"></td>
                {% elif user_role == "ORG_OWNER" %}
                    {% if approver['ROLE'] != "RELENG" and approver['ROLE'] != "ORG_OWNER" %}
                        <td><input type="checkbox" id=user{{ loop.index }} name="{{product_org}}_Select" value="{{approver['CEC_ID']}}__sep__{{approver['ROLE']}}__sep__{{product_org}}"></td>
                    {% endif %}
                {% else %}
                {% endif %}
            </tr>
            {% endfor %}
            </tbody>

        </table>
        <br>
        {% if user_role == "RELENG" or user_role == "ORG_OWNER" %}
        <a href="/add_user/{{product}}" class="btn btn-info">Add User </a>
        {{ form.submit(class="btn btn-danger",hidden='true', id='form-submit') }}
        <button type="button" class="btn btn-danger" data-toggle="modal" onclick="check()">Remove User</button>
        {% endif %}
    </div>
    <small id="HelpBlock" class="form-text text-muted">
        <br>
        ROLE BASED ACCESS CLASSIFICATION: <br>
        ----------------------------------------------------------------------------------------------------------------------------------<br>
        | <strong style="background-color:LightGray;">TECH_LEAD</strong>&emsp;&ensp;&nbsp;|: Create Branch || Trigger QA build || PR Bypass&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&ensp;&nbsp;&emsp;|<br>
        ----------------------------------------------------------------------------------------------------------------------------------<br>
        | <strong style="background-color:LightGray;">DEV_MANAGER</strong> |: Create Branch || Trigger QA build || PR Bypass || Throttle Approval || Sync Branch || USER_LOC &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;|<br>
        ----------------------------------------------------------------------------------------------------------------------------------<br>
        | <strong style="background-color:LightGray;">ORG_OWNER</strong>&emsp;&nbsp; |: Create Branch || Trigger QA build || PR Bypass || Throttle Approval || Sync Branch || USER_LOC || GitHub Access      |<br>
        ----------------------------------------------------------------------------------------------------------------------------------<br>
    </small>
</form>


<div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalTitle"> </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <b>Are you sure ?</b>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="delete_button" data-dismiss="modal">Cancel</button>
                <!--{{form.submit(class="btn btn-danger")}}-->
                <button type="button" class="btn btn-danger" id="modal-confirm">Proceed</button>
            </div>
        </div>
    </div>
</div>

<script>


function check(){
    str ="user";
    var name = "Delete User(s) ";
    num = 1;
    j = 0;
    var curr = `${str}${num}`;
    var var1= document.getElementById(curr);
    do {
    if (var1.checked == true){
         j=j+1;
         text=var1.value;
         const myArray = text.split("__sep__");
         name = name + myArray[0] + ",";
                }
    num = num+1;
    curr = `${str}${num}`;
    var1= document.getElementById(curr);
        } while (var1 != null );
    name = name.substring(0, name.length-1)+ " permanently ?";
    if (j >= 1) {
        $("#editModalTitle").html(name);
        $('#editModal').modal('show');
        }
    if (j == 0){
      $('#form-submit').click();
        }
    }

$('#modal-confirm').click(function(){
    // Perform the action after modal confirm button is clicked.
    document.getElementById("delete_button").disabled = true;
    document.getElementById("modal-confirm").disabled = true;
    $('#form-submit').click(); // submitting the form
});
</script>
{% endblock %}
