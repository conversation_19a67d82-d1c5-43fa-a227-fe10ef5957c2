{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
$(document).ready(function() {
$('#TT_CDETS_DETAILS').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
    <br>
    <div class="row">
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Submit Throttle Request</h5>
                    <p class="card-text">Submit throttle request</p>
                    <a href="/create_throttle_request/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Approve Throttle Request</h5>
                    <p class="card-text">Approve throttle request</p>
                    <a href="/approve_throttle_request/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Show Approvers</h5>
                    <p class="card-text">List of users who can approve</p>
                    <a href="/show_approver/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
    </div>
    {% if cdet_list %}
    <div class="fw-body" >
        <div class="content">
            <br>
            <br>
            {% if search %}
                <h3 style="text-align:left">All throttle requests for : {{cdets_id}}  </h3>
            {% else %}
                <h3 style="text-align:left">Latest throttle requests for : {{product_org}}  </h3>
            {% endif %}
            <br>
            <table id ="TT_CDETS_DETAILS" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                <thead class="table-info">
                <tr style="text-align:center; background-color:#000000;color:white">
                    <th scope="col" style="width: 5%">ID</th>
                    <th scope="col" style="width: 10%">Submited On</th>
                    <th scope="col" style="width: 15%">Branch<br>CDETS</th>
                    <th scope="col" style="width: 20%">Submitter's Justification </th>
                    <th scope="col" style="width: 20%">Approver's Comments </th>
                    <th scope="col" style="width: 20%">Status</th>
                    <th scope="col" style="width: 15%">Actions</th>
                </tr>
                </thead>
                <tbody>
                {% for cdet in cdet_list %}
                    <tr style="text-align:center">
                        <th scope="row">{{cdet['id']}}</th>
                        {% if cdet['submission_date'] %}
                            <td>{{cdet['submission_date']}}</td>
                        {% else %}
                            <td> </td>
                        {% endif %}
                        <td>{{cdet['branches_branch_name']}}<br>
                          {% for cdets_id in cdet['cdets_id'].split(",") %}
                            <a href=https://cdetsng.cisco.com/webui/#view={{ cdets_id }}>{{ cdets_id }}</a>
                            <br>
                          {% endfor %}
                        </td>

                        <td><a href=https://directory.cisco.com/dir/reports/{{cdet['submitter_cec_id']}}>{{cdet['submitter_cec_id']}}</a><br>
                        {{cdet['cdets_comment']}}</td>
                        {% if cdet['approval_1_cec_id'] %}
                            <td><a href=https://directory.cisco.com/dir/reports/{{cdet['approval_1_cec_id']}}>{{cdet['approval_1_cec_id']}}</a>
                              <br>{{cdet['error_msg']}}</td>
                        {% else %}
                            <td> </td>
                        {% endif %}
                        {% if cdet['approval_status'] == 'Pending' %}
                            <td><font color = "orange">{{cdet['approval_status']}}</font></td>
                        {% elif cdet['approval_status'] == 'Approved' %}
                            <td><font color = "green">{{cdet['approval_status']}}</font>
                            {% if cdet['expiring_in'] %}
                              <br><font color = "orange">Expiring in {{cdet['expiring_in']}}</font>
                            {% endif %}
                            </td>
                        {% elif cdet['approval_status'] == 'Denied' %}
                            <td><font color = "red">{{cdet['approval_status']}}</font></td>
                        {% elif cdet['approval_status'] == 'Expired' %}
                            <td><font color = "red">{{cdet['approval_status']}}</font></td>
                        {% else %}
                            <td>{{cdet['approval_status']}}</td>
                        {% endif %}
                        <td>
                            {% if cdet['approval_status'] == 'Validating' %}
                            <button type="button" class="btn btn-outline-success" data-toggle="modal" data-target="#revalidate{{ loop.index }}" title="revalidate"><i class="fas fa-check-double"></i></button>
                            <div class="modal fade" id="revalidate{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h6 class="modal-title" id="revalidate">Are you sure you want to revalidate {{cdet['cdets_id']}} : {{ cdet['branches_branch_name'] }} ? </h6>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <label> Validation can take couple of mins</label>
                                            <form method="POST">
                                                {{form.csrf_token()}}
                                                <div class="modal-footer">
                                                    {{form.revalidate(class="btn btn-success")}}
                                                    {{form.id(value=cdet['id'] ) }}
                                                    {{form.cdet(value=cdet['cdets_id']) }}
                                                    {{form.branch_name(value=cdet['branches_branch_name'])}}
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {% elif ( user_role == "ORG_OWNER" or user_role == "DEV_MANAGER" or user_role == "RELENG" )and cdet['approval_status'] != 'Expired' %}
                                <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#edit{{ loop.index }}" title="Edit"><i class="fas fa-edit"></i></button>
                                <div class="modal fade" id="edit{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                               <h6 class="modal-title" id="submit">Are you sure you want to edit {{cdet['cdets_id']}} : {{ cdet['branches_branch_name'] }} ? </h6>
                                               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                   <span aria-hidden="true">&times;</span>
                                               </button>
                                            </div>
                                            <div class="modal-body">
                                              <form method="POST">
                                                {{form.csrf_token()}}
                                                <div class="modal-body">
                                                  <label> Comments </label>
                                                  <div>
                                                    {{form.cdets_comment(required='required') }}
                                                  </div>
                                                </div>
                                                <div class="modal-footer">
                                                  {{form.approve(class="btn btn-success")}}
                                                  {{form.deny(class="btn btn-danger")}}
                                                  {{form.cancel(class="btn btn-warning")}}
                                                  {{form.id(value=cdet['id'] ) }}
                                                  {{form.cdet(value=cdet['cdets_id']) }}
                                                  {{form.branch_name(value=cdet['branches_branch_name'])}}
                                                  {{form.branch_name(value=cdet['error_msg'])}}
                                                </div>
                                              </form>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                       {% elif cdet['approval_status'] == 'Expired' %}
                          <button type="button" class="btn btn-outline-success" data-toggle="modal" data-target="#renew{{ loop.index }}" title="Renew"><i class="fas fa-registered"></i></button>
                            <div class="modal fade" id="renew{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h6 class="modal-title" id="renew">Are you sure you want to renew {{cdet['cdets_id']}} : {{ cdet['branches_branch_name'] }} ? </h6>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <form method="POST">
                                                {{form.csrf_token()}}
                                                <div class="modal-body">
                                                  <label> Comments </label>
                                                  <div>
                                                     {{form.cdets_comment(required='required') }}
                                                   </div>
                                                 </div>
                                               <div class="modal-footer">
                                                   {{form.renew(class="btn btn-success")}}
                                                   {{form.id(value=cdet['id'] ) }}
                                                   {{form.cdet(value=cdet['cdets_id']) }}
                                                   {{form.branch_name(value=cdet['branches_branch_name'])}}
                                               </div>
                                             </form>
                                        </div>
                                      </div>
                                    </div>
                                </div>
                       {% endif %}
                       </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
            <br><br>
            <form method="POST">
            {{form.hidden_tag()}}
            {% if search %}
                {{form.submit_latest(class="btn btn-info", style = "width:30%")}}
            {% else %}
                <h5 style="text-align:left">Search all throttle requests for specific CDET: </h3>
                {{form.input}}
                {{form.search(class="btn btn-info")}}
            {% endif %}
            <br><br>
            </form>
            </div>
        </div>
    {% else %}
        <br>
        <br>
        <h3 style="text-align:left">No throttle requests to show : {{product_org}}</h3>
    {% endif %}
    </div>
{% endblock %}
