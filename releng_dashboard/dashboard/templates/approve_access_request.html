{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
$(document).ready(function() {
$('#access_list').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
    <br>
    <div class="row">
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Access Control Home</h5>
                    <p class="card-text">Access Control Home Page </p>
                    <a href="/access_control_home/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Submit Request</h5>
                    <p class="card-text">Submit Access Request</p>
                    <a href="/create_access_request/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>

        <div class="col-sm-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Show Approvers </h5>
                    <p class="card-text">List of Approvers </p>
                    <a href="/show_access_approvers/{{product}}" class="btn btn-info">Click Here >></a>
                </div>
            </div>
        </div>
    </div>
    <br>
    <form method="POST">
        {% if user_list %}
            <div class="fw-body" >
                <div class="content">
                    <br>
                    <br>
                    <h3 style="text-align:left">Pending Requests for : {{product_org}}</h3>
                    <br>
                    <table id ="access_list" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                        <thead class="table-info">
                            <tr style="text-align:center; background-color:#000000;color:white">
                                <th scope="col" style="width: 5%">ID</th>
                                <th scope="col" style="width: 10%">CEC-USER</th>
                                <th scope="col" style="width: 10%">Submit Date </th>
                                <th scope="col" style="width: 10%">Submitter </th>
                                <th scope="col" style="width: 10%">Repo </th>
                                <th scope="col" style="width: 5%"> Type </th>
                                <th scope="col" style="width: 5%"> Category </th>
                                <th scope="col" style="width: 20%">Justification </th>
                                <th scope="col" style="width: 10%">Pending on </th>
                                <th scope="col" style="width: 5%">Status</th>
                                {% if user_role == "ORG_OWNER" or user_role == "RELENG" %}
                                    <th scope="col" style="width: 10%">Actions</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in user_list %}
                                <tr style="text-align:center">
                                    <th scope="row">{{request['id']}}</th>
                                    <td><a href=https://directory.cisco.com/dir/reports/{{request['USERID']}}>{{request['USERID']}}</a></td>
                                    <td>{{request['SUBMIT_DATE']}}</td>
                                    <td><a href=https://directory.cisco.com/dir/reports/{{request['SUBMITTER']}}>{{request['SUBMITTER']}}</a></td>
                                    <td>{{request['REPO']}}</td>
                                    <td>{{request['TYPE_ID']}}</td>
                                    <td>{{request['CATEGORY_ID']}}</td>
                                    <td>{{request['JUSTIFICATION']}}</td>
                                    <td><a href=https://directory.cisco.com/dir/reports/{{request['APPROVER']}}>{{request['APPROVER']}}</a></td>

                                    {% if request['STATUS'] == 'Pending' %}
                                        <td><font color = "orange">{{request['STATUS']}}</font></td>
                                    {% elif request['STATUS'] == 'Approved' %}
                                        <td><font color = "green">{{request['STATUS']}}</font></td>
                                    {% elif request['STATUS'] == 'Denied' %}
                                        <td><font color = "red">{{request['STATUS']}}</font></td>
                                    {% else %}
                                        <td>{{request['STATUS']}}</td>
                                    {% endif %}
                                    {% if user_role == "ORG_OWNER" or user_role == "RELENG" %}
                                    <td>
                                            <button type="button" class="btn btn-outline-success" data-toggle="modal" data-target="#approve{{ loop.index }}" title="approve"><i class="fas fa-check"></i></button>
                                            <div class="modal fade" id="approve{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h6 class="modal-title" id="approve">Are you sure you want to approve request number : {{request['id']}} for CEC-User : {{ request['USERID'] }} ? </h6>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form method="POST">
                                                                {{form.csrf_token()}}

                                                                <div class="modal-body">
                                                                    <label> Comments </label>
                                                                    <div>
                                                                        {{form.approver_comment(required='required') }}
                                                                        {{form.id(value=request['id'] ) }}
                                                                        {{form.userid(value=request['USERID']) }}
                                                                        {{form.repo(value=request['REPO']) }}
                                                                        {{form.type_id(value=request['TYPE_ID']) }}
                                                                        {{form.category_id(value=request['CATEGORY_ID']) }}
                                                                    </div>
                                                                </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            {{form.approve(class="btn btn-success")}}
                                                        </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-danger" data-toggle="modal" data-target="#deny{{ loop.index }}" title="deny"><i class="fas fa-times"></i></button>
                                            <div class="modal fade" id="deny{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h6 class="modal-title" id="deny">Are you sure you want to deny request number : {{request['id']}} for CEC-User : {{ request['USERID'] }} ? </h6>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form method="POST">
                                                                {{form.csrf_token()}}

                                                                <div class="modal-body">
                                                                    <label> Comments </label>
                                                                    <div>
                                                                        {{form.approver_comment(required='required') }}
                                                                        {{form.id(value=request['id'] ) }}
                                                                        {{form.repo(value=request['REPO']) }}
                                                                        {{form.userid(value=request['USERID']) }}
                                                                        {{form.type_id(value=request['TYPE_ID']) }}
                                                                        {{form.category_id(value=request['CATEGORY_ID']) }}

                                                                    </div>
                                                                </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            {{form.deny(class="btn btn-danger")}}
                                                        </div>
                                                    </div>
                                                   </form>
                                                </div>
                                            </div>
                                        </td>
                                        {% endif %}
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">No access requests to show : {{product_org}}</h3>
        {% endif %}
    </form>
</div>
{% endblock %}
