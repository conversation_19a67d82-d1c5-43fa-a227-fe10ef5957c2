{% extends 'product_base.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Generate Feature Code Coverage Report for feature which is merged</h1>
                <p class="lead"> This will generate Feature Level Code Coverage Report for the given list of Jira Feature IDs.</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="release_version" class="col-sm-2 col-form-label">MAIN BRANCH VERSION</label>
            <div class="col-sm-10">
                {{form.main_version}}
                <small class="form-text text-muted">select the main branch version in which the feature was merged</small>
            </div>
        </div>
        <div class="form-group row">
            <label for="feature_list" class="col-sm-2 col-form-label">JIRA FEATURE ID LIST</label>
            <div class="col-sm-10">
                {{form.feature_list}}
                <small class="form-text text-muted">mention jira feature id seperated by (,). Ex: FEAT-6101</small>
            </div>
        </div>
        <div class="form-group row">
            <label for="mail_to" class="col-sm-2 col-form-label">{{form.mail_to.label}}</label>
            <div class="col-sm-10">
                {{form.mail_to}}
                <small class="form-text text-muted">mention list of email id seperated by (,). By default you will get an email</small>
            </div>
        </div>
        <div class="form-group row" >
            <div class="col-sm-10">
             <button type="button" class="btn btn-info" style = "width:35%,"), data-toggle="modal" data-target="#proceed">Proceed with Feature Code Coverage</button>
                <div class="modal fade" id="proceed" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <p class="modal-title" id="proceed"> New branch will be created with feature specific code only.
                                    Hence during UT execution there "might" be some dependency errors.</br></br>
                                    <span style="color: blue;">Dev teams would have to fix these issues to get right code coverage numbers.</span></br>
                                    Information will be shared via email. </p>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-footer">
                                {{form.submit(class="btn btn-info", style = "width:55%")}}
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

{% endblock %}
