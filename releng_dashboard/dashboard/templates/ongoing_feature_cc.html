{% extends 'product_base.html' %}
{% block home %}
<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Generate Feature Code Coverage Report for ongoing feature </h1>
                <p class="lead"> This will generate Feature Level Code Coverage Report for the feature branch which is not merged to main branch.</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="branch_name" class="col-sm-2 col-form-label">DEV BRANCH NAME<font color="red"> *</font></label>
            <div class="col-sm-10">
                {{form.branch_name}}
                <small class="form-text text-muted">mention the dev branch name used for feature Ex: dev-<></small>
            </div>
        </div>
        <div class="form-group row">
            <label for="jira_id" class="col-sm-2 col-form-label">JIRA_ID<font color="red"> *</font></label>
            <div class="col-sm-10">
                {{form.jira_id}}
                <small class="form-text text-muted">mention jira feature id. Ex: FEAT-6101/PMOB-16611</small>
            </div>
        </div>
        <div class="form-group row">
            <label for="base_branch_name" class="col-sm-2 col-form-label">BASE BRANCH NAME<font color="red"> *</font></label>
            <div class="col-sm-10">
                {{form.base_branch_name}}
                <small class="form-text text-muted">mention the branch which should be taken as base. Ex: main </small>
            </div>
        </div>
        <div class="form-group row">
            <label for="mail_to" class="col-sm-2 col-form-label">{{form.mail_to.label}}</label>
            <div class="col-sm-10">
                {{form.mail_to}}
                <small class="form-text text-muted">mention list of email id seperated by (,). By default you will get an email</small>
            </div>
        </div>
        <div class="form-group row" >
            <div class="col-sm-10">
             <button type="button" class="btn btn-info" style = "width:35%,"), data-toggle="modal" data-target="#proceed">Proceed with Feature Code Coverage</button>
                <div class="modal fade" id="proceed" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h6 class="modal-title" id="proceed"> Please make sure feature branch is in sync with base branch to get accurate code coverage numbers </h6>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-footer">
                                {{form.submit(class="btn btn-info", style = "width:55%")}}
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

{% endblock %}
