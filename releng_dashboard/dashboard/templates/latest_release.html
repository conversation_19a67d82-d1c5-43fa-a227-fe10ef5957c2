{% extends 'base.html' %}
{% block content %}

<div class="container">
    <br>
    <p class="h3"> Latest Cloud Native Releases </p>
    <br>
    <table id="release_builds" class="table table-striped table-bordered">
        <thead class="table-primary">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col">Product</th>
            <th scope="col">Promoted On</th>
            <th scope="col">Release Number</th>
            <th scope="col">QA Build Number</th>
            <th scope="col">Branch</th>
            <th scope="col">Release Type</th>
            <th scope="col">Customer</th>
            <th scope="col">Images</th>
        </tr>
        </thead>
        <tbody>
        {% for latest_product_release in latest_product_releases %}
        <tr>
            <th scope="row"><a href="/released_builds/{{latest_product_release['builds_products_product_name']}}" >{{latest_product_release['builds_products_product_name']}}</a></th>
            <td>{{latest_product_release['promoted_on']}}</td>
            <td>{{latest_product_release['release_number']}}</td>
            <td>{{latest_product_release['builds_build_number']}}</td>
            <td>{{latest_product_release['branch_name']}}</td>
            <td>{{latest_product_release['release_type']}}</td>
            <td>{{latest_product_release['customer']}}</td>
            {% if latest_product_release['release_type'] == "FCS" %}
                <td>CCO Posted</td>
            {% else %}
                <td>
                    {% if latest_product_release["offline_url"] == None %}
                    <font color = "red"> N/A </font>
                    {% else %}
                    <a href = {{ latest_product_release["dh_offline_url"] }}/ target="_blank"> {{ latest_product_release["builds_products_product_name"] }}-{{ latest_product_release["release_number"] }}-offline </a>
                    {% endif %}
                <br>
                    {% if latest_product_release["online_url"] == None %}
                    <font color = "red"> N/A </font>
                    {% else %}
                    <a href = {{ latest_product_release["dh_online_url"] }}/ target="_blank"> {{ latest_product_release["builds_products_product_name"] }}-{{ latest_product_release["release_number"] }} </a>
                    {% endif %}
                </td>
            {% endif %}
        </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
