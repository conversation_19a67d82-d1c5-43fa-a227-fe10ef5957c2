{% extends 'product_base.html' %}
{% block home %}

<script>
    function trigger_corona(trigger_corona_scan) {
        var predecessor_id = document.getElementById("predecessor_id");
        var users_to_notify = document.getElementById("users_to_notify");
        
        var predecessor_id_radio = document.getElementById("predecessor_id_option1");
        var predecessor_id_input_radio = document.getElementById("predecessor_id_input_option2");
        var predecessor_id_content = document.getElementById("predecessor_id");
        var predecessor_id_input_content = document.getElementById("predecessor_id_input");
        var corona_links_content = document.getElementById("corona_links")

        if (trigger_corona_scan.checked) {
            predecessor_id.style.display = "block";
            users_to_notify.style.display = "block";
            predecessor_id_radio.style.display = "block";
            predecessor_id_input_radio.style.display = "block";
            corona_links_content.style.display = "block";

        } else {
            predecessor_id.style.display = "none";
            users_to_notify.style.display = "none";
            predecessor_id_radio.style.display = "none";
            predecessor_id_input_radio.style.display = "none";
            corona_links_content.style.display = "none";
        }
        
    
        predecessor_id_radio.addEventListener("change", function() {
        predecessor_id_content.style.display = "block";
        predecessor_id_input_content.style.display = "none";
        corona_links_content.style.display = "block";
        });

        predecessor_id_input_radio.addEventListener("change", function() {
        predecessor_id_content.style.display = "none";
        predecessor_id_input_content.style.display = "block";
        corona_links_content.style.display = "block";
        });
    }
</script>

<div class="col-xs-6 col-sm-10">
    <form method="POST">
        {{form.hidden_tag()}}
        <div class="jumbotron jumbotron-fluid">
            <div class="container">
                <h1 class="display-4">Trigger QA Build</h1>
                <p class="lead"> This will generate both online & offline builds</p>
            </div>
        </div>
        <div class="form-group row">
            <label for="product_name" class="col-sm-2 col-form-label">PRODUCT_NAME</label>
            <div class="col-sm-10">
                {{ product }}
            </div>
        </div>
        {% if product == 'upf' %}
        <div class="form-group row">
            <label for="source_branch" class="col-sm-2 col-form-label" style="text-align:left;"> STAROS_FULL_VERSION <font color="red"> *</font></label>
            <div class="col-sm-10"> {{ form.staros_full_version(size=30) }} </div>
        </div>
        {% endif %}
        {% if product in ['up-recovery','smi-nso'] %}
        <div class="form-group row">
            <label for="source_branch" class="col-sm-2 col-form-label" style="text-align:left;"> NSO_VERSION <font color="red"> *</font></label>
            <div class="col-sm-10"> {{ form.nso_version(size=30) }} </div>
        </div>
        {% endif %}
        {% if product not in pre_build_products %}
            <div class="form-group row">
                <label for="branch_name" class="col-sm-2 col-form-label">{{form.branch_name.label}}</label>
                <div class="col-sm-10">
                    {{form.branch_name(id="branchname")}}
                </div>
            </div>
            {% if product not in ['up-recovery','smi-nso', 'upf'] %}
                <div class="form-group row">
                    <label for="trigger_corona_scan" class="col-sm-2 col-form-label">Trigger Corona Scan</label>
                    <div class="col-sm-10">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input class="form-check-input" type="checkbox" name="trigger_corona_scan"
                            id="trigger_corona_scan" onchange="trigger_corona(this)" default='false' {% if not trigger_corona_scan
                            %}default="false" {% endif %}>
                    </div>
                </div>
            {% endif %}
            <div class="container">
                <div class="form-group row">
                    <div id="predecessor_id_option1" style="display:none">
                        <input type="radio" name="predecessor_type" value="predecessor_id_option1" id="predecessor_id_option1" checked {% if selected_option == 'predecessor_id_option1' %}selected{% endif %}>
                        <label for="predecessor_id_option1">Select Predecessor IDs from previous releases</label>
                    </div>
                </div>

                <div class="form-group row">
                    <div id="predecessor_id_input_option2" style="display:none">
                        <input type="radio" name="predecessor_type" value="predecessor_id_input_option2" id="predecessor_id_input_option2" {% if selected_option == 'predecessor_id_input_option2' %}selected{% endif %}>
                        <label for="predecessor_id_input_option2">Enter Predecessor ID</label>
                    </div>
                </div>

                <div class="form-group row" id="predecessor_id" style="display:none">
                    {% if predecessor_id_choices and predecessor_id_choices != 'none' %}
                        <label for="predecessor_id" class="col-sm-2 col-form-label">Predecessor ID</label>
                    <div class="col-sm-10">
                        <select id="predecessor_id" name="predecessor_id">
                            {% for pre_id  in predecessor_id_choices %}
                                <option value="{{ pre_id }}">{{ pre_id }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">
                        	Ex: QA_Build Number,corona_id,release_type,release_version
                        </small>
                    </div>

                    {% endif %}
                </div>

                <div class="form-group row" id="predecessor_id_input" style="display:none">
                        <label for="predecessor_id_input" class="col-sm-2 col-form-label" style="text-align:left;">Predecessor ID</label>
                        {{form.predecessor_id_input(id="predecessor_id_input")}}
                </div>

                <div class="form-group row" id="corona_links" style="display:none">
                    <div class="col-sm-10">
                    <small class="form-text text-muted">
                        Predecessor ID must be from
                        {% if product == 'amf' %}Access and Mobility Management Function<a href=https://corona.cisco.com/products/8002> AMF Corona Link </a><br>{% endif %}
                    {% if product == 'bng' %}Broadband Network Gateway Software<a href=https://corona.cisco.com/products/6808> BNG Corona Link </a><br>{% endif %}
                    {% if product == 'pcf' %}Policy Control Function Software<a href=https://corona.cisco.com/products/4860> PCF Corona Link </a><br>{% endif %}
		    {% if product == 'cpc' %}Converged Policy and Charging<a href=https://corona.cisco.com/products/15440> CPC Corona Link </a><br>{% endif %}
                    {% if product in ['smf','ccg','sgw','kpm'] %}Session Management Function Software<a href=https://corona.cisco.com/products/4861> SMF/CCG/SGW/KPM Corona Link </a><br>{% endif %}
                    {% if product in ['upf','rcm','cnvpc'] %}User Plane Function Software<a href=https://corona.cisco.com/products/4862> UPF/RCM/CNVPC Corona Link </a><br> {% endif %}
                    </small>
                    </div>
                </div>
        
                
                <div class="form-group row" id="users_to_notify" style="display:none">
                    <label for="users_to_notify"  class="col-sm-2 col-form-label">Users to Notify:</label>
                    <div class="col-sm-10">  {{form.users_to_notify(id="users_to_notify")}} <br>
                    <small  class="form-text text-muted"> (Comma separated Cisco mails ID's of users to be Notified about this corona scan) </small>
                    </div>
                </div>
            </div>

        {% endif %}
        {% if product == "cee" %}
            <div class="form-group row">
                <label for="cee_build_no" class="col-sm-2 col-form-label">{{form.build_no.label}}</label>
                <div class="col-sm-10">
                    {{form.build_no}}
                    <small class="form-text text-muted">Latest CEE builds are available at: <a href="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/sprint_releases/smi-apps/smi-cee-products/">smi-cee-products</a></small>
                </div>
            </div>
        {% endif %}
        {% if product == "deployer" %}
            <div class="form-group row">
                <label for="deployer_build_no" class="col-sm-2 col-form-label">{{form.build_no.label}}</label>
                <div class="col-sm-10">
                    {{form.build_no}}
                    <small class="form-text text-muted">Latest Deployer Builds are available at: <a href="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/smi-apps/smi-cluster-deployer-products/">smi-deployer-vm</a>
                        	</small>
                </div>

            </div>
        {% endif %}
        {% if product == "base-vm" %}
            <div class="form-group row">
                <label for="basevm_build_no" class="col-sm-2 col-form-label">{{form.build_no.label}}</label>
                <div class="col-sm-10">
                    {{form.build_no}}
                    <small class="form-text text-muted">Latest Base-vm Builds are available at: <a href="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/sprint_releases/smi-base-vm/">smi-base-vm</a></small>
                </div>
            </div>
        {% endif %}
        {% if product == "cee-bm" %}
            <div class="form-group row">
                <label for="cee_bm_build_no" class="col-sm-2 col-form-label">{{form.build_no.label}}</label>
                <div class="col-sm-10">
                    {{form.build_no}}
                    <small class="form-text text-muted">Latest CEE-BM builds are available at: <a href="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-apps/smi-cee-products/">smi-cee-bm-products</a></small>
                </div>
            </div>
        {% endif %}
        {% if product == "deployer-bm" %}
            <div class="form-group row">
                <label for="deployer_bm_build_no" class="col-sm-2 col-form-label">{{form.build_no.label}}</label>
                <div class="col-sm-10">
                    {{form.build_no}}
                    <small class="form-text text-muted">Latest Deployer-BM Builds are available at: <a href="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-apps/smi-cluster-deployer-products">smi-deployer-bm-products</a>
                    </small>
                </div>
            </div>
        {% endif %}
        {% if product == "base-bm" %}
            <div class="form-group row">
                <label for="base_bm_build_no" class="col-sm-2 col-form-label">{{form.build_no.label}}</label>
                <div class="col-sm-10">
                    {{form.build_no}}
                    <small class="form-text text-muted">Latest Base-BM Builds are available at: <a href="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-base-image-iso/">smi-base-bm</a></small>
                </div>
            </div>
        {% endif %}
 
        <div class="form-group row" >
            <div class="col-sm-10">
                {{form.submit(class="btn btn-info", style = "width:10%")}}
            </div>
            <!--{{ form.submit(hidden='true', id='form-submit') }}
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#confirmationModal">
                Start
            </button-->
        </div>
        <!--script>
            <var x = document.getElementById("branchname").value;
            document.getElementById("weeklybuildno").innerHTML = x;
        </script-->
        <!-- Modal
        <div class="modal fade" id="confirmationModal" tabindex="-1" role="dialog" aria-labelledby="confirmationModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        Are you sure?
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">No</button>
                        <button type="button" class="btn btn-success success" id="modal-confirm">Yes</button>
                    </div>
                </div>
            </div>
        </div-->
    </form>
</div>

<!--script>
$('#modal-confirm').click(function(){
    // Perform the action after modal confirm button is clicked.
    $('#form-submit').click(); // submitting the form
});
</script-->

{% endblock %}
