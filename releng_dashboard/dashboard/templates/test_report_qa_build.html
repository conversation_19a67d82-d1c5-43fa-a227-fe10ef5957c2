{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script language="javascript">
$(document).ready(
function() {
$('#functional_suite').DataTable({
        "order": [[ 0, "desc" ]]
    });
});

</script>


<div class="col-xs-6 col-sm-9">
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h3 class="display-4">{{sanity_info['job_type']}} for {{sanity_info['build_number']}}</h3>
                {% if sanity_info['status'] == "COMPLETED" %}
                    <h3 class="display-4">PASS PERCENTAGE : {{ sanity_info['passed_percentage']}}% </h3>
                {% else %}
                    <h3 class="display-4">STATUS : {{sanity_info['status']}}</h3>
                {% endif %}
        </div>
    </div>
    <table id="functional_suite" class="table table-striped table-bordered" style="width: 100%">
        <thead class="table-primary">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col">Start Time</th>
            <th scope="col">Functional Suite</th>
            <th scope="col">Links</th>
            <th scope="col">Pass Count<br>Fail Count</th>
            <th scope="col">Total Count<br> Pass Percentage</th>
        </tr>
        </thead>
        <tbody>
        {% for sanity_report in sanity_result %}
        <tr>
            {% if sanity_report['start_time'] %}
                <td>{{sanity_report['start_time']}}</td>
            {% else %}
                <td>  </td>
            {% endif %}
            <td class="accordion-toggle" data-toggle='collapse' data-target="#{{sanity_report['functional_suite']}}">
                <a href="/test_report/functional_suite/{{sanity_report['functional_suite']}}"> {{sanity_report['functional_suite']}} </a>
                {% if sanity_report["fail_test_case"] not in (None,"","none") %}
                    <button class="btn outline:none" type="button" toggle="true" data-toggle="collapse" data-target="#{{suit}}" aria-expanded="false" aria-controls="#{{suit}}"><i class="fas fa-caret-down"></i></button>
                {% endif %}
            </td>
            {% if sanity_report['status'] != "IN_PROGRESS" %}
                <td>
                    {% if sanity_report.get('wrt_report') and sanity_report['wrt_report'].startswith('https://mitg-webtool.cisco.com/') %}
                        <a href = {{sanity_report['wrt_report']}} target="_blank"> WRT Report </a>
                    <br>
                    {% endif %}                        
                    {% if sanity_report['jenkins_job'] %}
                        <a href = {{sanity_report['jenkins_job']}} target="_blank"> Execution Link </a>
                    {% endif %}
                </td>
                <td style="text-align:left">
                    Pass Count: {{sanity_report['passed_case_count']}}
                    <br>
                    {% if sanity_report['failed_case_count'] %}
                        Fail Count: {{sanity_report['failed_case_count']}}
                    {% else %}
                        Fail Count: {{ sanity_report['total_feature_file_count'] - sanity_report['passed_case_count']}}
                    {% endif %}
                </td>
                <td>Total Count: {{sanity_report['total_feature_file_count']}}
                    <br>
                    {% if sanity_report["passing_criteria"] %}
                        {% if sanity_report["passing_criteria"] > sanity_report["passed_percentage"] %}
                            <p style="color:red;">Pass Percentage:{{sanity_report['passed_percentage']}}%</p>
                        {% else %}
                            <p style="color:green;">Pass Percentage:{{sanity_report['passed_percentage']}}%</p>
                        {% endif %}
                    {% else %}
                        Pass Percentage: {{sanity_report['passed_percentage']}}%
                    {% endif %}
                </td>
            {% else %}
                <td colspan="3" align="center"> {{sanity_report['status']}}</td>
            {% endif %}
        </tr>
        <tr> 
            {% if sanity_report["fail_test_case"] not in (None,"","none") %}
            <td colspan="100%" class="hiddenRow" >
                <div class="accordian-body collapse" id="{{sanity_report['functional_suite']}}">
            
                
                <ul class="nav nav-tabs" role="tablist">
                <li  class="nav-item active" role="presentation"><a href="#failed"><p style="color:red;" >Failed tests:</p></a>
                
                
                
                </li>
               <!--li class="nav-item" role="presentation">
               <a href="#passed">Passed tests({{sanity_report['passed_case_count']}}) </a>
                
                
               </li-->
               </ul>
            <div class="tab-content clearfix">
               
               <div class="tab-pane active" id="failed">
                {% autoescape false %}
                {{ sanity_report['fail_test_case'] | string | replace(",", "<br/>") }}
                {% endautoescape %}
               </div>
               <!--div class="tab-pane " id="passed">
                {% autoescape false %}
                {{ sanity_report['pass_test_case'] | string | replace(",", "<br/>") }}
                {% endautoescape %}
               </div-->
                 </div>
                 
                
                </div>
            </td>
        {% endif %}
        </tr>
        {% endfor %}
        <tr>
            <td> </td>
            <td>Total</td>
            <td></td>
            <td>Pass Count: {{sanity_info['passed_case_count']}}<br>
                Fail Count: {{sanity_info['failed_case_count']}}</td>
            <td>Total Count: {{sanity_info['total_feature_file_count']}}<br>
                Pass Percentage: {{sanity_info['passed_percentage']}}%</td>
        </tr>
        </tbody>
    </table>
    <br><br>
</div>
{% endblock %}
