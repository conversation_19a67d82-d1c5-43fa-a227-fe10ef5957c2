{% extends 'product_base.html' %}
{% block home %}

<div class="col-xs-6 col-sm-9">

    <br>
    <div class="jumbotron jumbotron-fluid">
        <div class="container">
            <h1 class="display-5">MYID Groups for Organization: {{product_org}}</h1>
            <p class="lead">List of MYID groups that exist for read,write,artifactory download and sonar access</p>
        </div>
    </div>
    <br> <br>

    <table id="approvers" class="table table-striped table-bordered" style="width:100%;">
        <thead class="table-primary">
        <tr style="text-align:center; background-color:#000000;color:white">
            <th scope="col">MYID Group</th>
            <th scope="col">Description</th>
            <th scope="col">OWNERS</th>
        </tr>
        </thead>
        <tbody>
        {% for items in team_name %}
        <tr>
            <td><a href=https://myid-groups.cisco.com/group-details/{{myid_group_info[items]['group_value']}}>{{ myid_group_info[items]['group_name'] }}</a></td>
            {% if myid_group_info[items]['group_name'].endswith('qa-read-gh') %}
            <td>Repositories: cd-pipeline
                <br> Services: Github(READ)</td>
            {% elif myid_group_info[items]['group_name'].endswith('qa-write-gh') %}
            <td> Repositories: cd-pipeline
                <br> Services: Github (WRITE), Artifactory Download, Sonar(Browse with Code)</td>
            {% elif myid_group_info[items]['group_name'].startswith('pats-read') %}
            <td> Repositories: All repos
                <br> Services: Github (READ)</td>
            {% elif myid_group_info[items]['group_name'].startswith('pats-write') %}
            <td> Repositories: All repos
                <br> Services: Github (WRITE), Artifactory Download, Sonar(Browse with Code)</td>
            {% elif myid_group_info[items]['group_name'].endswith('-read-gh') %}
            <td> Repositories: All except cd-pipeline
                <br> Services: Github(READ)</td>
            {% elif myid_group_info[items]['group_name'].endswith('app-infra-write-gh') %}
            <td> Repositories: app-infra
                <br> Services: Github (WRITE)</td>
            {% elif myid_group_info[items]['group_name'].endswith('-write-gh') %}
            <td> Repositories: All except cd-pipeline
                <br> Services: Github (WRITE), Artifactory Download, Sonar(Browse with Code)</td>
            {% elif myid_group_info[items]['group_name'].endswith('artifactory-external') %}
            <td> Services: Artifactory Download </td>
            {% elif myid_group_info[items]['group_name'].endswith('sonar-external') %}
            <td> Services: Sonar(Browse w/o code)</td>
            {% endif %}</td>
            <td>{{ myid_group_info[items]['owner'] }}</td>
        </tr>

        {% endfor %}
        </tbody>
    </table>
    <br>

</div>
{% endblock %}
