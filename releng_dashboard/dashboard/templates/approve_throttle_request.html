{% extends 'product_base.html' %}
{% block home %}

<link rel="stylesheet" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script type="text/javascript" language="javascript" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">

<script language="javascript">
$(document).ready(function() {
$('#qa_build').DataTable({
        "order": [[ 0, "desc" ]],
        "pageLength": 10
    });
} );
</script>

<div class="col-xs-6 col-sm-9">
    <form method="POST">
        {% if cdet_list %}
            <div class="fw-body" >
                <div class="content">
                    <br>
                    <br>
                    <h3 style="text-align:left">Pending Throttle Requests : {{product_org}}</h3>
                    <br>
                    <table id ="qa_build" class="table table-striped table-bordered" style="border: 1px solid gray;width:100%;">
                        <thead class="table-info">
                            <tr style="text-align:center; background-color:#000000;color:white">
                                <th scope="col" style="width: 5%">ID</th>
                                <th scope="col" style="width: 8%">CDETS</th>
                                <th scope="col" style="width: 10%">Branch</th>
                                <th scope="col" style="width: 5%">Date</th>
                                <th scope="col" style="width: 8%">Submitter</th>
                                <th scope="col" style="width: 20%">Justification </th>
                                <th scope="col" style="width: 5%">Status</th>
                                <th scope="col" style="width: 5%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cdets in cdet_list %}
                                <tr style="text-align:center">
                                    <th scope="row">{{cdets['id']}}</th>
                                    <td><a href=https://cdetsng.cisco.com/webui/#view={{cdets['id']}}>{{cdets['cdets_id']}}</a></td>
                                    <td>{{cdets['branches_branch_name']}}</td>
                                    {% if cdets['submission_date'] %}
                                        <td>{{cdets['submission_date']}}</td>
                                    {% else %}
                                        <td> </td>
                                    {% endif %}
                                    <td><a href=https://directory.cisco.com/dir/reports/{{cdets['submitter_cec_id']}}>{{cdets['submitter_cec_id']}}</a></td>
                                    <td>{{cdets['cdets_comment']}}</td>
                                    {% if cdets['approval_status'] == 'Pending' %}
                                        <td><font color = "orange">{{cdets['approval_status']}}</font></td>
                                    {% elif cdets['approval_status'] == 'Approved' %}
                                        <td><font color = "green">{{cdets['approval_status']}}</font></td>
                                    {% elif cdets['approval_status'] == 'Denied' %}
                                        <td><font color = "red">{{cdets['approval_status']}}</font></td>
                                    {% else %}
                                        <td>{{cdets['approval_status']}}</td>
                                    {% endif %}
                                    {% if user_role == "ORG_OWNER" or user_role == "DEV_MANAGER" or user_role == "RELENG" %}
                                        <td>
                                            <button type="button" class="btn btn-outline-success" data-toggle="modal" data-target="#approve{{ loop.index }}" title="approve"><i class="fas fa-check"></i></button>
                                            <div class="modal fade" id="approve{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h6 class="modal-title" id="approve">Are you sure you want to approve {{cdets['cdets_id']}} : {{ cdets['branches_branch_name'] }} ? </h6>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form method="POST">
                                                                {{form.csrf_token()}}
                                                                <div class="modal-body">
                                                                    <label> Comments </label>
                                                                    <div>
                                                                        {{form.cdets_comment(required='required') }}
                                                                        {{form.id(value=cdets['id'] ) }}
                                                                        {{form.cdet(value=cdets['cdets_id']) }}
                                                                        {{form.branch_name(value=cdets['branches_branch_name'])}}
                                                                    </div>
                                                                </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            {{form.approve(class="btn btn-success")}}
                                                        </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-danger" data-toggle="modal" data-target="#deny{{ loop.index }}" title="deny"><i class="fas fa-times"></i></button>
                                            <div class="modal fade" id="deny{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="start" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h6 class="modal-title" id="deny">Are you sure you want to deny {{cdets['cdets_id']}} : {{ cdets['branches_branch_name'] }} ? </h6>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form method="POST">
                                                                {{form.csrf_token()}}
                                                                <div class="modal-body">
                                                                    <label> Comments </label>
                                                                    <div>
                                                                        {{form.cdets_comment(required='required') }}
                                                                        {{form.id(value=cdets['id'] ) }}
                                                                        {{form.cdet(value=cdets['cdets_id']) }}
                                                                        {{form.branch_name(value=cdets['branches_branch_name'])}}
                                                                    </div>
                                                                </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            {{form.deny(class="btn btn-danger")}}
                                                        </div>
                                                    </div>
                                                   </form>
                                                </div>
                                            </div>
                                        </td>
                                    {% else %}
                                        <td> </td>
                                    {% endif %}
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% else %}
            <br>
            <br>
            <h3 style="text-align:left">No throttle requests to show : {{product_org}}</h3>
        {% endif %}
    </form>
</div>
{% endblock %}
