DEBUG_MODE="False"
ENVIRONMENT="PRODUCTION"
DB_INST="5g-prod"
JENKINS_SERVER="https://engci-private-sjc.cisco.com/jenkins/cn-releng/"
QA_BUILD_JOB_NAME="CN_ReleaseToQA/5G_QA_Builds_Token"
SMI_NSO_QA_JOB_NAME="5G-SMI-NSO/5G_QA_Builds_Token_NSO"
QA_LOC_JOB_NAME="Developer_Jobs/CN_LOC_BETWEEN_QA_BUILDS"
THROTTLE_APPROVAL_JOB_NAME="Github_Webhooks/CDETS_Check"
THROTTLE_CDETS_VALIDATION_JOB_NAME="Github_Webhooks/throttle_tracker_validate_cdets"
DATE_LOC_JOB_NAME="Developer_Jobs/CN_LOC_BETWEEN_DATES"
DATE_USER_LOC_JOB_NAME="Developer_Jobs/CN_USER_LOC_BETWEEN_DATES"
DEV_OFFLINE_BUILD_JOB_NAME="Developer_Jobs/Dev_Offline_Bundle_Create_Token"
DEVHUB_BUILD_JOB_NAME="CN_PromoteToCustomer/5G_Release_Builds_Token"
SONAR_RUN_JOB_NAME="Developer_Jobs/code_coverage_branch"
SONAR_RUN_JOB_NAME_RHEL_8="Developer_Jobs/code_coverage_branch_admin"
SONAR_REPORT_JOB_NAME="Developer_Jobs/sonar_report"
GOSEC_REPORT_JOB_NAME="Developer_Jobs/gosec_report_all_cn"
CLOC_REPORT_JOB_NAME="Developer_Jobs/CN_CLOC"
CCO_BUILD_JOB_NAME="Dashboard_CCO_Builds"
ADD_ACCESS_REQ_JOB_NAME="Github_Admin_Activity/Dash_github_add_access_request_to_db"
PROCESS_ACCESS_REQ_JOB_NAME="Github_Admin_Activity/Dash_process_access_request"
PROCESS_MYID_ACCESS_REQ_JOB_NAME="Github_Admin_Activity/Join_MYID_Group"
GITHUB_ACCESS_REQ_PROCESS_JOB="Github_Admin_Activity/process_access_req_viaMyID"
PROCESS_USER_ADDITION_JOB_NAME="Scheduled_Jobs/userinfo_addition"
PR_BY_PASS_JOB_NAME="Github_Admin_Activity/PR_check_by_pass"
MERGE_PIPELINE_JOB_NAME="Github_Admin_Activity/Merge_Declarative_Pipeline"
MERGE_REFRESH_JOB_NAME="Github_Admin_Activity/merge_github_dashboard_event"
CORONA_SCAN_JOB_NAME="CN_ReleaseToQA/Corona_Scan_Tool_Declarative_Pipeline"
CREATE_BRANCH_REF_JOB_NAME="CN_Throttle_Pull/change_reference"
CREATE_BRANCH_PRODUCT_JOB_NAME="CN_Throttle_Pull/throttle-pull-pipeline"
CEE_ARTIFACTORY_URL="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-apps/smi-cee-products/"
DEPLOYER_ARTIFACTORY_URL="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-apps/smi-cluster-deployer-products/"
BASEVM_ARTIFACTORY_URL="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/sprint_releases/smi-base-vm/"
CEE_BM_ARTIFACTORY_URL="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-apps/smi-cee-products/"
DEPLOYER_BM_ARTIFACTORY_URL="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-apps/smi-cluster-deployer-products/"
BASE_BM_ARTIFACTORY_URL="https://engci-maven-master.cisco.com/artifactory/smi-fuse-internal-group/releases/smi-base-image-iso/"
GRAFANA_HOST=cn-rel-dash-lnx.cisco.com
CDL_RELENG_ARTIFACTORY="https://engci-maven-master.cisco.com/artifactory/mobile-cnat-charts-release/releng-builds/cdl-products/"
MERGED_FEATURE_CC_REPORT_JOB_NAME="Code_Coverage/feature_code_coverage"
ONGOING_FEATURE_CC_REPORT_JOB_NAME="Code_Coverage/product_sonar_analysis_ut"
DELETE_BRANCH_JOB_NAME="Github_Admin_Activity/delete_branch"
CREATE_BRANCH_JOB_NAME="CN_Throttle_Pull/create_branch"
CLOSE_MERGE_ID="Github_Admin_Activity/close_merge_id"
CLOSE_PR="Github_Admin_Activity/merge_close_pr"
CDETS_DIFF_JOB_NAME="Developer_Jobs/CDETS_Diff_Main_vs_Release"