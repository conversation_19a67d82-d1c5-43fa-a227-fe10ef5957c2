from flask import Blueprint, request, jsonify, make_response
from functools import wraps
import jwt
from .. import app
import ldap
from dashboard.build.models import Builds
from dashboard.sonar.models import Sonar, FeatureCoverage
from dashboard.models import IVT, SANITY_REPORTS, Branches, Products
from dashboard.tasks import get_list_passed_cc_build, get_product_organization
from .. import db
from pprint import pprint
from os import environ
import sys, os
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
import Constants as Constants
from Bot import bot_send_message
from humanfriendly import format_timespan
import time
import datetime
from common import get_content
from wrt_parse import list_test_case
from Utils import *
rest_api = Blueprint('rest_api', __name__)

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if 'token' in request.headers:
            token = request.headers['token']
        if not token:
            return jsonify({'message' : 'Token is missing!'}), 401
        try:
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=["HS256"])
        except Exception as e:
            return jsonify({'message' : 'Token is invalid! ERROR: %s' % e }), 401

        return f(data['email'], *args, **kwargs)

    return decorated


def validate_user(username, password):
    # https://www.cisco.com/c/en/us/support/docs/security/asa-5500-x-series-next-generation-firewalls/91831-mappingsvctovpn.html
    AD_SEARCH_DN = 'OU=Cisco Users,DC=cisco,DC=com'
    # https://docs.secureauth.com/display/KBA/Active+Directory+Attributes+List
    AD_SEARCH_FIELDS = dict(
        email='mail',
        givenName='givenName'
    )
    try:
        active_dir = ldap.initialize(Constants.AD_LDAP_URL)
        active_dir.simple_bind_s('%<EMAIL>' % username, password)
        user_info = active_dir.search_ext_s(AD_SEARCH_DN, ldap.SCOPE_SUBTREE,"CN=%s" % username, AD_SEARCH_FIELDS.values())[0][1]
        active_dir.unbind_s()
        email = user_info["mail"][0].decode("utf-8")
    except ldap.LDAPError:
        email = None
    return email


@rest_api.route('/get_token', methods=['GET'])
def login():
    auth = request.authorization
    if not auth or not auth.username or not auth.password:
        return make_response('Could not verify', 401, {'WWW-Authenticate' : 'Basic realm="CEC Login required!"'})
    user_mail = validate_user(auth.username, auth.password)
    if not user_mail:
        return make_response('Could not verify', 401, {'WWW-Authenticate' : 'Basic realm="CEC Login required!"'})
    else:
        token = jwt.encode({'email' : user_mail, 'exp' : datetime.datetime.utcnow() + datetime.timedelta(days=180)}, app.config['SECRET_KEY'], algorithm="HS256")
        return jsonify({'token' : token})


@rest_api.route('/verify_token', methods=['GET'])
@token_required
def verify_token(user_email):
    token = request.headers['token']
    data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=["HS256"])
    # time left will always to a positive int.
    time_left = int(data['exp']) - int(time.time())
    if time_left:
        format_timespan(time_left)
        return jsonify({'expire_in' : format_timespan(time_left)})


@rest_api.route('/<string:product>/<string:branch_name>/latest', methods=['GET'])
@token_required
def get_latest_qa_build(user_email, product, branch_name):
    try:
        qa_build_info = []
        build_mobj = Builds()
        for row in build_mobj.query.filter_by(products_product_name=product, branches_branch_name=branch_name, build_status="Passed").order_by(Builds.id.desc()).limit(2).all():
            data = row.__dict__
            qa_build_info.append(data)
        if qa_build_info:
            product_build_number, qa_build_loc, pre_qa_build_loc, pre_product_build_number = None, None, None, None
            for count, data in enumerate(qa_build_info):
                if count == 0:
                    qa_build_loc = data["int_artifactory"]
                    product_build_number="%s.%s" % (product, data["build_number"])
                if count == 1:
                    pre_qa_build_loc = data["int_artifactory"]
                    pre_product_build_number="%s.%s" % (product, data["build_number"])
            return jsonify({'product_build_number' : product_build_number,
                            'qa_build_loc' : qa_build_loc,
                            'branch_name' : branch_name,
                            'previous_build_number' : pre_product_build_number,
                            'previous_qa_build_loc' : pre_qa_build_loc })
        else:
            return make_response('Invalid branch name or product name', 404, {'NotFound' : 'The requested resource was not found'})
    except Exception as e:
        return make_response('Exception during DB operation. Plz contact releng team' % e, 500, {'DB Error' : 'Internal Server Error : %s " % e'})


@rest_api.route('/<string:product>/<string:branch_name>/ut_numbers', methods=['GET'])
@token_required
def get_latest_ut_numbers(user_email, product, branch_name):
    try:
        columns = ['ut_failed','ut_error','ut_passed']
        product_dic = {}
        # Check if the product is supported by quality metrics
        if product in Constants.quality_metrics_product_mapping:
            sonar_obj = Sonar()
            sub_products = Constants.quality_metrics_product_mapping[product]
            # ex: if product is smf. sub products will be smf & converged_core_shared
            for sub_product in sub_products:
                sonar_info = {}
                # this is to extract what all org & repos are part of quality metric product.
                for product_info in Constants.quality_metrics_product_repo[sub_product]:
                    for organization, repo_list in product_info.items():
                        for repository in repo_list:
                            # get the latest UT number from DB (whatever is latest)
                            result = sonar_obj.query.filter_by(organization=organization, repository=repository, branch=branch_name).order_by(Sonar.id.desc()).limit(1).all()
                            if result:
                                data = result[0].__dict__
                                repo_data = {}
                                for k,v in data.items():
                                    if k in columns:
                                        repo_data[k] = str(v)
                                sonar_info[repository] = repo_data
                            else:
                                sonar_info[repository] = {}
                product_dic[sub_product] = sonar_info
                # Creating nested dictionary which will look like this
                # { "converged_core_shared":
                                            # { "gtpp-ep": { "ut_error": "2", "ut_failed": "0", "ut_passed": "847" },
                                            # "oam-pod": { "ut_error": "2", "ut_failed": "0", "ut_passed": "0" },
                                            # "protocol": { "ut_error": "0", "ut_failed": "0", "ut_passed": "490" } },
                # "smf":
                    # { "smf-common": { "ut_error": "3", "ut_failed": "0", "ut_passed": "0" },
                    # "smf-service": { "ut_error": "0", "ut_failed": "0", "ut_passed": "874" } } }
            return jsonify(product_dic)
        else:
            return make_response('Invalid product name', 404, {'NotFound' : 'The requested resource was not found'})
    except Exception as e:
        return make_response('Exception during DB operation. Plz contact releng team' % e, 500, {'DB Error' : 'Internal Server Error : %s " % e'})


@rest_api.route('/<string:product>/<string:release_version>/feature_cc', methods=['GET'])
@token_required
def get_latest_feature_cc(user_email,product, release_version):
    try:
        if release_version:
            # Using the release version get the data from feature_coverage table.
            feature_info = {}
            feature_coverage_obj = FeatureCoverage()
            release_version = release_version + ".m0"
            if product in ['smf','sgw']:
                product = "ccg"
            result = feature_coverage_obj.query.filter_by(release_version=release_version, product=product).all()
            if result:
                # Return back json with FEAT Info
                columns = ['sonar_app_name','sonar_app_branch']
                for row in result:
                    data = row.__dict__
                    feature_data = {}
                    for k,v in data.items():
                        if k in columns:
                            feature_data[k] = str(v)
                    feature_info[data["feat_id"]] = feature_data
            return jsonify(feature_info)
        else:
            return make_response('release_version is mandatory', 404, {'NotFound' : 'The requested resource was not found'})
    except Exception as e:
        return make_response('Exception during DB operation. Plz contact releng team' % e, 500, {'DB Error' : 'Internal Server Error : %s " % e'})

@rest_api.route('/<string:product>/<string:branch_name>/ft_numbers', methods=['GET'])
@token_required
def get_latest_ft_numbers(user_email, product, branch_name):
    try:
        columns = ['total_feature_file_count','passed_case_count','failed_case_count','passed_percentage']
        sanity_obj = SANITY_REPORTS()
        # Check if the product is supported by ft quality metrics
        if product in Constants.ft_quality_metrics_product_mapping.keys():
            main_product = Constants.ft_quality_metrics_product_mapping[product]
            result_info={}
            for entries in main_product:
                # Get the entries in the product for which ft numbers to be populated
                for sub_prod,sub_prod_ctg in entries.items():
                    result_info_tmp= {}
                    # Execute if the product has further categorization Ex: smf -> smf,converged_core_shared
                    for items in sub_prod_ctg:
                        temp_data = {}
                        job_type = Constants.jt_products[items]
                        functional_suite= Constants.fs_products[items]
                        if items in ['smf','pgw','sgw']:
                            product_name = 'ccg'
                        else:
                            product_name = items
                        build_details,bn_details = get_list_passed_cc_build(items,branch_name)
                        if build_details== None or bn_details==None:
                            return make_response('Data not present/Branch name is invalid',404)
                        branch_list=list(build_details.values())
                        result = sanity_obj.query.filter_by(products_product_name=product_name,job_type=job_type,functional_suite=functional_suite,status="COMPLETED").filter(SANITY_REPORTS.branch_name.in_((branch_list))).order_by(SANITY_REPORTS.end_time.desc()).limit(5).all()
                        if result:
                            for row in result:
                                data = row.__dict__
                                if data:
                                    report_data = {}
                                    for key,value in data.items():
                                        if key in columns:
                                            report_data[key] = str(value)
                                        # Append parent build number detail for which ft numbers are fetched
                                        temp_data = data.get('branch_name')
                                    report_data['build_number'] = bn_details[temp_data]
                                    result_info_tmp[items] = report_data
                                    break
                    result_info[sub_prod] = result_info_tmp
            if (result_info[sub_prod] == {}):
                return make_response('FT numbers not available for the product',404)
            return jsonify(result_info)
        else:
            return make_response('Invalid product name', 404, {'NotFound' : 'The requested resource was not found'})
    except Exception as e:
        return make_response('Exception during DB operation. Plz contact releng team' % e, 404, {'DB Error' : 'Data not present/Branch is invalid : %s " % e'})

@rest_api.route('/ivt_result', methods=['POST'])
@token_required
def add_ivt_result_to_db(user_email):
    return make_response('ivt_result is not supported by releng dashboard. Plz use sanity_result', 400, {'Bad Request' : 'ivt_result is not supported by releng dashboard'})
    """
    if request.method == 'POST':
        if "PRODUCT_BUILD_NUMBER" not in request.json:
            return make_response('Mandatory parameters PRODUCT_BUILD_NUMBER is missing', 400, {'Bad Request' : 'Mandatory parameters are missing.'})

        product_build_number = request.json['PRODUCT_BUILD_NUMBER']
        if "." not in product_build_number:
            return make_response('Invalid qa build number or product name', 404, {'NotFound' : 'The requested resource was not found'})

        product = product_build_number.split(".",1)[0]
        build_number = product_build_number.split(".",1)[1]

        # Check if the qa build number is valid
        # qa_build_info will return back branch name if qa build exists
        qa_build_info = query_build_number(product,build_number,"builds")
        if not qa_build_info:
            return make_response('Invalid qa build number or product name', 404, {'NotFound' : 'The requested resource was not found'})
        else:
            qa_build_branch_name = qa_build_info["branches_branch_name"]

        # Check if entry of QA build is present in IVT Table
        ivt_build_info = query_build_number(product,build_number,"ivt")
        if not ivt_build_info:
            missing_params = []
            for param in ['WRT_REPORT']:
                if param not in request.json:
                    missing_params.append(param)
            if missing_params:
                return make_response('Mandatory parameters are missing %s' % ",".join(missing_params), 400, {'Bad Request' : 'Mandatory parameters are missing.'})
            ivt_obj = IVT()
            db_command = "insert"
        else:
            ivt = IVT()
            ivt_obj = ivt.query.filter_by(products_product_name=product, build_number=build_number).first()
            db_command = "update"

        ivt_obj.products_product_name = product
        ivt_obj.build_number = build_number
        ivt_obj.branch_name = qa_build_branch_name
        if "WRT_REPORT" in request.json:
            ivt_obj.wrt_report = request.json['WRT_REPORT']
        if "IVT_APPROVED" in request.json:
            ivt_obj.ivt_approved = request.json['IVT_APPROVED']
        if "PASSED_PERCENTAGE" in request.json:
            ivt_obj.passed_percentage = int(request.json['PASSED_PERCENTAGE'])
        if "TOTAL_FEATURE_FILE_COUNT" in request.json:
            ivt_obj.total_feature_file_count = int(request.json['TOTAL_FEATURE_FILE_COUNT'])
        if "PASSED_CASE_COUNT" in request.json:
            ivt_obj.passed_case_count = int(request.json['PASSED_CASE_COUNT'])
        if "FAILED_CASE_COUNT" in request.json:
            ivt_obj.failed_case_count = int(request.json['FAILED_CASE_COUNT'])
        if "START_TIME" in request.json:
            # date should be in following format : 2020-Nov-20 05:09:55 GMT
            ivt_obj.start_time = datetime.datetime.strptime(request.json['START_TIME'],"%Y-%b-%d %H:%M:%S %Z")
        if "END_TIME" in request.json:
            ivt_obj.end_time = datetime.datetime.strptime(request.json['START_TIME'],"%Y-%b-%d %H:%M:%S %Z")
        try:
            if db_command == "insert":
                db.session.add(ivt_obj)
            db.session.commit()
        except Exception as e:
            pprint("Unable to write to DB %s" % e)
            db.session.rollback()
            db.session.close()
            if environ.get("ENVIRONMENT") == "PRODUCTION":
                room_id = environ.get("TEAMS_ROOM_ID")
                bot_send_message(room_id=room_id, message="HIGH: Unable to write IVT info to DB during POST "
                                                          "operation.\n" + ivt_obj)
            return make_response('Exception during DB operation. Plz contact releng team %s' % e, 500, {'DB Error' : 'Internal Server Error'})
        finally:
            db.session.close()
        return make_response('Successfully updated DB %s' % ivt_obj, 200, {'SUCCESS' : '%s' % request})
    """

@rest_api.route('/sanity_result', methods=['POST'])
@token_required
def add_sanity_result_to_db(user_email):

    if request.method == 'POST':
        # Check for mandatory parameters in json
        for param in ["PRODUCT_BUILD_NUMBER","FUNCTIONAL_SUITE","JOB_TYPE","STATUS"]:
            if param not in request.json:
                return make_response('Mandatory parameters %s is missing' % param, 400, {'Bad Request' : 'Mandatory parameters are missing.'})

        product_build_number = request.json['PRODUCT_BUILD_NUMBER']
        functional_suite = request.json['FUNCTIONAL_SUITE']
        job_type = request.json['JOB_TYPE']

        if "." not in product_build_number:
            return make_response('Invalid qa build number or product name', 404, {'NotFound' : 'The requested resource was not found'})

        product = product_build_number.split(".",1)[0]
        build_number = product_build_number.split(".",1)[1]

        # Don't accept Functional suite as Total
        if functional_suite.lower() == "total":
            return make_response('Functional suite name as Total is not accepted.', 400, {'Bad Request' : 'Total is not allowed as functional suite'})


        # Check if the qa build number is valid
        qa_build_info = query_build_number(product,build_number,"builds")
        if not qa_build_info:
            return make_response('Invalid qa build number or product name', 404, {'NotFound' : 'The requested resource was not found'})
        else:
            qa_build_branch_name = qa_build_info["branches_branch_name"]

        # Check if entry of QA build & Functional suite is present in SANITY Table
        sanity_build_info = query_sanity_table(product, build_number,functional_suite, job_type)
        if not sanity_build_info:
            sanity_obj = SANITY_REPORTS()
            sanity_obj.products_product_name = product
            sanity_obj.build_number = build_number
            sanity_obj.functional_suite = functional_suite
            sanity_obj.branch_name = qa_build_branch_name
            db_command = "insert"
        else:
            sanity = SANITY_REPORTS()
            sanity_obj = sanity.query.filter_by(products_product_name=product, build_number=build_number, functional_suite=functional_suite).first()
            db_command = "update"

        if "WRT_REPORT" in request.json:
            sanity_obj.wrt_report = request.json['WRT_REPORT']
            content = get_content(request.json['WRT_REPORT'], None, None)
            if content:
                fail_test_case = list_test_case("failTestsTable", content)
                pass_test_case = list_test_case("passTestsTable", content)
                if fail_test_case:
                    sanity_obj.fail_test_case = ",".join(fail_test_case)
                if pass_test_case:
                    sanity_obj.pass_test_case = ",".join(pass_test_case)
        if "SANITY_APPROVED" in request.json:
            sanity_obj.sanity_approved = request.json['SANITY_APPROVED']
        if "PASSED_PERCENTAGE" in request.json:
            sanity_obj.passed_percentage = int(request.json['PASSED_PERCENTAGE'])
        if "TOTAL_FEATURE_FILE_COUNT" in request.json:
            sanity_obj.total_feature_file_count = int(request.json['TOTAL_FEATURE_FILE_COUNT'])
        if "PASSED_CASE_COUNT" in request.json:
            sanity_obj.passed_case_count = int(request.json['PASSED_CASE_COUNT'])
        if "FAILED_CASE_COUNT" in request.json:
            sanity_obj.failed_case_count = int(request.json['FAILED_CASE_COUNT'])
        if "START_TIME" in request.json:
            # date should be in following format : 2020-Nov-20 05:09:55 GMT
            sanity_obj.start_time = datetime.datetime.strptime(request.json['START_TIME'],"%Y-%b-%d %H:%M:%S %Z")
        if "END_TIME" in request.json:
            sanity_obj.end_time = datetime.datetime.strptime(request.json['START_TIME'],"%Y-%b-%d %H:%M:%S %Z")
        if "JOB_TYPE" in request.json:
            sanity_obj.job_type = request.json['JOB_TYPE']
        if "JENKINS_JOB" in request.json:
            sanity_obj.jenkins_job = request.json['JENKINS_JOB']
        if "STATUS" in request.json:
            sanity_obj.status = request.json['STATUS']
        if "PASSING_CRITERIA" in request.json:
            sanity_obj.passing_criteria = request.json['PASSING_CRITERIA']
        try:
            if db_command == "insert":
                db.session.add(sanity_obj)
            db.session.commit()
        except Exception as e:
            pprint("Unable to write to DB %s" % e)
            db.session.rollback()
            db.session.close()
            if environ.get("ENVIRONMENT") == "PRODUCTION":
                room_id = environ.get("TEAMS_ROOM_ID")
                bot_send_message(room_id=room_id, message="HIGH: Unable to write Sanity Info to DB during POST "
                                                          "operation.\n" + sanity_obj)
            return make_response('Exception during DB operation. Plz contact releng team %s' % e, 500, {'DB Error' : 'Internal Server Error'})
        finally:
            db.session.close()

        total_feature_file_count, passed_case_count = 0, 0
        obj = SANITY_REPORTS()
        functional_suite_status = []
        for row in obj.query.filter_by(products_product_name=product, build_number=build_number, job_type=job_type):
            data = row.__dict__
            if data["functional_suite"] != "Total":
                functional_suite_status.append(data["status"])
                if data["total_feature_file_count"]:
                    total_feature_file_count += data["total_feature_file_count"]
                if data["passed_case_count"]:
                    passed_case_count += data["passed_case_count"]

        # Update Functional Suite : Total
        sanity_build_info = query_sanity_table(product, build_number, "Total", job_type)
        if not sanity_build_info:
            db_command = "insert"
            sanity_obj_all = SANITY_REPORTS()
            sanity_obj_all.products_product_name = product
            sanity_obj_all.build_number = build_number
            sanity_obj_all.functional_suite = "Total"
            sanity_obj_all.branch_name = qa_build_branch_name
            sanity_obj_all.wrt_report = " "
            sanity_obj_all.job_type = job_type
        else:
            db_command = "update"
            sanity = SANITY_REPORTS()
            sanity_obj_all = sanity.query.filter_by(products_product_name=product, build_number=build_number, functional_suite="Total", job_type=job_type).first()

        failed_case_count = total_feature_file_count - passed_case_count

        sanity_obj_all.start_time = datetime.datetime.now()
        sanity_obj_all.total_feature_file_count = total_feature_file_count
        sanity_obj_all.passed_case_count = passed_case_count
        sanity_obj_all.failed_case_count = failed_case_count
        if "IN_PROGRESS" not in functional_suite_status:
            if total_feature_file_count == 0:
                passed_percentage = 0
            else:
                passed_percentage = (passed_case_count / total_feature_file_count) * 100
            sanity_obj_all.status = "COMPLETED"
            sanity_obj_all.passed_percentage = int(float(passed_percentage))
        else:
            sanity_obj_all.passed_percentage = 0
            if total_feature_file_count > 0:
                sanity_obj_all.status = "PARTIALLY_COMPLETE"
            else:
                sanity_obj_all.status = "IN_PROGRESS"
        try:
            if db_command == "insert":
                db.session.add(sanity_obj_all)
            db.session.commit()
        except Exception as e:
            pprint("Unable to write to DB %s" % e)
            db.session.rollback()
            db.session.close()
            if environ.get("ENVIRONMENT") == "PRODUCTION":
                room_id = environ.get("TEAMS_ROOM_ID")
                bot_send_message(room_id=room_id, message="HIGH: Unable to write Functional Suite Total to DB during "
                                                          "POST operation.\n" + sanity_obj_all)
            return make_response('Exception during DB operation. Plz contact releng team %s' % e, 500, {'DB Error' : 'Internal Server Error'})
        finally:
            db.session.close()
        return make_response('Successfully updated DB for %s' % functional_suite, 200, {'SUCCESS' : '%s' % request})


def query_build_number(product, build_number, table):
    if table == "builds":
        obj = Builds()
    if table == "ivt":
        obj = IVT()
    for row in obj.query.filter_by(products_product_name=product, build_number=build_number).all():
        data = row.__dict__
        if data:
            return data

def query_sanity_table(product, build_number, functional_suite,job_type):
    obj = SANITY_REPORTS()
    for row in obj.query.filter_by(products_product_name=product, build_number=build_number, functional_suite=functional_suite, job_type=job_type).all():
        data = row.__dict__
        if data:
            return data
