from flask import render_template, Blueprint, flash, redirect, request
from .models import ACCESS_REQUESTS, GITHUB_ACCESS_INFO, MYID_ACCESS_REQUESTS
from .. import mail
from datetime import datetime
from os import environ
from dashboard.tasks import get_product_list, get_product_organization,get_dashboard_user_role,get_dashboard_role_list
from dashboard.build.tasks import trigger_j<PERSON><PERSON>_build
access_control = Blueprint('access_control', __name__)
from dashboard import oidc
from dashboard.models import DASHBOARD_ACCESS
from .forms import AccessControl_form, AccessControl_Approve_form, RemoveUser, AddUser,AccessControl_myID, AccessControl_MYID_Approve_form
from .tasks import create_new_access_request, process_access_requests, get_access_owners, remove_users, send_mail_list, process_myid_join_request,process_myid_req
import sys
import os
import re
from .. import mail, db
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
from GithubApi import GithubApi
import myid_group_info as myid_group_info
import time
from cisco_utils import is_cec_valid
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../promotion/")
from MyIDGroup import MyIDGroup

@oidc.require_login
def access_control_home(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash(product_org)
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    if product in ["upf","staros"]:
        flash("%s product do not support this feature. For further assistance, <NAME_EMAIL> " % product, "info")
        return redirect('/home')
    if product in ["up-recovery"]:
        flash("Raise Access request under smi-nso-cfp. For further assistance, <NAME_EMAIL> " , "info")
        return redirect('/home')

    form = AccessControl_Approve_form ()
    user_role,repo_name = get_dashboard_user_role(product_org,oidc.user_getfield('uid'))
    req_list = []
    info_list= []
    show_active_button = False
    access_obj = ACCESS_REQUESTS()
    note_dic = {}

    if form.validate_on_submit():
        action = ""
        if form.approve.data:
            action = "Approved"
        elif form.deny.data:
            action = "Denied"
        elif form.cancel.data:
            action = "Cancelled"
        elif form.add_info.data:
            action = "Add_Info"
        elif form.req_info.data:
            action = "Req_Info"
        if action in ("Approved", "Denied", "Cancelled", "Add_Info", "Req_Info"):
            if form.repo.data == 'All':
                  result = process_access_requests(str(oidc.user_getfield('uid')), product, product_org, form.userid.data, form.req_submitter.data, form.type_id.data, form.approver_comment.data, form.id.data, action,form.category_id.data, form.approver_list.data)
            else:
                  result = process_access_requests(str(oidc.user_getfield('uid')), product, product_org, form.userid.data, form.req_submitter.data, form.type_id.data, form.approver_comment.data, form.id.data, action,form.category_id.data, form.approver_list.data, repos=form.repo.data)
        if form.submit_all.data:
            show_active_button = True
            for row in access_obj.query.filter_by(PRODUCT_NAME=product).order_by(ACCESS_REQUESTS.id.desc()).limit(100).all():
                dict = row.__dict__
                req_list.append(dict)
        else:
            return redirect('/access_control_home/%s' % product)
    else:
        for row in access_obj.query.filter_by(PRODUCT_NAME=product).filter(ACCESS_REQUESTS.STATUS.in_(('Pending', 'Need Info'))).order_by(ACCESS_REQUESTS.id.desc()).limit(100).all():
            dict = row.__dict__
            req_list.append(dict)
    for req in req_list:
        access_info = GITHUB_ACCESS_INFO()
        i_list = []
        id = req["id"]
        for row in access_info.query.filter_by(REQ_ID=id).order_by(GITHUB_ACCESS_INFO.id.asc()).limit(101).all():
            dict = row.__dict__
            i_list.append(dict)
        note_list = []
        for row in i_list:
            note  = ''
            #note += "<span style='font-weight:bold'>" + row["USER_ID"] +"</span>"
            note += '<b>' +row["USER_ID"] +'</b>' 
            note += datetime.fromtimestamp(row["TIME"]).strftime(':%Y-%m-%d %H:%M: ')+ '<br/>'
            note += row["NOTE"] + '<br/>'
            note_list.append(note.replace(' ', '\ '))
        if len(note_list) > 0:
            note_dic[row["REQ_ID"]] = ''.join(note_list) 
    return render_template('access_control_home.html', product_org=product_org, product=product, req_list=req_list, note_dic=note_dic, form=form, user_role=user_role,show_active_button=show_active_button,login_user=oidc.user_getfield('uid'))



@oidc.require_login
def process_access_request(product):
    # Pre-checks
    product_list = get_product_list()
    username = oidc.user_getfield('uid')
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')

#    if request.method == "POST":
#        flash(list(request.form.listvalues()))
    form = AccessControl_Approve_form()

    if form.validate_on_submit():
        if form.approve.data:
            action = "Approved"
        elif form.deny.data:
            action = "Denied"
        elif form.add_info.data:
            action = "Add_Info"
        elif form.req_info.data:
            action = "Req_Info"
        else:
            action = "Cancelled"
        #flash("Calling approve_access_requests for user: %s" %(form.userid.data), "info")
        try:
                    # Update database
                    if form.repo.data == 'All':
                        process_access_requests(username, product, product_org, form.userid.data, form.req_submitter.data, form.type_id.data, form.approver_comment.data, form.id.data, action,form.category_id.data, form.approver_list.data)
                    else:
                        process_access_requests(username, product, product_org, form.userid.data, form.req_submitter.data, form.type_id.data, form.approver_comment.data, form.id.data, action,form.category_id.data,form.approver_list.data, repos=form.repo.data)
                    #send_email_notifiation(username, cdet, product_org, branch, cdets_comment, product)
        except Exception as e:
                    flash("Caught an exception: %s" % e, "danger")
                    return redirect('/access_control_home/%s' % product)
        finally:
                    if form.errors:
                        flash("flash error : %s " % form.errors , "info")

    access_obj = ACCESS_REQUESTS()
    user_role,repo_name = get_dashboard_user_role(product_org,username)
    users_pending = []
    for row in access_obj.query.filter_by(STATUS='Pending', PRODUCT_NAME=product).order_by(ACCESS_REQUESTS.id.desc()).limit(100).all():
            dict = row.__dict__
            users_pending.append(dict)
#    flash(users_pending)

    return render_template('approve_access_request.html', product_org=product_org, product=product, user_list=users_pending, form=form, user_role=user_role,login_user=username)


@oidc.require_login
def create_access_request(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')

    submitter = oidc.user_getfield('uid')
    user_role,repo_name = get_dashboard_user_role(product_org,submitter)
    form = AccessControl_form()
    if request.method == "POST":
        userids = form.userid.data # comma seperated list of IDs
        process_users = ""
        for uid in set(userids.split(",")):
                #Checks per user-ID :
                # 1. Valid USER-ID
                # 2. Already part of requested team or not
                # 3. Request already exist for the user and is in Pending state

                    justification = form.justification.data.replace('\'','')
                    type_id = form.type_id.data
                    category_id=form.category_id.data
                    select_all_repos = form.select_all_repos.data
                    if not select_all_repos:
                        repos = request.form.getlist("repo_list")
                    else:
                        repos = ['All']
#                    flash("Repos: %s" % repos, "info")
                    ret_val = validate_user_request(product, product_org,uid,type_id,repos)
                    if ret_val == True:
                        # Check if no existing request exist in DB for same user, type_id, category but in Pending state
                        access_obj = ACCESS_REQUESTS()
                        repo_str = ','.join(repos)
#                        flash("Repo string is %s" % repo_str, "info")
                        existing_request_list=access_obj.query.filter_by(PRODUCT_NAME=product,GRP_ID='github', TYPE_ID=type_id, CATEGORY_ID=category_id, USERID=uid, REPO=repo_str, STATUS='Pending').all()
                        if len(existing_request_list) >= 1:
                            flash("Request for user %s with %s access already exists. It will be skipped." % (uid,type_id), "danger")
                        else:
                            process_users += uid + ","
        process_users = process_users.rstrip(',')
        if process_users:
                if not select_all_repos:
                    result = create_new_access_request(process_users, product, product_org, justification, type_id, category_id,submitter, repos)
                else:
                    result = create_new_access_request(process_users, product, product_org, justification, type_id, category_id,submitter)
                return redirect('/access_control_home/%s' % product)

    github_obj = GithubApi()
    all_repos = github_obj.list_all_repos_names(product_org)
    return render_template('submit_access_request.html', form=form, product=product, product_org=product_org, all_repos=all_repos, user_role=user_role,login_user=submitter)

@access_control.route('/submit_myid_req/<product>', methods=['GET', 'POST'])
@oidc.require_login
def submit_myid_req(product):
    show_active_button = False
    no_access_req_pdt = ['pgw','udc','upf']
    if product in no_access_req_pdt:
        flash("%s product is not supported for accommodating MYID Group join request" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    myid_joinreq_obj = MYID_ACCESS_REQUESTS()
    record_list = []
    submitter = oidc.user_getfield('uid')
    user_role,repo_name = get_dashboard_user_role(product_org,submitter)
    form = AccessControl_MYID_Approve_form()
    if form.validate_on_submit():
        if form.approve.data or form.deny.data:
            if form.approve.data:
                action="Approved"
            elif form.deny.data:
                action="Denied"
            response = process_myid_req(product,product_org,form.id.data,form.myidgrpname.data,form.userid.data,action,form.approver_comment.data,submitter)
            return redirect('/submit_myid_req/%s' % product)
    if form.submit_all.data:
        show_active_button = True
        for row1 in myid_joinreq_obj.query.filter_by(PRODUCT_NAME=product).order_by(MYID_ACCESS_REQUESTS.id.desc()).limit(100).all():
            dict = row1.__dict__
            record_list.append(dict)
    else:
        for row1 in myid_joinreq_obj.query.filter_by(STATUS='Pending',PRODUCT_NAME=product).order_by(MYID_ACCESS_REQUESTS.id.desc()).limit(100).all():
            dict = row1.__dict__
            record_list.append(dict)
    return render_template('access_control_myid.html',product=product,record_list=record_list,product_org=product_org,user_role=user_role,show_active_button=show_active_button,form=form)

@access_control.route('/join_myid_group/<product>', methods=['GET', 'POST'])
@oidc.require_login
def join_myid_groups(product):
    product_list = get_product_list()
    no_access_req_pdt = ['pgw','udc','upf']
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    if product in no_access_req_pdt:
        flash("%s product is not supported for accommodating MYID Group join request to have GitHub, Sonar and Artifactory access" % product, "danger")
        return redirect('/home')
    if product == 'pcf':
        team_prod = "policy"
    elif product == 'ccg':
            team_prod = "cn"
    elif product == 'cnee':
            team_prod = "infrastructure"
    elif product == 'lfs':
            team_prod = "tools"
    elif product == 'cnvpc':
            team_prod = "vpc"
    elif product == 'ulb':
        team_prod = "lbs-apps"
    elif product == 'smi-nso':
        team_prod = "nso"
    else:
        team_prod = product
    submitter = oidc.user_getfield('uid')
    owner = get_dashboard_role_list(product_org,['ORG_OWNER'])
    org_owner_list = ','.join(owner)
    form = AccessControl_myID()
    github_obj = GithubApi()
    all_repos = github_obj.list_all_repos_names(product_org)
    dev_repos=[]
    qa_repos=[]
    for repo in all_repos:
        if not re.match('.*-cd-pipeline|app-infra|IVT-Regression|k6|cnbng-ft-automation',repo):
            dev_repos.append(repo)
        else:
            qa_repos.append(repo)
    if request.method == "POST":
        userids = form.userid.data # comma seperated list of IDs
        type_id = form.type_id.data
        access_level = form.access_level.data
        justification = form.justification.data.replace('\'','')
        if access_level == 'D':
            if type_id == 'read':
                myidgrp_name = team_prod + '-read-gh'
            if type_id == 'write':
                myidgrp_name = team_prod + '-write-gh'
        elif access_level == 'Q':
            if product_org != 'mobile-cnat-golang-lib':
                for s,d in myid_group_info.qa_owner.items():
                    if s.endswith(team_prod):
                        qa_owner_list=d
                        org_owner_list = ','.join(qa_owner_list)
            else:
               if type_id == 'read':
                   myidgrp_name = team_prod + '-read-gh'
               elif type_id == 'write':
                   myidgrp_name = team_prod + '-app-infra-write-gh'
            if type_id == 'read' and product_org != 'mobile-cnat-golang-lib':
                myidgrp_name = team_prod + '-qa-read-gh'
            if type_id == 'write' and product_org != 'mobile-cnat-golang-lib':
                myidgrp_name = team_prod + '-qa-write-gh'
        if not (access_level and justification and userids and type_id):
            flash("CEC ID, Access level, Access Type and Justification is required. It cannot be empty", "danger")
            return redirect('/join_myid_group/%s' % product)
        if myidgrp_name not in myid_group_info.myidgrp:
            flash("MY ID group information is not available for %s access to %s repos" % (type_id,access_level),"info")
            return redirect('/%s' % product)
        else:
            flash("Join request been placed. Will receive mail on the details. Once your request is approved, the access would be granted ","info")
            for uid in set(userids.split(",")):
                process_myid_join_request(submitter,uid,myidgrp_name,justification,org_owner_list,product,product_org)
            return redirect('/submit_myid_req/%s' % product)
    return render_template('show_myid.html',product_org=product_org,form=form,product=product,dev_repos=dev_repos,qa_repos=qa_repos)

@access_control.route('/join_myid_artifactory/<product>', methods=['GET', 'POST'])
@oidc.require_login
def join_myid_artifactory(product):
    product_list = get_product_list()
    no_access_req_pdt = ['pgw','udc','upf']
    if product in no_access_req_pdt:
        flash("%s product is not supported for accommodating MYID Group join request to have Artifactory access" % product, "danger")
        return redirect('/home')
    if product == 'pcf':
        team_prod = "policy"
    elif product == 'ccg':
            team_prod = "cn"
    elif product == 'cnee':
            team_prod = "infrastructure"
    elif product == 'lfs':
            team_prod = "tools"
    elif product == 'cnvpc':
            team_prod = "vpc"
    elif product == 'ulb':
        team_prod = "lbs-apps"
    elif product == 'smi-nso':
        team_prod = "nso"
    else:
        team_prod = product
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    submitter = oidc.user_getfield('uid')
    owner = get_dashboard_role_list(product_org,['ORG_OWNER'])
    org_owner_list = ','.join(owner)
    form = AccessControl_myID()
    if request.method == "POST":
        userids = form.userid.data # comma seperated list of IDs
        justification = form.justification.data.replace('\'','')
        myidgrp_name = team_prod + "-artifactory-external"
        if not (justification and userids):
            flash("CEC ID and Justification is required. It cannot be empty", "danger")
            return redirect('/join_myid_group/%s' % product)
        flash("Join request been placed. Will receive mail on the details. Once your request is approved, within an hour, the access would be granted ","info")
        for uid in set(userids.split(",")):
            process_myid_join_request(submitter,uid,myidgrp_name,justification,org_owner_list,product,product_org)
            return redirect('/submit_myid_req/%s' % product)
    return render_template('show_myid_artifactory.html',product_org=product_org,form=form,product=product)

@access_control.route('/join_myid_sonar/<product>', methods=['GET', 'POST'])
@oidc.require_login
def join_myid_sonar(product):
    product_list = get_product_list()
    no_access_req_pdt = ['pgw','udc','upf']
    if product in no_access_req_pdt:
        flash("%s product is not supported for accommodating MYID Group join request to have Sonar access" % product, "danger")
        return redirect('/home')
    if product == 'pcf':
        team_prod = "policy"
    elif product == 'ccg':
            team_prod = "cn"
    elif product == 'cnee':
            team_prod = "infrastructure"
    elif product == 'lfs':
            team_prod = "tools"
    elif product == 'cnvpc':
            team_prod = "vpc"
    elif product == 'ulb':
        team_prod = "lbs-apps"
    elif product == 'smi-nso':
        team_prod = "nso"
    else:
        team_prod = product
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    submitter = oidc.user_getfield('uid')
    owner = get_dashboard_role_list(product_org,['ORG_OWNER'])
    org_owner_list = ','.join(owner)
    form = AccessControl_myID()
    if request.method == "POST":
        userids = form.userid.data # comma seperated list of IDs
        justification = form.justification.data.replace('\'','')
        myidgrp_name = team_prod + "-sonar-external"
        if not (justification and userids):
            flash("CEC ID and Justification is required. It cannot be empty", "danger")
            return redirect('/join_myid_group/%s' % product)
        flash("Join request been placed. Will receive mail on the details. Once your request is approved, within an hour, the access would be granted ","info")
        for uid in set(userids.split(",")):
            process_myid_join_request(submitter,uid,myidgrp_name,justification,org_owner_list,product,product_org)
            return redirect('/submit_myid_req/%s' % product)
    return render_template('show_myid_sonar.html',product_org=product_org,form=form,product=product)

@access_control.route('/show_myid_groups/<product>', methods=['GET'])
@oidc.require_login
def show_myid_groups(product):
    product_org = get_product_organization(product)
    no_access_req_pdt = ['pgw','udc','upf']
    if product in no_access_req_pdt:
        flash("%s product is not supported for displaying MYID Group details" % product, "danger")
        return redirect('/home')
    group_info={}
    team_name=[]
    if product == 'pcf':
            team_prod = "policy"
    elif product == 'ccg':
            team_prod = "cn-"
    elif product == 'cnee':
            team_prod = "infrastructure"
    elif product == 'lfs':
            team_prod = "tools"
    elif product == 'cnvpc':
            team_prod = "vpc"
    elif product == 'ulb':
        team_prod = "lbs-apps"
    elif product == 'smi-nso':
        team_prod = "nso"
    else:
        team_prod = product
    github_obj = GithubApi()
    all_repos = github_obj.list_all_repos_names(product_org)
    for k,v in myid_group_info.myidgrp.items():
        if product == 'ccg':
            team_prod = "cn-"
        if k.startswith(team_prod) and (k.endswith("-gh") or k.endswith("-external")) and not 'admins' in k:
            group_info[k]={}
            owner = get_dashboard_role_list(product_org,['ORG_OWNER'])
            org_owner_list = ','.join(owner)
            team_name.append(k)
            if k.endswith('qa-read-gh') or k.endswith('qa-write-gh') or k.endswith('-external'):
                for s,d in myid_group_info.qa_owner.items():
                    if product == 'ccg':
                        team_prod = "cn"
                    if s.endswith(team_prod):
                        qa_owner_list=d
                        org_owner_list = ','.join(qa_owner_list)
                        if k.endswith('-external'):
                            org_owner_list = ','.join(list(set(owner+qa_owner_list)))
            group_info[k]['group_name']=k
            group_info[k]['group_value']=v
            group_info[k]['owner']=org_owner_list
    if group_info == {}:
        flash("MY ID group information is not available" ,"info")
        return redirect('/%s' % product)
    return render_template('show_myid_groups.html', product=product,product_org=product_org,myid_group_info=group_info,team_name=team_name)

@oidc.require_login
def show_approver(product):
    access_role_obj = DASHBOARD_ACCESS()
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    approver_role_list=[]
    for row1 in access_role_obj.query.filter_by(ORG_NAME=product_org).filter(DASHBOARD_ACCESS.ROLE.in_(('ORG_OWNER','RELENG'))):
        dict = row1.__dict__
        approver_role_list.append(dict)
    return render_template('show_access_approvers.html', access_approver_list=approver_role_list, product=product,product_org=product_org)


def validate_user_request(product,product_org,uid,type_id,repos):
    # Checks per user-ID :
    # 1. Valid USER-ID
    # 2. Already part of requested team or not

    github_obj = GithubApi()
    # 1. Valid USER-ID
    is_gh_member = github_obj.is_github_member(uid)
    if is_gh_member == False:
            flash("%s is not yet a Github member. To help you with access, please login to : https://wwwin-github.cisco.com.  For now processing will be skipped for : %s  " % (uid, uid), "info")
            return False

    # 2. Already part of requested team or not
    team_prod = product
    if product == 'pcf':
            team_prod = "policy"
    if product == 'ccg':
            team_prod = "cn"
    if product == 'cnvpc':
            team_prod = "vpc"
    if product == 'ulb':
        team_prod = "lbs-apps"
    if product == 'lbs-libraries':
        team_prod = "lbs-libraries" 
    if product == 'smi-nso':
        team_prod = "nso"
    if type_id == 'read':
            team_member = github_obj.check_user_in_team(product_org, team_prod+"-read", uid)
    elif type_id == 'write':
            if 'app-infra' in repos:
                team_member = github_obj.check_user_in_team(product_org, team_prod+"-app-infra-write", uid)
            else:
                team_member = github_obj.check_user_in_team(product_org, team_prod+"-write", uid)

    if not team_member:
            flash("%s already have %s access to %s. Processing will be skipped for %s" % (uid,type_id,product_org,uid), "info")
            return False

    #flash("Processing user : %s as True " % (uid), "info")
    return True

@access_control.route('/manage_dashboard_access/<product>', methods=['GET', 'POST'])
@oidc.require_login
def manage_dashboard_access(product):
    tt_obj = DASHBOARD_ACCESS()
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    login_user_role,repo_name=get_dashboard_user_role(product_org,oidc.user_getfield('uid'))
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    approver_role_list=[]
    for row1 in tt_obj.query.filter_by(ORG_NAME=product_org):
        dict = row1.__dict__
        approver_role_list.append(dict)
    form = RemoveUser()
    if form.validate_on_submit():
        if form.submit.data:
            user_to_delete = (request.form.getlist(product_org + "_Select"))
            user_list=[]
            for value in user_to_delete:
                user = value.split('__sep__')[0]
                user_list.append(user)
            users=','.join(user_list)
            if login_user_role == "ORG_OWNER" or login_user_role == "RELENG":
                result = remove_users(user_to_delete,product_org)
                if not result:
                    flash("Please select/check users to be removed", "danger")
                    return redirect('/manage_dashboard_access/%s' % product)
                else:
                    flash("Successfully removed the users %s for org - %s from Dashboard Access" % (users,product_org), "success")
                    send_mail_list(product_org,product,user=users,role=None,repos=None,status="Removed")
                return redirect('/manage_dashboard_access/%s' % product)
            else:
                flash("%s does not have previlege to remove an user from %s " % (product_org,login_user_role), "info")
                return redirect('/manage_dashboard_access/%s' % product)
    return render_template('show_org_approvers.html', approver_list=approver_role_list, product=product,product_org=product_org,user_role=login_user_role,form=form)

@access_control.route('/add_user/<product>', methods=['GET', 'POST'])
@oidc.require_login
def add_user(product):
    user_name = oidc.user_getfield('uid')
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    user_role,repo_name=get_dashboard_user_role(product_org,user_name)
    if user_role not in ['RELENG','ORG_OWNER']:
        flash("%s, you are not authorized to add users. Plz contact releng team" % str(user_name), "danger")
        return redirect('/%s' % product)
    form = AddUser()
    userinfo = {}
    role_choices = ['ORG_OWNER', 'DEV_MANAGER' , 'TECH_LEAD']
    if request.method == "POST":
        user = form.user.data
        github_obj = GithubApi()
        is_gh_member = github_obj.is_github_member(user)
        if is_gh_member == False:
            flash("Cannot add user '%s' to the organization. '%s' is not a valid/authorized user. Please reach out to cn-releng-support team  " % (user,user), "danger")
            return redirect('/add_user/%s' % product)

        role,repo_name=get_dashboard_user_role(product_org,user)
        if role == "ORG_OWNER" or role == "RELENG":
            flash("%s already have access control previlege for org: %s " % (user,product_org), "info")
            return redirect('/add_user/%s' % product)
        else:
            role = form.role.data
            select_all_repos = form.select_all_repos.data
            if environ.get("ENVIRONMENT") == "PRODUCTION":
                db_name="5g-prod"
            else:
                db_name="5g-dev"
            if not select_all_repos:
                repos = request.form.getlist("repo_list")
                repo_name = ','.join(repos)
                if role == 'ORG_OWNER':
                    repo_name = "all"
            if select_all_repos:
                repo_name = "all"
            parameter = {
                'DB_NAME' : db_name,
                'SUBMITTER' : user_name,
                'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME'),
                'CEC_ID' : user,
                'PRODUCT_ORG': product_org,
                'PRODUCT':product,
                'ROLE': role,
                'REPO_LIST': repo_name
            }
            if ((role == 'ORG_OWNER' or role == 'DEV_MANAGER' or role == 'TECH_LEAD') and len(repo_name)>1):
                existing_role,existing_repo=get_dashboard_user_role(product_org,user)
                if (existing_role):
                    if (existing_role == role):
                        if (existing_repo == "all" and repo_name != "all"):
                            flash("Cannot overwrite all repos with selective repos for user %s. Kindly remove the user and add again with selective repos" %user, "danger")
                            return redirect('/manage_dashboard_access/%s' % product)
                        if (existing_repo == repo_name):
                            flash("User %s has been assigned same repos already.Kindly check and update repos." % user,"info")
                            return redirect('/manage_dashboard_access/%s' % product)
                    else:
                        flash("ROLE mismatch for updating repos of user %s" % user, "danger")
                        return redirect('/manage_dashboard_access/%s' % product)
                flash("Processing your request of adding new user. Please wait for few mins..", "info")
                trigger_jenkins_build.delay(user_name,product,environ.get('PROCESS_USER_ADDITION_JOB_NAME'),parameter)
                return redirect('/manage_dashboard_access/%s' % product)
            else:
                flash("ROLE/REPOS should be chosen. Cannot be empty", "danger")
                return redirect('/add_user/%s' % product)
    github_obj = GithubApi()
    all_repos = github_obj.list_all_repos_names(product_org)
    return render_template('add_approvers.html', form=form, product=product, product_org=product_org,all_repos=all_repos,role_choices=role_choices,userinfo=userinfo)
