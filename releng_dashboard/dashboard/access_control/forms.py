from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SubmitField, widgets, SelectMultipleField, SelectField, HiddenField, IntegerField, BooleanField,RadioField
from wtforms.validators import DataRequired, Length
from os import environ
from wtforms.widgets import Text<PERSON>rea
from wtforms import validators
from dashboard.tasks import list_to_choices
from GithubApi import GithubA<PERSON>

def role_choices():
    choices = ['ORG_OWNER', 'DEV_MANAGER' , 'TECH_LEAD']
    return choices

def type_choices():
    choices = ['read', 'write']
    return choices

def category_choices():
    choices = ['grant', 'revoke']
    return choices


class AccessControl_form(FlaskForm):
    id = SelectField('id')
    userid = StringField('USERID')
    submitter = StringField('SUBMITTER')
    type_id = SelectField('TYPE_ID', choices=type_choices(), default='read')
    category_id = SelectField('CATEGORY_ID', choices=category_choices(), default='grant')
    select_all_repos = BooleanField('All Repos')
    justification = StringField(label=('Justification/Comments'), widget=TextArea(), validators=[validators.DataRequired(), validators.Length(min=2, max=500, message='Do not use special characters')])
    submit = SubmitField('Submit')
    approve = SubmitField('Approve')
    deny = SubmitField('Deny')
    cancel = SubmitField('Cancel')
    submit_all = SubmitField(label="Show all requests - Pending & Processed")
    submit_active = SubmitField(label="Show only active requests")


class AccessControl_Approve_form(FlaskForm):
    id = HiddenField('id')
    userid = HiddenField('USERID')
    req_submitter = HiddenField('REQ_SUBMITTER')
    type_id = HiddenField('TYPE_ID')
    category_id = HiddenField('CATEGORY_ID')
    approver_list = HiddenField('APPROVER_LIST')
    approver_comment = StringField('approver_comment', widget=TextArea(), validators=[validators.length(max=500)])
    submit = SubmitField('Submit')
    repo = HiddenField('REPO')
    add_info = SubmitField('Add. Info')
    req_info = SubmitField('Req. Info')
    approve = SubmitField('Approve')
    deny = SubmitField('Deny')
    cancel = SubmitField('Cancel')
    submit_all = SubmitField(label="Show all requests")
    submit_active = SubmitField(label="Show only pending requests")

class AccessControl_MYID_Approve_form(FlaskForm):
    id = HiddenField('id')
    myidgrpname = HiddenField('MYID_GROUP_NAME')
    userid = HiddenField('USERID')
    approve = SubmitField('Approve')
    approver_comment = StringField('approver_comment', widget=TextArea(), validators=[validators.DataRequired(), validators.Length(min=2, max=500, message='Do not use special characters')])
    deny = SubmitField('Deny')
    submit = SubmitField('Submit')
    submit_all = SubmitField(label="Show all requests")
    submit_active = SubmitField(label="Show only pending requests")

class RemoveUser(FlaskForm):
    delete = SubmitField('Remove User')
    submit = SubmitField('Submit')

class AddUser(FlaskForm):
    user = StringField('CEC_ID',[DataRequired()])
    role = SelectField('ROLE', choices=role_choices())
    submit = SubmitField('Submit')
    select_all_repos = BooleanField('All Repos')

class AccessControl_myID(FlaskForm):
    id = SelectField('id')
    userid = StringField('USERID')
    submitter = StringField('SUBMITTER')
    access_level = RadioField('access_level',choices=[('D','Dev'),('Q','QA')])
    type_id = SelectField('TYPE_ID', choices=type_choices(), default='read')
    justification = StringField(label=('Justification/Comments'), widget=TextArea(), validators=[validators.DataRequired(), validators.Length(min=2, max=500, message='Do not use special characters')])
    submit = SubmitField('Submit')