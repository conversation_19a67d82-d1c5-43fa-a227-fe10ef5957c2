from .. import db

class ACCESS_REQUESTS(db.Model):
    __tablename__ = 'ACCESS_REQUESTS'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    PRODUCT_NAME = db.Column(db.String(400), primary_key=True)
    USERID = db.Column(db.String(100), primary_key=True)
    GRP_ID = db.Column(db.String(20))
    TYPE_ID =  db.Column(db.String(20))
    CATEGORY_ID =  db.Column(db.String(20))
    STATUS = db.Column(db.String(100))
    APPROVER_COMMENT = db.Column(db.String(1000))
    APPROVER = db.Column(db.String(100))
    SUBMIT_DATE = db.Column(db.Date)
    JUSTIFICATION = db.Column(db.String(1000))
    SUBMITTER = db.Column(db.String(100))
    REPO = db.Column(db.String(100))

class ACCESS_APPROVERS(db.Model):
    __tablename__ = 'ACCESS_APPROVERS'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    PRODUCT_NAME = db.Column(db.String(200), primary_key=True)
    GRP_ID = db.Column(db.String(20))
    PRIM_OWNER = db.Column(db.String(100))
    SEC_OWNER = db.Column(db.String(100))
    
class GITHUB_ACCESS_OWNERS(db.Model):
    __tablename__ = 'GITHUB_ACCESS_OWNERS'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    ORG_NAME = db.Column(db.String(200))
    REPO_NAME = db.Column(db.String(100))
    OWNER = db.Column(db.String(100))

class GITHUB_ACCESS_INFO(db.Model):
    __tablename__ = 'GITHUB_ACCESS_INFO'
    id = db.Column(db.Integer,primary_key=True, autoincrement=True)
    REQ_ID = db.Column(db.Integer)
    TIME = db.Column(db.Integer)
    USER_ID = db.Column(db.String(30))
    NOTE = db.Column(db.String(1000))

class MYID_ACCESS_REQUESTS(db.Model):
    __tablename__ = 'MYID_ACCESS_REQUESTS'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    PRODUCT_NAME = db.Column(db.String(400), primary_key=True)
    USERID = db.Column(db.String(100), primary_key=True)
    MYID_GRP_NAME = db.Column(db.String(100))
    APPROVER = db.Column(db.String(100))
    APPROVER_COMMENT = db.Column(db.String(1000))
    SUBMIT_DATE = db.Column(db.Date)
    SUBMITTER = db.Column(db.String(100))
    JUSTIFICATION = db.Column(db.String(1000))
    APPROVE_LINK = db.Column(db.String(600))
    STATUS = db.Column(db.String(100))