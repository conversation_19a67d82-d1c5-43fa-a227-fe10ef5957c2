import os
import sys
import time
import re
from datetime import datetime
from pprint import pprint
from flask import flash, redirect
from flask_mail import Message
from .models import ACCESS_REQUESTS, GITHUB_ACCESS_OWNERS, ACCESS_APPROVERS, GITHUB_ACCESS_INFO,MYID_ACCESS_REQUESTS
from .. import mail, db
from os import environ
from .. import cache
from dashboard.build.tasks import trigger_jenkins_build
from sqlalchemy import and_
from dashboard.utils.common import get_mailer_user_list
import sys, os
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../promotion/")
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../scripts/github/")
from Bot import bot_send_message
from github_add_user import update_access_request_db
from dashboard.models import DASHBOARD_ACCESS
from dashboard.tasks import get_dashboard_role_list
from dashboard import oidc
import myid_group_info as myid_group_info
from MyIDGroup import MyIDGroup
import Constants as Constants

def create_new_access_request(usernames, product, product_org, justification, action, category,submitter, repos=None):

    if not justification:
        flash('You need to mention a valid justification.', "info")
        return

    try:
           flash("Creating access request for users: %s. Plz wait for few mins..." %(usernames), "info")
           if environ.get("ENVIRONMENT") == "PRODUCTION":
              db_name="5g-prod"
           else:
              db_name="5g-dev"
           parameter = {
                    'USER': usernames,
                    'ORG':product_org,
                    'ACTION_TYPE': action,
                    'STATUS':"Pending",
                    'CATEGORY': category,
                    'JUSTIFICATION': justification,
                    'SUBMITTER' : submitter,
                    'DB_NAME' : db_name,
                    'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME')
                    }
           if repos:
                   parameter['REPOS'] = ','.join(repos)
           t = trigger_jenkins_build.delay(usernames, product, environ.get('ADD_ACCESS_REQ_JOB_NAME'),parameter)

    except Exception as e:
           flash("Caught an excpetion: %s" % e, "danger")


def get_access_owners(product_org, all=True):
    approver_list = []
    access_approver_obj=GITHUB_ACCESS_OWNERS()
    result = []
    for row in access_approver_obj.query.filter_by(ORG_NAME=product_org):
        result.append(row.__dict__)

    for approver in result:
        approver_list.append(approver['OWNER'])

    approver_list=list(dict.fromkeys(approver_list))
    return approver_list


def process_access_requests(username, product, product_org, user, req_submitter, type_id, approver_comment, id, action,category,approver_list, repos=None):
    #flash("Welcome to process_access_requests for user: %s, action:%s , id=%s, PRODUCT_NAME=%s, req_submitter=%s, TYPE_ID=%s, APPROVER=%s, APPROVER_COMMENT=%s, approver_list=%s " %(user, action, id, product, req_submitter, type_id, username, approver_comment, approver_list), "info")
    info_process = False
    if action in ["Add_Info","Req_Info"]:
        info_process = True
        process_info_action(username, product, product_org, user, type_id, approver_comment, id, action,category,info_process, repos)
        #Need to find action type.
        mail_dic, bot_email_str, bot_msg = update_access_request_db("5g-prod", user, req_submitter, product, product_org, '', type_id, approver_comment, action, username, approver_comment, False, username, False, approver_list, repos)
        try:
            for mailer in set(bot_email_str.split(",")):
                bot_send_message(email=mailer, message=bot_msg)
        except Exception as e:
            print("Exception while sending bot message ", e)
        sender       = mail_dic["sender"]  
        to_list      = mail_dic["to_list"].replace(",", "@cisco.com,")+"@cisco.com"  
        cc_list      = mail_dic["cc_list"].replace(",", "@cisco.com,")+"@cisco.com"
        for aprvr in approver_list.split(','):
            if aprvr in to_list or aprvr in cc_list:
                continue
            cc_list += ','+aprvr + '@cisco.com'
        #to_list      = '<EMAIL>'
        #cc_list      = '<EMAIL>'
        subject      = mail_dic["subject"]
        html_content = mail_dic["html_content"]
        msg = Message(subject=subject, sender=sender, cc=cc_list.split(","), recipients=to_list.split(","))
        # Select products throttle approvers as recipients
        msg.html = html_content
        mail.send(msg)
        return;
    
    process_info_action(username, product, product_org, user, type_id, approver_comment, id, action,category,info_process, repos)   #Adding an info object to capture approver comments 
    access_obj = ACCESS_REQUESTS()
    try:
        access_obj.query.filter_by(id=id,PRODUCT_NAME=product,GRP_ID='github', TYPE_ID=type_id, CATEGORY_ID=category).update(dict(STATUS=action ,APPROVER=username,APPROVER_COMMENT=approver_comment))
        db.session.commit()

        flash("Successfully updated access request", "success")
    except Exception as e:
        flash("Unable to write to DB %s  " %(e), "danger")
        db.session.rollback()
        flash("Failed to update access request", "error")
    finally:
        db.session.close()

    read_org = ""
    write_org = ""
    if type_id == 'read':
            read_org = product_org
    else:
            write_org = product_org

                    #flash("form.approver_comment.data=%s, form.type_id.data=%s , read_org: %s , write_org=%s " % (form.approver_comment.data,form.type_id.data,read_org,write_org), "info")
    if environ.get("ENVIRONMENT") == "PRODUCTION":
              db_name="5g-prod"
    else:
              db_name="5g-dev"
    parameter = {'USER_LIST': user,
                         'READ_ONLY_ORGS': read_org,
                         'WRITE_ORGS' : write_org,
                         'CATEGORY_ID' : category,
                         'APPROVER' : username,
                         'APPROVER_COMMENT': approver_comment,
                         'STATUS' : action,
                         'DB_NAME' : db_name,
                         'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME')
    }
    if repos:
        parameter['REPOS'] = repos
    try:
                            #flash("Processing request for user: %s. Plz wait for few mins..." %(user), "info")
                            # Update github teams
                            trigger_jenkins_build.delay(str(username), product, environ.get('PROCESS_ACCESS_REQ_JOB_NAME'),parameter)
    except Exception as e:
                            flash("Caught an excpetion: %s" % e, "danger")

def process_myid_join_request(submitter,userid,myidgrp_name,justification,org_owner_list,product,product_org):
    if environ.get("ENVIRONMENT") == "PRODUCTION":
        db_name="5g-prod"
    else:
        db_name="5g-dev"
    parameter = {'CEC_ID': userid,
                 'SUBMITTER':submitter,
                 'ORG_NAME':product_org,
                 'MYID_GROUP_NAME':myidgrp_name,
                 'JOIN_GROUP': True,
                 'JUSTIFICATION':justification,
                 'ORG_OWNER':org_owner_list,
                 'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME'),
                 'DB_NAME': db_name
        }
    try:
                            #flash("Processing request for user: %s. Plz wait for few mins..." %(user), "info")
                            # Update github teams
                            trigger_jenkins_build.delay(str(submitter), product, environ.get('PROCESS_MYID_ACCESS_REQ_JOB_NAME'),parameter)
    except Exception as e:
                            flash("Caught an excpetion: %s" % e, "danger")

def process_myid_req(product,product_org,id,myidgrpname,userid,action,approver_comment,submitter):
    myid_joinreq_obj = MYID_ACCESS_REQUESTS()
    for row1 in myid_joinreq_obj.query.filter_by(PRODUCT_NAME=product,MYID_GRP_NAME=myidgrpname,USERID=userid,id=id):
        dict = row1.__dict__
        request_link=dict['APPROVE_LINK']

    try:
       flash("Processing access request for user: %s. Plz wait for few mins..." %(userid), "info")
       if environ.get("ENVIRONMENT") == "PRODUCTION":
          db_name="5g-prod"
       else:
          db_name="5g-dev"
       parameter = {
                'REQUEST_ID':id,
                'CEC_ID': userid,
                'PRODUCT': product,
                'ORG_NAME':product_org,
                'ACTION': action,
                'MYID_GROUP_NAME':myidgrpname,
                'APPROVER_COMMENT': approver_comment,
                'APPROVER': submitter,
                'APPROVE_LINK': request_link,
                'DB_NAME' : db_name,
                'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME')
                }
       t = trigger_jenkins_build.delay(userid, product, environ.get('GITHUB_ACCESS_REQ_PROCESS_JOB'),parameter)

    except Exception as e:
           flash("Caught an excpetion: %s" % e, "danger")

def process_info_action(username, product, product_org, user, type_id, approver_comment, id, action,category, info_process, repos=None):
    next_action_map  = {"Add_Info":"Pending","Req_Info":"Need Info"}
    action_desc      = {"Add_Info":"Provided more info","Req_Info":"Need more info/justification"}

    info_obj         = GITHUB_ACCESS_INFO()
    info_obj.REQ_ID  = id
    info_obj.TIME    = int(time.time())
    info_obj.USER_ID = username
    info_obj.NOTE    = approver_comment
    access_obj       = ACCESS_REQUESTS()
    #req_submitter    = ''
    #req_category_id  = ''
    try:
        #State change for the Request
        if info_process:            #True only in the case of "Add_Info" and "Need Info". Other cases jenkins job will take care of state changes.
            access_obj.query.filter_by(id=id,PRODUCT_NAME=product,GRP_ID='github', TYPE_ID=type_id, CATEGORY_ID=category).update(dict(STATUS=next_action_map[action])) #, APPROVER=username, APPROVER_COMMENT="{u}: {d}".format(u=user, d=action_desc[action])))
            #category_id   = access_obj.category_id
            #req_submitter = access_obj.submitter

        #Adding more info to the db for a request.
        db.session.add(info_obj)
        
        db.session.commit()

        flash("Successfully updated access request", "success")
    except Exception as e:
        flash("Unable to write to DB %s  " %(e), "danger")
        db.session.rollback()
        flash("Failed to update access request", "error")
    finally:
        db.session.close()

def get_access_role(username,product_org):
    # compare username with PRIM & SEC OWNERS of that org and return role accordingly
    role = "viewer"
    ret=None
    cn_releng_users=get_mailer_user_list("cn-releng-support")
    cn_releng_users.append("banandya")
    if username in cn_releng_users:
            role="releng"
            return role

    approver_list = []
    access_approver_obj=GITHUB_ACCESS_OWNERS()
    result = []
    for row in access_approver_obj.query.filter_by(ORG_NAME=product_org):
        approver_list.append(row.__dict__)

    for approver in approver_list:
        if approver['OWNER'] == username:
            ret='approver'

    # Check approver in ACCESS_REQUESTS table bz READ requests have DE's manager as approver
    if ret == None:
        #flash("No matching approver record found for user %s" % username , "error")
        role="viewer"
    else:
        #flash("Matching approver record found for user %s" % username , "info")
        role="approver"

    return role

def remove_users(user_to_delete,product_org):
    if not user_to_delete:
        return
    for value in user_to_delete:
        approver = value.split('__sep__')[0]
        role = value.split('__sep__')[1]
        if re.match('RELENG', role):
            # Don't allow user to delete ORG_OWNER or RELENG from Approver list.
            print("You can't delete ORG_OWNER or RELENG from access control list. Plz reach out to "
                  "<EMAIL>", "danger")
            return None
        d_access_obj = DASHBOARD_ACCESS()
        d_access_obj.query.filter_by(ORG_NAME=product_org, CEC_ID=approver, ROLE=role).delete()
        if role == 'ORG_OWNER':
            for k,v in Constants.PRODUCT_LIST.items():
                if v == product_org:
                    prd_info = k
            team_prod = prd_info
            if prd_info == 'pcf':
                team_prod = "policy"
            if prd_info == 'ccg':
                team_prod = "cn"
            if prd_info == 'cnvpc':
                team_prod = "vpc"
            if prd_info == 'ulb':
                team_prod = "lbs-apps"
            if prd_info == 'lbs-libraries':
                team_prod = "lbs-libraries"
            myid_obj = MyIDGroup()
            access_token = myid_obj.myid_refresh_token_2()
            for k,v in myid_group_info.myidgrp.items():
                if k.startswith(team_prod) and "qa" not in k:
                   owner_list = myid_obj.getowneruser(access_token,k)
                   if approver in owner_list:
                       result=myid_obj.remowneruser(access_token,k,approver)
                       if result == 0:
                           print("Owner %s has been successfully removed from the MyID Group:%s" %(approver,k))
                   else:
                       print("ERROR: User %s is not an existing owner of the MyID Group %s and hence the user cannot be removed" %(approver,k))
        try:
            db.session.commit()
            exitvalue = 1
        except Exception as e:
            pprint("unable to delete the user from DB %s" % e)
            db.session.rollback()
            return None
        finally:
            db.session.close()
    return exitvalue

def send_mail_list(product_org,product,user,role,repos,status):
    head = '''<head>
       <style>
       table, td, th {
       border-collapse: collapse;
       border: 1px solid black;
       text-align: left;
       padding: 6px;
       }
      </style>
      </head>'''
    # Send email
    submitter = oidc.user_getfield('uid')
    if status == "Approved":
        subject = "New user %s has been added successfully to the Organization %s" % (user,product_org)
        body = ('<body> New user %s has been successfully added to Dashboard Access Management of the Organization - %s. <br><br> Please log in to 5G Dashboard to view the users <br> Dashboard Link: https://cn-rel-dash-lnx.cisco.com/manage_dashboard_access/%s' % (user,product_org,product))
        body += ('<br><br>Submitted by: %s <br> New user added  : %s <br> Github Organization  : %s <br> ' \
                 'Product : %s <br> Repo(s)  : %s <br> Role  : %s <br>' % (submitter,user,product_org,product,repos,role))

    else:
        subject = "User(s) %s successfully removed from the Organization %s" % (user,product_org)
        body = ('<body> User(s) %s successfully removed from Dashboard Access Management of the Organization - %s. <br><br> Please log in to 5G Dashboard to view the users <br> Dashboard Link: https://cn-rel-dash-lnx.cisco.com/manage_dashboard_access/%s' % (user,product_org,product))
        body += ('<br><br>Submitted by: %s <br> User(s) Removed  : %s <br> Github Organization  : %s <br> ' \
                 'Product : %s <br> Repo(s)  : %s <br> Role  : %s <br>' % (submitter,user,product_org,product,repos,role))
        # bot content (bot_con)
        bot_con ='User(s) %s successfully removed from Dashboard Access Management of the Organization - %s. \n\n Please log in to 5G Dashboard to view the users \n Dashboard Link: https://cn-rel-dash-lnx.cisco.com/manage_dashboard_access/%s' % (user,product_org,product)
        bot_con +='\n Submitted by: %s \n User(s) Removed  : %s \n Github Organization  : %s \n ' \
                  'Product : %s \n Repo(s)  : %s \n Role  : %s \n' % (submitter,user,product_org,product,repos,role)
        bot_con +='\n\nThanks,\nCN Releng Team'

    body += "<br>Thanks,<br>CN-Releng-Support</body>"
    content = head + body
    msg = Message(subject=subject, sender="<EMAIL>")
    # Select product owners as recipients
    recipients_list = get_dashboard_role_list(product_org,["ORG_OWNER","DEV_MANAGER","RELENG"])
    recipients_list.append(submitter)
    recipients_list.append(user)
    msg.recipients = recipients_list
    msg.html = content
    mail.send(msg)
    recipients = [x + "@cisco.com" for x in recipients_list]
    bot_send_list = list(set(recipients))

    try:
        for mailer in bot_send_list:
            bot_send_message(email=mailer, message=bot_con)
    except Exception as e:
        print("Exception while sending bot message ", e)
    return