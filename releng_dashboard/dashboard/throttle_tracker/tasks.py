import os
import sys
import time
from pprint import pprint
from flask import flash, redirect
from flask_mail import Message

from .models import TT_CDETS_DETAILS, TT_USERS
from dashboard.models import TT_Role,DASHBOARD_ACCESS
from dashboard.tasks import get_dashboard_role_list
from .. import mail, db
from os import environ
from .. import cache
import requests
import json
from requests.auth import HTTPBasic<PERSON>uth
from GithubApi import GithubApi
from dashboard.utils.cnjenkins import CN<PERSON>enkins
from dashboard.github_ops.models import PullRequest
import Constants as Constants
import time
import datetime
from humanfriendly import format_timespan
from dashboard.utils.common import get_mailer_user_list
from dashboard.build.tasks import trigger_jenkins_build
from os import environ

sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
from Bot import bot_send_message
#from GithubApi import GithubApi
#room_id = environ.get("room_id")

def send_email_notifiation(username, CDETS, product_org, branch, cdet_comment, product):
    email = username + "@cisco.com"
    mark = "================================="
    tt_obj1 = TT_CDETS_DETAILS()
    try:
        row = tt_obj1.query.filter_by(products_organization=product_org, cdets_id=CDETS, branches_branch_name=branch).first()
        data = row.__dict__
        TT_id = data["id"]
        submitter = data["submitter_cec_id"]
    except Exception as e:
        flash("Failed to send email notification. \n <NAME_EMAIL> if issue persists. ", "danger")
        return redirect('/throttle_tracker_home/%s' % product)
    head = '''<head>
       <style>
       table, td, th {
       border-collapse: collapse;
       border: 1px solid black;
       text-align: left;
       padding: 6px;
       }
      </style>
      </head>'''
    # Send email
    if data["approval_status"] == "Pending":
        subject = "Throttle request #%s has been submitted successfully" % TT_id
        body = ('<body> A request has been submitted for review for CDETS ID - %s. <br><br> Please log in to 5G Dashboard and Approve or Deny the request. <br> Dashboard Link: https://cn-rel-dash-lnx.cisco.com/throttle_tracker_home/%s' % (CDETS,product))
        body += ('<br><br>Submitted by: %s <br> Thrrotle Request ID  : %s <br> CDETS ID  : %s <br> Github Organization  : %s <br> ' \
                    'Branch  : %s <br> Approval Status  : Pending <br> Comments : %s <br>' % (username,str(TT_id),CDETS,product_org,branch,cdet_comment))

    else:
        subject = "Throttle request #%s has been updated successfully" % TT_id
        body = ('<body> A request has been updated for CDETS ID - %s. <br> <br> Please log in to 5G Dashboard for more details. <br> Dashboard Link: https://cn-rel-dash-lnx.cisco.com/throttle_tracker_home/%s' % (CDETS,product))
        body += ('<br><br>Updated by: %s <br> Thrrotle Request ID  : %s <br> CDETS ID  : %s <br> Github Organization  : %s <br> ' \
            'Branch  : %s <br> Approval Status  : %s <br> Comments : %s <br>' % (username,str(TT_id),CDETS,product_org,branch,data["approval_status"],cdet_comment))

    body += "<br>Thanks,<br>CN-Releng-Support</body>"
    content = head + body
    msg = Message(subject=subject, sender="<EMAIL>")
    # Select products throttle approvers as recipients
    recipients_list = get_dashboard_role_list(product_org,["ORG_OWNER","DEV_MANAGER"])
    recipients_list.append(username)
    recipients_list.append(submitter)
    msg.recipients = recipients_list
    msg.html = content
    mail.send(msg)
    return


def validate_throttle_request(username, product, product_org, branch, CDETS):
    parameter= {"CDETS_ID" : CDETS,
                "BRANCH" : branch,
                "ORG" : product_org}
    trigger_jenkins_build.delay(username, product, environ.get('THROTTLE_CDETS_VALIDATION_JOB_NAME'),
                                parameter)

def create_new_throttle_request(username, product, product_org, branch, CDETS, cdet_comment):
    #github_obj = GithubApi()
    #if not CDET:
    #    flash('You need to mention CDET ID.', "info")
    #    return
    tt_obj = TT_CDETS_DETAILS()
    #if tt_obj.query.filter_by(branches_branch_name=branch,products_organization=product_org,cdets_id=CDETS):
    #    flash("Throttle request already exists for org: %s, branch: %s, CDETs ID: %s" %(product_org,branch,CDETS), "warning")
    #    return None
    CDETS = CDETS.strip()
    tt_obj.cdets_id = CDETS
    tt_obj.products_organization = product_org
    tt_obj.submitter_cec_id = username
    tt_obj.approval_status = "Validating"
    tt_obj.branches_branch_name = branch
    tt_obj.cdets_comment = cdet_comment
    tt_obj.submission_date = datetime.datetime.today().strftime('%d %b %Y')
    try:
        db.session.add(tt_obj)
        db.session.commit()
        flash("Successfully submitted a throttle request", "success")
    except Exception as e:
        pprint("Unable to write to DB %s" % e)
        db.session.rollback()
        flash("Failed to submit a throttle request. Please check whether this is a duplicate throttle request. \n <NAME_EMAIL> if issue persists. ", "danger")
        return None
    finally:
        db.session.close()
    validate_throttle_request(username, product, product_org, branch, CDETS)
    send_email_notifiation(username, CDETS, product_org, branch, cdet_comment, product)

def get_tt_approvers(product_org):
    tt_role_obj = TT_Role()
    role="approver"
    user_info = []
    for row in tt_role_obj.query.filter_by(role=role,products_organization=product_org):
        user_info.append(row.__dict__)
    user_list = []
    for info in user_info:
        user_list.append(info['users_cec_id'])
    return user_list

def get_tt_role(username,product_org):
    tt_role_obj = TT_Role()
    user_info = []
    role = "viewer"
    for row in tt_role_obj.query.filter_by(users_cec_id=username,products_organization=product_org):
        user_info.append(row.__dict__)
    for info in user_info:
        role = info['role']

    cn_releng_users=get_mailer_user_list("cn-releng-support")
    if username in cn_releng_users:
        role="releng"
    
    return role

def update_pull_request(product_org,branch,cdet):
    pr_obj = PullRequest()
    pr_info = []
    for row in pr_obj.query.filter_by(organization=product_org,pr_to_branch=branch):
        data = row.__dict__
        if cdet in str(data['cdets']):
            pr_info.append(data['pr_url'])
    jenkins_obj = CNJenkins(server_url=environ.get('JENKINS_SERVER'), username=environ.get('JENKINS_USERNAME'),
                        token=environ.get('JENKINS_TOKEN'))
    for pr_url in pr_info:
        #Trigger throttle tracker job for the PR
        parameter = {}
        parameter['PULL_REQUEST_URL'] = pr_url
        jenkins_obj.build_job(environ.get('THROTTLE_APPROVAL_JOB_NAME'),parameter)

def approve_throttle_requests(username, product, product_org, branch, cdet, action, cdets_comment, id):
    tt_obj = TT_CDETS_DETAILS()
    try:
        tt_obj.query.filter_by(products_organization=product_org,
                               cdets_id=cdet,
                               branches_branch_name=branch).update(dict(approval_status=action,
                                                                        approval_1_cec_id=username,
                                                                        error_msg=cdets_comment,
                                                                        approval_date=datetime.datetime.today().strftime('%d %b %Y %H:%M:%S')))
        db.session.commit()
        flash("successfully updated a throttle request", "success")
        result = True
    except Exception as e:
        pprint("Unable to write to DB %s" % e)
        db.session.rollback()
        flash("Failed to update throttle request or a pull request containing CDETs id associated with throttle request", "failure")
        result = None
    finally:
        db.session.close()
    # Update pull request
    if result:
        try:
            update_pull_request(product_org,branch,cdet)
        except Exception as e:
            pprint("Unable to update pull request %s" % e)
    send_email_notifiation(username, cdet, product_org, branch, cdets_comment, product)

def renew_throttle_requests(username, product, product_org, branch, cdet, cdets_comment, id):
    tt_obj = TT_CDETS_DETAILS()
    try:
        tt_obj.query.filter_by(products_organization=product_org,
                               cdets_id=cdet,
                               branches_branch_name=branch).update(dict(approval_status="Pending",
                                                                        approval_1_cec_id=None,
                                                                        error_msg=None,
                                                                        submission_date=datetime.datetime.today().strftime('%d %b %Y %H:%M:%S')))
        db.session.commit()
        flash("successfully renewed the throttle request", "success")
        result = True
    except Exception as e:
        pprint("Unable to write to DB %s" % e)
        db.session.rollback()
        flash("Failed to update throttle request or a pull request containing CDETs id associated with throttle request", "failure")
        result = None
    finally:
        db.session.close()
    send_email_notifiation(username, cdet, product_org, branch, cdets_comment, product)

def get_throttle_requests(product_org, cdets_id=None):
    tt_obj = TT_CDETS_DETAILS()
    cdets_tmp = []
    rows = []
    current_time = datetime.datetime.today()
    if cdets_id:
        for row in tt_obj.query.filter_by(products_organization=product_org,cdets_id=cdets_id).order_by(TT_CDETS_DETAILS.id.desc()).all():
            rows.append(row.__dict__)
    else:
        for row in tt_obj.query.filter_by(products_organization=product_org).order_by(TT_CDETS_DETAILS.id.desc()).limit(100).all():
            rows.append(row.__dict__)
    for data in rows:
        if data['approval_status'] == "Approved" and data['approval_date']:
            approval_date_obj =  datetime.datetime.strptime(data['approval_date'], "%d %b %Y %H:%M:%S")
            expiring_date_obj = approval_date_obj + datetime.timedelta(days=1)
            diff_obj = expiring_date_obj - current_time
            if diff_obj.days >= 0:
                data['expiring_in'] = format_timespan(diff_obj.seconds)
            else:
                data['approval_status'] = "Expired"
        cdets_tmp.append(data)
    return(cdets_tmp)
