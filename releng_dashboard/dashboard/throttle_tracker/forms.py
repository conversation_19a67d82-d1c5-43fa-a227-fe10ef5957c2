from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField, widgets, SelectMultipleField, SelectField, HiddenField, Integer<PERSON>ield
from wtforms.validators import DataRequired, Length
from os import environ
from wtforms.widgets import TextArea
from wtforms import validators
from dashboard.tasks import list_to_choices


class Throttle_form(FlaskForm):
    branches = []
    actions = []
    branch_name = SelectField('Branch_Name', choices=list_to_choices(branches), default='main')
    cdet = StringField('CDETS',[DataRequired()])
    cdet_comment = StringField('Justification/Comments', widget=TextArea(), validators=[validators.length(max=500)])
    submit = SubmitField('Submit')
    products_organization = StringField('products_organization',[DataRequired()])
    user = StringField('user',[DataRequired()])
    role = StringField('role',[DataRequired()])

class Approve_form(FlaskForm):
    submit = SubmitField('Submit')
    approve = SubmitField('Approve')
    deny = SubmitField('Deny')
    cancel = SubmitField('Cancel')
    renew = SubmitField('Renew')
    revalidate = SubmitField('Revalidate')
    branch_name =  HiddenField()
    cdet =  HiddenField()
    id =  HiddenField()
    cdets_comment = StringField('cdets_comment', widget=TextArea(), validators=[validators.length(max=500)])
    search = SubmitField(label="Search CDETs")
    submit_latest = SubmitField(label="Show latest requests")
    input = StringField('CDETS')
    
