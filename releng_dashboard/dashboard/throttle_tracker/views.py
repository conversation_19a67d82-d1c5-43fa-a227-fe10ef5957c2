from flask import render_template, Blueprint, flash, redirect, request
from .models import TT_CDETS_DETAILS
from dashboard.tasks import get_product_list, get_product_organization, get_tt_branch_list,get_dashboard_user_role,get_dashboard_role_list
throttle_tracker = Blueprint('throttle_tracker', __name__)
from dashboard import oidc
from dashboard.models import DASHBOARD_ACCESS
from .forms import Throttle_form, list_to_choices, Approve_form
from .tasks import get_throttle_requests,create_new_throttle_request, approve_throttle_requests, get_tt_role, renew_throttle_requests, validate_throttle_request


@throttle_tracker.route('/throttle_tracker_home/<product>', methods=['GET', 'POST'])
@oidc.require_login
def throttle_tracker_home(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')

    user_role,repo_name=get_dashboard_user_role(product_org,oidc.user_getfield('uid'))
    form = Approve_form()
    search = False
    cdets_tmp = []
    cdets_id = ""

    if form.validate_on_submit():
        action = ""
        if form.approve.data:
            action = "Approved"
        elif form.deny.data:
            action = "Denied"
        elif form.cancel.data:
            action = "Cancelled"
        if action in ("Approved", "Denied", "Cancelled"):
            result = approve_throttle_requests(str(oidc.user_getfield('uid')), product, product_org, form.branch_name.data, form.cdet.data, action, form.cdets_comment.data, form.id.data)
        
        if form.renew.data:
            renew_throttle_requests(str(oidc.user_getfield('uid')), product, product_org, form.branch_name.data, form.cdet.data, form.cdets_comment.data, form.id.data)

        if form.revalidate.data:
            validate_throttle_request(str(oidc.user_getfield('uid')), product, product_org, form.branch_name.data, form.cdet.data)

        if form.search.data:
            search = True
            if form.input.data:
                cdets_id = form.input.data
            cdets_tmp = get_throttle_requests(product_org,cdets_id)
        else:
            return redirect('/throttle_tracker_home/%s' % product) 
    else:
        cdets_tmp = get_throttle_requests(product_org)
    return render_template('throttle_tracker_home.html', cdet_list=cdets_tmp, product_org=product_org, product=product, form=form, user_role=user_role,search=search,cdets_id=cdets_id)

@throttle_tracker.route('/approve_throttle_request/<product>',  methods=['GET', 'POST'])
@oidc.require_login
def approve_throttle_request(product):
    product_list = get_product_list()
    username = oidc.user_getfield('uid')
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    tt_obj = TT_CDETS_DETAILS()
    user_role,repo_name=get_dashboard_user_role(product_org,oidc.user_getfield('uid'))
    cdets_tmp = []
    for row1 in tt_obj.query.filter_by(approval_status='Pending', products_organization=product_org):
        dict = row1.__dict__
        cdets_tmp.append(dict)

    #tt_obj = TT_CDETS_DETAILS()
    form = Approve_form()
    if form.validate_on_submit():
        if form.approve.data:
            action = "Approved"
        elif form.deny.data:
            action = "Denied"
        else:
            action = "Cancelled"

        result = approve_throttle_requests(str(oidc.user_getfield('uid')), product, product_org, form.branch_name.data, form.cdet.data, action, form.cdets_comment.data, form.id.data)
        return redirect('/throttle_tracker_home/%s' % product)
    return render_template('approve_throttle_request.html', cdet_list=cdets_tmp, product_org=product_org, product=product, form=form, user_role=user_role)

@throttle_tracker.route('/create_throttle_request/<product>', methods=['GET', 'POST'])
@oidc.require_login
def create_throttle_request(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    #tt_obj = TT_CDETS_DETAILS()
    form = Throttle_form()
    branches = get_tt_branch_list(product)
    form.branch_name.choices = list_to_choices(branches)
    if request.method == "POST":
        branch = form.branch_name.data
        cdet = form.cdet.data
        comments = form.cdet_comment.data
        #cdet_check = is_cdets_valid_api(cdet)
        #if cdet_check:
        create_new_throttle_request(str(oidc.user_getfield('uid')), product, product_org, branch, cdet, comments)
        return redirect('/throttle_tracker_home/%s' % product)

        #else:
        #     flash("Please enter valid CDETS ID", "danger")
        #     return redirect('/throttle_tracker_home/%s' % product)
        #if not result:
        #    flash("failed to submit a throttle request", "danger")
    return render_template('submit_throttle_request.html', form=form, product=product, branches=branches, product_org=product_org)

@throttle_tracker.route('/show_approver/<product>', methods=['GET', 'POST'])
@oidc.require_login
def show_approver(product):
    product_list = get_product_list()
    #tt_role_obj = TT_Role()
    tt_role_obj = DASHBOARD_ACCESS()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    login_user_role,repo_name=get_dashboard_user_role(product_org,oidc.user_getfield('uid'))
    approver_role_list=[]
    for row1 in tt_role_obj.query.filter_by(ORG_NAME=product_org).filter(DASHBOARD_ACCESS.ROLE.in_(('ORG_OWNER','RELENG','DEV_MANAGER'))):
        dict = row1.__dict__
        approver_role_list.append(dict)
    return render_template('show_tt_approvers.html', tt_approver_list=approver_role_list, product=product,product_org=product_org,user_role=login_user_role)
