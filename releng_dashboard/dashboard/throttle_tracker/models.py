from .. import db

class TT_CDETS_DETAILS(db.Model):
    __tablename__ = 'TT_CDETS_DETAILS'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    cdets_id = db.Column(db.String(200), primary_key=True)
    products_organization = db.Column(db.String(400), primary_key=True)
    branches_branch_name = db.Column(db.String(200), primary_key=True)
    approval_status = db.Column(db.String(200))
    submitter_cec_id = db.Column(db.String(200))
    cdets_comment = db.Column(db.String(400))
    approval_1_cec_id = db.Column(db.String(300))
    submission_date = db.Column(db.String(200))
    approval_date = db.Column(db.String(200))
    error_msg = db.Column(db.String(1000))
    submission_date = db.Column(db.String(200), default=None)

class TT_USERS(db.Model):
    __tablename__ = 'TT_USERS'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    cec_id = db.Column(db.String(200), primary_key=True)
    cec_email = db.Column(db.String(200), primary_key=True)
