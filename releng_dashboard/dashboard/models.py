from . import db


class Releases(db.Model):
    __tablename__ = 'RELEASES'
    id = db.Column(db.Integer, primary_key=True)
    release_number = db.Column(db.String(200))
    builds_build_number = db.Column(db.String(200))
    builds_products_product_name = db.Column(db.String(100))
    dh_online_url = db.Column(db.String(400))
    dh_offline_url = db.Column(db.String(400))
    cco_url = db.Column(db.String(400))
    promotion_stage = db.Column(db.String(200))
    promotion_status = db.Column(db.String(200))
    release_type = db.Column(db.String(200))
    promoted_on = db.Column(db.BigInteger)
    rer_number = db.Column(db.Integer)
    customer = db.Column(db.String(200))
    cdl_artifactory_link = db.Column(db.String(1000))
    loc_report = db.Column(db.String(1000))
    change_log = db.Column(db.String(1000))


class Branches(db.Model):
    __tablename__ = 'BRANCHES'
    id = db.Column(db.Integer, primary_key=True)
    products_organization = db.Column(db.String(400))
    branch_name = db.Column(db.String(200))
    parent_branch = db.Column(db.String(200))
    parents_branch_point = db.Column(db.String(400))
    branch_creation_date = db.Column(db.String(200))
    branch_status = db.Column(db.String(200))
    branch_version = db.Column(db.String(200))
    branch_type = db.Column(db.String(200))
    branch_description = db.Column(db.String(200))
    rer_number = db.Column(db.String(200))


class Products(db.Model):
    __tablename__ = 'PRODUCTS'
    id = db.Column(db.Integer, primary_key=True)
    product_name = db.Column(db.String(100))


class Organizations(db.Model):
    __tablename__ = 'ORGNIZATIONS'
    id = db.Column(db.Integer, primary_key=True)
    organization = db.Column(db.String(200))
    product_name = db.Column(db.String(100))
    PRODUCT_ARTIFACTORY_EP = db.Column(db.String(600))


class TT_Role(db.Model):
    __tablename__ = 'TT_ROLE'
    id = db.Column(db.Integer, primary_key=True)
    role = db.Column(db.String(200))
    users_cec_id = db.Column(db.String(100))
    products_organization = db.Column(db.String(400))
    approval_flag = db.Column(db.String(1))
    email_notification_status = db.Column(db.String(1))
    users_cec_email = db.Column(db.String(200))

class IVT(db.Model):
    __tablename__ = 'IVT_REPORTS'
    products_product_name = db.Column(db.String(200), primary_key=True)
    build_number = db.Column(db.String(200), primary_key=True)
    branch_name = db.Column(db.String(200))
    wrt_report = db.Column(db.String(600))
    ivt_approved = db.Column(db.String(100))
    passed_percentage = db.Column(db.Integer)
    total_feature_file_count = db.Column(db.Integer)
    passed_case_count = db.Column(db.Integer)
    failed_case_count = db.Column(db.Integer)
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)

class SANITY_REPORTS(db.Model):
    __tablename__ = 'SANITY_REPORTS'
    id = db.Column(db.Integer)
    products_product_name = db.Column(db.String(200), primary_key=True)
    build_number = db.Column(db.String(200), primary_key=True)
    branch_name = db.Column(db.String(200))
    wrt_report = db.Column(db.String(600))
    sanity_approved = db.Column(db.String(100))
    passed_percentage = db.Column(db.Integer)
    total_feature_file_count = db.Column(db.Integer)
    passed_case_count = db.Column(db.Integer)
    failed_case_count = db.Column(db.Integer)
    start_time = db.Column(db.Date)
    end_time = db.Column(db.Date)
    functional_suite = db.Column(db.String(200), primary_key=True)
    job_type = db.Column(db.String(200),primary_key=True)
    jenkins_job = db.Column(db.String(1000))
    status = db.Column(db.String(200))
    passing_criteria = db.Column(db.Integer)
    fail_test_case = db.Column(db.Text)
    pass_test_case = db.Column(db.Text)

class DASHBOARD_ACCESS(db.Model):
    ORG_NAME = db.Column(db.String(200),primary_key=True)
    REPO_NAME = db.Column(db.String(100))
    CEC_ID = db.Column(db.String(100),primary_key=True)
    ROLE = db.Column(db.String(100))
    CEC_FULLNAME = db.Column(db.String(100))
