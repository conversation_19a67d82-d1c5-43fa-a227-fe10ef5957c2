import os
import re
import sys
import datetime
import time
import subprocess
from pprint import pprint
from flask import flash
from flask_mail import Message
from dashboard.throttle_tracker.tasks import get_tt_approvers
from dashboard.build.tasks import trigger_jenkins_build
from .models import StaleBranches, DashboardDeletedBranches
from dashboard.branch_ops.models import DashboardMergeData, DashboardMergePR
from ..models import Branches
from .. import mail, db
from os import environ
from .. import cache
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
#sys.path.append("/Users/<USER>/Downloads/2021/5g-dashboard/5g-dash-merge-oct/enhancement-p2/releng-tools/libs/")
from GithubApi import GithubApi
from Bot import bot_send_message
from dashboard.build.models import TriggeredBuilds
from dashboard.build.tasks import trigger_jenkins_build
from merge_dashboard_utils import MergeDashboardUtils
from dashboard import oidc
from dashboard.access_control.tasks import get_access_role
from .. import celery

def merge_branch_request(username, product, parameter):
    # Insert data into dashboard_deleted_branches
    branch_merge_obj = DashboardMergeData()
    branch_merge_obj.merge_org = parameter['ORG']
    branch_merge_obj.merge_repo = parameter['REPO']
    branch_merge_obj.source_branch = parameter['SOURCE_BRANCH']
    branch_merge_obj.destination_branch = parameter['DESTINATION_BRANCH']
    branch_merge_obj.triggered_by = username
    branch_merge_obj.dry_run = parameter['DRY_RUN']
    branch_merge_obj.merge_status = parameter['MERGE_STATUS']
    branch_merge_obj.start_time = parameter['START_TIME']
    branch_merge_obj.approval_status = parameter['APPROVAL_STATUS']
    branch_merge_obj.approver = parameter['APPROVER']
    branch_merge_obj.notify_by_email = parameter['NOTIFY_BY_EMAIL']

    try:
        db.session.add(branch_merge_obj)
        db.session.commit()
        pprint("Merge data added in DB successfully")
    except Exception as e:
        db.session.rollback()
        pprint("Unable to write to DB %s" % e)
    finally:
        db.session.close()
    pprint("Merge to branch : %s information for Repo: %s inserted into DB" % (parameter['DESTINATION_BRANCH'], parameter['REPO']))
    return None

def get_current_merge_id(username, product, start_time):
    get_current_merge_id_obj = DashboardMergeData()
    merge_id = None
    print("start time is",start_time)
    try:
        merge_id_row = get_current_merge_id_obj.query.filter_by(start_time=start_time).first()
        merge_id = merge_id_row.merge_id
    except Exception as e:
        pprint(e)
    finally:
        db.session.close()
    return merge_id

def query_db_merge_requests(product_org,product):
    merge_branch_requests = []
    branch_obj = DashboardMergeData()
    try:
        for row in branch_obj.query.filter_by(merge_org=product_org):
            merge_branch_requests.append(row.__dict__)
    except Exception as e:
        pprint(e)
    finally:
        db.session.close()
    return merge_branch_requests

def query_db_merged_branches(product_org,product):
    merged_branch_info = []
    triggered_builds_info = []
    merged_status_info = []
    branch_obj = DashboardMergeData()
    triggered_build_obj = TriggeredBuilds()
    try:
        for row in branch_obj.query.filter_by(merge_org=product_org):
            merged_branch_info.append(row.__dict__)
        for row in triggered_build_obj.query.filter_by(product_name=product):
            triggered_builds_info.append(row.__dict__)
    except Exception as e:
        pprint(e)
    finally:
        db.session.close()
    for i in merged_branch_info:
        for j in triggered_builds_info:
            #if i['jenkins_build_url'] is not None or j['build_url'] is not None and i['jenkins_build_url'] == j['build_url']:
            if i['jenkins_build_url'] == j['build_url']:
                i['merge_id'] = i['merge_id']
                i['merge_org'] = i['merge_org']
                i['merge_repo'] = i['merge_repo']
                i['source_branch'] = i['source_branch']
                i['destination_branch'] = i['destination_branch']
                i['start_time'] = i['start_time']
                i['end_time'] = i['end_time']
                i['jenkins_build_url'] = j['build_url']
                i['triggered_by'] = i['triggered_by']
                i['merge_status'] = i['merge_status']
                i['dry_run'] = i['dry_run']
                i['merge_report'] = i['merge_report']
                i['approval_status'] = i['approval_status']
                i['notify_by_email'] = i['notify_by_email']
                i['build_status'] = j['status']
                merged_status_info.append(i)
    merged_status_info = [dict(t) for t in {tuple(d.items()) for d in merged_status_info}]
    return merged_status_info

def get_pr_info_by_merge_id(merge_id):
    pr_branch_info = []
    pr_branch_obj = DashboardMergePR()
    try:
        for row in pr_branch_obj.query.filter_by(merge_id=merge_id):
            pr_branch_info.append(row.__dict__)
    except Exception as e:
        pprint(e)
    finally:
        db.session.close()
    return pr_branch_info

def process_sync_request(username,merge_id,action):
    merge_data_obj = DashboardMergeData()
    try:
        merge_data_obj.query.filter_by(merge_id=merge_id).update(dict(approver=username,approval_status=action))
        pprint("State changed for merge-id %s to %s" % (merge_id ,action))
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        pprint("Fail to update the state for merge id %s and %s" % (merge_id, e))
    finally:
        db.session.close()

def get_sync_request(merge_id):
    merge_data_obj = DashboardMergeData()
    merge_id_info = []
    try:
        for row in merge_data_obj.query.filter_by(merge_id=merge_id):
            merge_id_info.append(row.__dict__)
    except Exception as e:
        pprint(e)
    finally:
        db.session.close()
    return merge_id_info

def check_if_dev_branch_throttle_enabled(product_org, branch):
    branch_obj = Branches()
    branch_status = ''
    try:
        branch_row = branch_obj.query.filter_by(products_organization=product_org, branch_name=branch).first()
        print("*****")
        print(branch_row)
        print("*****")
        if branch_row is None:
            branch_status = "dev-branch"
        else:
            branch_status = branch_row.branch_status
            if branch_status == 'throttle-enabled':
                branch_status = 'throttle-enabled'
            else:
                branch_status = "dev-branch"
    except Exception as e:
        pprint(e)
    finally:
        db.session.close()
    return branch_status

def sync_request_notification(merge_request_parameter,approvers):
    username = merge_request_parameter['TRIGGERED_BY']
    product_org = merge_request_parameter['ORG']
    product = merge_request_parameter['PRODUCT']
    source_branch = merge_request_parameter['SOURCE_BRANCH']
    destination_branch = merge_request_parameter['DESTINATION_BRANCH']
    merge_id = merge_request_parameter['MERGE_ID']
    repo = merge_request_parameter['REPO']
    approval_status = merge_request_parameter['APPROVAL_STATUS']
    approver = merge_request_parameter['APPROVER']
    email = username + "@cisco.com"

    # Send email & Bot Notification
    subject = "Branch sync request by %s for %s : %s -> %s" % (username,product,source_branch,destination_branch)
    msg = Message(subject=subject, sender="<EMAIL>")
    approvers_list = [x + "@cisco.com" for x in approvers]
    approvers_list.append(email)
    msg.recipients = approvers_list

    html = """\
                <html>
                  <head>Branch sync request has been raised by <b> %s </b> on product : <b> %s </b> </head>
                  <body>
                    <p><b>Request Details:</b><br> <br>
                      Merge ID           : %s <br>
                      Github Organization: %s <br>
                      Repo(s)            : %s <br>
                      Source Branch      : %s <br>
                      Destination Branch : %s <br>
                      Approval Status    : %s <br>
                      Approver           : %s <br> <br> <br>

                   Kindly follow  <a href="https://cn-rel-dash-lnx.cisco.com/product_sync_homepage/%s">this link</a>  to Approve/Deny the Sync request.

                   <br><br><br>
                   Thanks,<br>CN Releng Team <br> <a href="https://cn-rel-dash-lnx.cisco.com/">5G Dashboard </a>
                </p>
              </body>
            </html>
                """ % (username,product,merge_id,product_org,repo,source_branch,destination_branch,approval_status,approver,product)

    # bot content (bot_con)
    bot_con = 'Hello,\n\nBranch sync request has been raised by %s on product %s. \n\nRequest Details: \n\nMerge ID: %s \nGithub Organization: %s \nRepo(s) : %s \nSource Branch : %s \nDestination Branch: %s \nApproval Status: %s \nApprover: %s \n' % (username,product,merge_id,product_org,repo,source_branch,destination_branch,approval_status,approver)
    bot_con += '\nKindly follow link : https://cn-rel-dash-lnx.cisco.com/product_sync_homepage/%s to Approve/Deny the Sync request' % product
    bot_con += '\n\nThanks,\nCN Releng Team'

    content = html
    msg.html = content
    mail.send(msg)
    approvers_list = list(set(approvers_list))
    #bot_notifiers = ','.join([str(elem) for elem in approvers_list])
    for mailid in approvers_list:
        bot_send_message(email=mailid, message=bot_con)
    # Room ID of 5G Dashboard Notification Space
    room_id = os.environ['room_id']
    bot_send_message(room_id=room_id, message=bot_con)
    return


def query_merge_pr(product_org):
    merge_pr_info = []
    merge_data_obj = DashboardMergeData()
    pr_data_obj = DashboardMergePR()
    try:
        for row in pr_data_obj.query.join(merge_data_obj, pr_data_obj.merge_id==merge_data_obj.merge_id).add_columns\
            (merge_data_obj.merge_id,pr_data_obj.merge_pr_id,merge_data_obj.org,pr_data_obj.merge_pr_repo,merge_data_obj.source_branch,merge_data_obj.destination_branch,\
            merge_data_obj.source_branch_sha,merge_data_obj.destination_branch_sha,merge_data_obj.start_time,merge_data_obj.end_time,\
            merge_data_obj.jenkins_build_id,merge_data_obj.triggered_by,merge_data_obj.merge_status,pr_data_obj.pr_link,\
            pr_data_obj.pr_authors):
            data=dict()
            data['merge_id'] = row[1]
            data['merge_pr_id'] = row[2]
            data['merge_org'] = row[3]
            data['merge_pr_repo'] = row[4]
            data['source_branch'] = row[5]
            data['destination_branch'] = row[6]
            data['source_branch_sha'] = row[7]
            data['destination_branch_sha'] = row[8]
            data['start_time'] = row[9]
            data['end_time'] = row[10]
            data['jenkins_build_id'] = row[11]
            data['triggered_by'] = row[12]
            data['merge_status'] = row[13]
            data['pr_link'] = row[14]
            merge_pr_info.append(data)
    except Exception as e:
        pprint(e)
    finally:
        db.session.close()
    return merge_pr_info

def refresh_pr_page(merge_id,dbinst):
    github_obj = GithubApi()
    # Get the list of PRs from the merge ID
    merge_id_pr_list = get_pr_info_by_merge_id(merge_id)
    print("merge_id_pr_list value is ", merge_id_pr_list)
    merge_id_pr = []
    for pr in merge_id_pr_list:
            merge_id_pr.append(pr['pr_link'])
    for pr_url in merge_id_pr:
        pr_number = pr_url.split("/")[-1]
        pr_repo = pr_url.split("/")[-3]
        pr_org = pr_url.split("/")[-4]
        # Get PR head_sha 
        pr_data = github_obj.get_pr_details(pr_org,pr_repo,pr_number)
        if pr_data:
            pr_head_sha = pr_data['pr_head_sha']
            pr_from_branch = pr_data['pr_from_branch']
            pr_to_branch = pr_data['pr_to_branch']
            merge_utils_obj = MergeDashboardUtils(pr_url,pr_org,pr_repo,pr_number,pr_to_branch,pr_from_branch,pr_head_sha,dbinst,merge_id,context="all")
            pr_status,pr_build_status,pr_has_conflict,pr_check_complete_flag,pr_failure_reason = merge_utils_obj.check_pr_completion()
            merge_utils_obj.update_dashboard_merge_pr_status(pr_status,pr_build_status,pr_has_conflict,pr_check_complete_flag,pr_failure_reason)
    # Update merge status based on all PR data
    refresh_merge_status(merge_id)
                            
def refresh_merge_status(merge_id):
    github_obj = GithubApi()
    merge_data_obj = DashboardMergeData()
    pr_data_obj = DashboardMergePR()
    merged_state_list = []
    mergeable = False
    merge_id_pr = get_pr_info_by_merge_id(merge_id)
    merge_id_pr_list = []
    for pr in merge_id_pr:
            merge_id_pr_list.append(pr['pr_link'])
    print("merge_id_pr_list is", merge_id_pr)
    for pr in merge_id_pr_list:
        pr_number = pr.split("/")[-1]
        pr_repo = pr.split("/")[-3]
        pr_org = pr.split("/")[-4]
        pr_info = github_obj.get_pr_details(pr_org,pr_repo,pr_number)
        if pr_info:
            pr_merged = pr_info['merged']
            state = pr_info['state']
            if pr_merged and state == 'closed':
                merged_state = 'MERGED'
                merged_state_list.append(merged_state)
            elif not pr_merged and state == 'closed':
                merged_state = 'CLOSED'
                merged_state_list.append(merged_state) 
            else:
                merged_state = 'IN_PROGRESS'
                merged_state_list.append(merged_state)
            print('merged_state_list is',merged_state_list)

    fully_merged = fully_closed = partially_closed = merge_in_progress = False
    if merged_state_list:
        merged_count = merged_state_list.count('MERGED')
        closed_count = merged_state_list.count('CLOSED')
        progress_count = merged_state_list.count('IN_PROGRESS')
        print("Merged count is %s , closed count is %s , progress count is %s " % (merged_count,closed_count,progress_count))
        if closed_count >= 0 and merged_count > 0 and progress_count == 0:
            fully_merged = all(element in ["MERGED","CLOSED"] for element in merged_state_list)
        elif closed_count > 0 and merged_count == 0 and progress_count == 0:
            fully_closed = all(element == "CLOSED" for element in merged_state_list)
        elif closed_count > 0 and merged_count > 0 and progress_count >= 0:
            partially_closed =  any(element in ["MERGED","CLOSED"] for element in merged_state_list)
        elif closed_count >= 0 and merged_count == 0 and progress_count >=0:
            merge_in_progress = any(element in "PROGRESS" for element in merged_state_list)
    else:
        fully_merged = fully_closed = partially_closed = merge_in_progress = False

    print("Fully Merged is %s , Fully closed is %s , partially closed is %s,Merge in progress is %s  " % (fully_merged,fully_closed,partially_closed,merge_in_progress))
    if fully_merged:
        print("All PRs in MERGED state")
        try:
            # update the status of the DashboardMergePR state to MERGED for all PRs under that mergeID
            merge_data_obj.query.filter_by(merge_id=merge_id).update(dict(merge_status="MERGED"))
            db.session.commit()
            pprint("State changed for %s to " % (merge_id))
        except Exception as e:
            db.session.rollback()
            pprint("Fail to update the state for merge id %s and %s" % (merge_id, e))
        finally:
            db.session.close()
    elif fully_closed:
        print("All PRs in CLOSED state")
        try:
            # update the status of the DashboardMergePR state to CLOSED for all PRs under that mergeID
            merge_data_obj.query.filter_by(merge_id=merge_id).update(dict(merge_status="CLOSED"))
            db.session.commit()
            pprint("State changed for %s to " % (merge_id))
        except Exception as e:
            db.session.rollback()
            pprint("Fail to update the state for merge id %s and %s" % (merge_id, e))
        finally:
            db.session.close()
    elif partially_closed:
        print("Some PRs in CLOSED/MERGED state")
        try:
            # update the status of the DashboardMergePR state to CLOSED for all PRs under that mergeID
            merge_data_obj.query.filter_by(merge_id=merge_id).update(dict(merge_status="PARTIALLY_CLOSED"))
            db.session.commit()
            pprint("State changed for %s to " % (merge_id))
        except Exception as e:
            db.session.rollback()
            pprint("Fail to update the state for merge id %s and %s" % (merge_id, e))
        finally:
            db.session.close()
    elif merge_in_progress:
        print("Some PRs in PROGRESS state")
        try:
            # update the status of the DashboardMergePR state to CLOSED for all PRs under that mergeID
            merge_data_obj.query.filter_by(merge_id=merge_id).update(dict(merge_status="IN_PROGRESS"))
            db.session.commit()
            pprint("State changed for %s to " % (merge_id))
        except Exception as e:
            db.session.rollback()
            pprint("Fail to update the state for merge id %s and %s" % (merge_id, e))
        finally:
            db.session.close()
    else:
        print("All PRs are not in MERGED state, some PRs are closed without merging or in conflict state")
        try:
            # update the status of the DashboardMergePR state to closed for all PRs under that mergeID
            merge_data_obj.query.filter_by(merge_id=merge_id).update(dict(merge_status="IN_PROGRESS"))
            db.session.commit()
            pprint("State changed for %s to " % (merge_id))
        except Exception as e:
            db.session.rollback()
            pprint("Fail to update the state for merge id %s and %s" % (merge_id, e))
        finally:
            db.session.close()

def extend_stale_branch_deletion(username, product, product_org,branches_to_extend):
    github_obj = GithubApi()
    branch_obj = StaleBranches()
    for value in branches_to_extend:
        branch = value.split('__sep__')[0]
        repo = value.split('__sep__')[1]
        head_sha = value.split('__sep__')[2]
        deletiondate = value.split('__sep__')[3]
        branch_status = value.split('__sep__')[4]
        expiry = datetime.datetime.strptime(deletiondate,"%d %b %Y")
        expiry = expiry + datetime.timedelta(days=30)
        expiry = (expiry.date()).strftime("%Y-%m-%d")
        try:
            branch_obj.query.filter_by(product=product_org,repo=repo,branch_name=branch).update(dict(branch_status="obsolete",expiry_date=expiry,last_user=username))
            db.session.commit()
            flash("successfully extended branch deletion date ", "success")
            result = True
        except Exception as e:
            pprint("Unable to write to DB %s" % e)
            db.session.rollback()
            flash("Failed to extend branch deletion date", "failure")
            result = None
        finally:
            db.session.close()

def delete_stale_branches(username, product, product_org, branches_to_delete):
    branch_list = []
    if not branches_to_delete:
        flash('You need to select the branch/es to be deleted.', "info")
        return

    user_role = get_access_role(username,product_org)
    branch_dic = {}
    for value in branches_to_delete:
        branch = value.split('__sep__')[0]
        repo = value.split('__sep__')[1]
        head_sha = value.split('__sep__')[2]
        branch_status = value.split('__sep__')[5]
        if re.match('^master$|^main$|^rel-|^tl-', branch):
            # Don't allow user to delete main or release branches.
            flash("You can't delete main or release branches. Plz reach out to "
                  "<EMAIL>", "danger")
            return None

        if branch_status == 'Y' and user_role != 'releng':
            flash("You can't delete a protected branch. Plz reach out to "
                  "<EMAIL>", "danger")
            return None

        if branch not in branch_dic:
            branch_dic[branch] = []

        branch_repo_list = branch_dic[branch]
        branch_repo_list.append(repo)
        branch_dic[branch] = branch_repo_list

        info = [branch, repo, head_sha]
        branch_list.append(info)

    for branch, branch_repo_list in branch_dic.items():
        # Trigger the job to delete the branch
        parameter = {'ORGANIZATION' : product_org,
                     'BRANCH_NAME' : branch,
                     'REPO' : ",".join(branch_repo_list),
                     'REMOVE_BRANCH_PROTECTION' : 'true',
                     'BRANCH_OWNER' : str(oidc.user_getfield('uid'))}
        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('DELETE_BRANCH_JOB_NAME'),
                                    parameter)

    flash("Branch deletion is in progress. You will receive a Bot message with jenkins job details", "success")
    # Delete stable branches cache for the product.
    clean_up_cache.delay()
    return None

@celery.task(name='clean_up_delete_branch_cache', trail=True)
def clean_up_cache():
    time.sleep(200)
    cache.delete_memoized(query_delete_branches)
    cache.delete_memoized(query_db_deleted_branches)

def create_git_branch(username, product, product_org, component, parent_branch, branch_name, description, parent_tag=None):
    #return True, 'Testing'
    #createBranch(self, NEW_BRANCH_NAME, org, component, parent_branch, parent_tag=None):
    room_id = os.environ['room_id']
    github_obj = GithubApi()
    print(" parent_tag %s" % parent_tag)
    try:
        branch = github_obj.check_branch_existance(product_org, component, branch_name)
        if branch is not None:
            result = 1
            status = "branch exists"
            return False, status
        result, status = github_obj.create_branch(branch_name, product_org, component, parent_branch, parent_tag)
    except Exception as e:
        print("Got exception while creating branch", e)
        result = 1
        status = ''
    if 1 ==  result:
        return False, status
    return True, status


@cache.memoize(timeout=1200)
def query_delete_branches(product_org):
    del_branch_info = []
    branch_obj = StaleBranches()
    for row in branch_obj.query.filter_by(product=product_org):
        data = row.__dict__
        last_co_date = data['lastcommitdate']
        if data['expiry_date']:
            expiry = data['expiry_date']
        else:
            expiry = ""
        data['lastcommitdate'] = datetime.datetime.strptime(last_co_date,"%Y-%m-%d").strftime('%d %b %Y')
        if data['expiry_date']:
            data['expiry_date'] = datetime.datetime.strptime(expiry,"%Y-%m-%d").strftime('%d %b %Y')
        del_branch_info.append(data)
    return del_branch_info


@cache.memoize(timeout=1200)
def query_db_deleted_branches(product_org):
    deleted_branch_info = []
    branch_obj = DashboardDeletedBranches()
    for row in branch_obj.query.filter_by(product=product_org):
        deleted_branch_info.append(row.__dict__)
    return deleted_branch_info