from flask import render_template, redirect, flash, Blueprint, request
from dashboard.tasks import get_product_list, get_product_organization, get_user_role, get_branch_list, check_team, get_dashboard_user_role
from dashboard.build.tasks import trigger_jen<PERSON>_build,validate_qa_build
from dashboard.build.models import Builds,TriggeredBuilds
from dashboard.branch_ops.forms import DeleteForm, CreateBranchOrgForm, CreateBranchHomeForm, CreateBranchProductForm, MergeForm, MergeStatusForm, PRStatusForm, BranchForm
from dashboard.tasks import list_to_choices
from dashboard.branch_ops.tasks import delete_stale_branches, query_delete_branches, query_db_deleted_branches, create_git_branch, extend_stale_branch_deletion, query_db_merge_requests, get_current_merge_id, query_merge_pr, get_pr_info_by_merge_id, process_sync_request,get_sync_request,merge_branch_request,check_if_dev_branch_throttle_enabled,sync_request_notification,refresh_pr_page,refresh_merge_status
from dashboard.throttle_tracker.tasks import get_tt_role, get_tt_approvers
from dashboard.models import TT_Role, Branches
from os import environ
from dashboard import oidc
import sys
import os, re
import datetime
from .. import cache
from dateutil.parser import parse


sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
from GithubApi import GithubApi
from Constants import PRODUCT_LIST, NO_QA_PRODUCT_LIST
from Bot import bot_send_message
import time

branch_ops = Blueprint('branch_ops', __name__)
grafana_host=os.environ["GRAFANA_HOST"]

# Display Branch Details
@branch_ops.route('/branches/<product>', methods=['GET', 'POST'])
@oidc.require_login
def product_branches(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "info")
        return redirect('/home')

    # Get the associated product_org
    product_org = get_product_organization(product)
    # If product org not defined, it is one of the common org or smi org. Redirect to home page with pop up
    if not product_org:
        flash("No branch details present with Product :: %s " % product, "danger")
        return redirect('/%s' % product)

    form = BranchForm()
    show_active_button = False
    if form.validate_on_submit():
        if form.submit_all.data:
            branch_info = query_branches(product_org, "all")
            show_active_button = True
        else:
            return redirect('/branches/%s' % product)
    else:
        branch_info = query_branches(product_org, "not obsolete")
    return render_template('product_branches.html', product=product, branch_info=branch_info, form=form, show_active_button=show_active_button)


@branch_ops.route('/deletebranches/<product>', methods=['GET', 'POST'])
@oidc.require_login
def delete_branches(product):
    username = str(oidc.user_getfield('uid'))
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    # Get product org for the product
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'
    # Get product org for the product
    perm, team_name = check_team(username, product, product_org, 'admins')
    if perm==1:
        perm, team_name = check_team(username, product, product_org, 'write')
        if perm==1:
            flash("Operation Denied .You are not part of %s GitHub Team . "
                  "Only %s team's member can delete branches via Dashboard. " % (team_name, team_name), "danger")
            return redirect('/%s' % product)
    form = DeleteForm()
    if form.validate_on_submit():
        if form.renew.data:
            username = str(oidc.user_getfield('uid'))
            branches_to_extend = (request.form.getlist(product + "_Select"))
            extend_stale_branch_deletion(username, product, product_org,branches_to_extend)
        elif form.submit.data:
            branches_to_delete = (request.form.getlist(product + "_Select"))
            delete_stale_branches(str(oidc.user_getfield('uid')), product, product_org,branches_to_delete)
            return redirect('/deletebranches/%s' % product)

    del_branch_info = query_delete_branches(product_org)
    return render_template('delete_branches.html', product_org=product_org, product=product,
                           del_branch_info=del_branch_info, form=form)


@branch_ops.route('/db_deleted_branches/<product>', methods=['GET', 'POST'])
@oidc.require_login
def db_deleted_branches(product):
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')

    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'
    # Get product org for the product
    db_del_branch_info = query_db_deleted_branches(product_org)
    return render_template('db_deleted_branches.html', product_org=product_org, product=product,
                           db_del_branch_info=db_del_branch_info)


@branch_ops.route('/createbranchhome/<product>', methods=['GET', 'POST'])
@oidc.require_login
def create_branch_home(product):
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')

    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'

    form = CreateBranchHomeForm()
    if request.method == "POST":
        create_branch_type = form.create_branch_type.data
        if form.validate_on_submit():
            if create_branch_type == 'P':
                return redirect('/createbranchproduct/%s' % product)
            else:
                return redirect('/createbranchorg/%s' % product)
    return render_template('create_branch_home.html', form=form, product=product,product_org=product_org,no_qa_product_list=NO_QA_PRODUCT_LIST)


@branch_ops.route('/createbranchorg/<product>', methods=['GET', 'POST'])
@oidc.require_login
def create_branch_org(product):
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')

    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'

    username = str(oidc.user_getfield('uid'))
    # Get product org for the product
    perm, team_name = check_team(username, product, product_org, 'admins')
    if perm==1:
        perm, team_name = check_team(username, product, product_org, 'write')
        if perm==1:
            flash("Operation Denied .You are not part of %s GitHub Team . "
                  "Only %s team's member can create branches via Dashboard. " % (team_name, team_name), "danger")
            return redirect ('/createbranchhome/%s' % product)

    github_obj = GithubApi()
    all_repos = github_obj.list_all_repos_names(product_org)
    if not all_repos:
        flash("Couldn't find the repos. Contact <EMAIL>", "danger")
        return redirect('/createbranchhome/%s' % product)
    form = CreateBranchOrgForm()
    if request.method == "POST":
        repos_to_create_branch = request.form.getlist("repo_list")
        branch = form.branch.data
        select_all_repos = form.select_all_repos.data
        create_branch_ref = form.create_branch_ref.data
        cdets_id = form.cdets_id.data

        if form.validate_on_submit():
            if not (branch.startswith('dev-') or branch.startswith('test-')):
                flash("Failed. Branch name should start with 'dev-' or 'test-'", "danger")
                return redirect('/createbranchorg/%s' % product)
            if len(branch) >= 17:
                flash("Failed. Branch name should be less than 17 chars", "danger")
                return redirect('/createbranchorg/%s' % product)
            if len(repos_to_create_branch) < 1 and not select_all_repos:
                flash("Failed. No repos selected for branch creation.", "danger")
                return redirect('/createbranchorg/%s' % product)
        if select_all_repos:
            repos_to_create_branch = all_repos

        parameter = {'PRODUCT' : product,
                     'ORGANIZATION': product_org,
                     'REPO': ",".join(repos_to_create_branch),
                     'PARENT_BRANCH': form.parent_branch.data,
                     'BRANCH_NAME': form.branch.data,
                     'COMMIT_AUTHOR_CEC_ID' : str(oidc.user_getfield('uid')),
                     'SKIP_DB' : "true"
                     }
        if create_branch_ref:
            parameter['CHANGE_BRANCH_REFERENCE'] = "true"
        if cdets_id:
            parameter['COMMIT_CDETS_ID'] = cdets_id

        trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('CREATE_BRANCH_JOB_NAME'),
                                    parameter)
        if create_branch_ref:
            flash("Branch creation & branch reference change is in progress. You will receive a Bot message with jenkins job details", "success")
        else:
            flash("Branch creation is in progress. You will receive a Bot message with jenkins job details", "success")
        return redirect('/createbranchorg/%s' % product)
    return render_template('create_branch_org.html', product_org=product_org, product=product, repo_info=all_repos,
                           form=form)


@branch_ops.route('/createbranchproduct/<product>', methods=['GET', 'POST'])
@oidc.require_login
def create_branch_product(product):
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')

    if product in NO_QA_PRODUCT_LIST:
        flash(
            "%s is part of ccg product. plz create a branch from CCG product page" % product,
            "danger")
        return redirect('/home')

    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'
    username = str(oidc.user_getfield('uid'))
    email = str(oidc.user_getfield('uid')) + "@cisco.com"
    room_id = os.environ['room_id']
    user_role,repo_name = get_dashboard_user_role(product_org,username)
    if user_role != 'ORG_OWNER' and user_role != 'DEV_MANAGER' and user_role != 'TECH_LEAD'and user_role != 'RELENG':
            flash("%s, you are not authorize to create branch in org %s. Plz contact releng team" % (username,product_org),
                  "danger")
            return redirect('/createbranchhome/%s' % product)

    form = CreateBranchProductForm()
    parent_branch_name = get_branch_list(product)
    if not parent_branch_name:
        parent_branch_name = ['main']
    form.parent_branch_name.choices = list_to_choices(parent_branch_name)
    if request.method == "POST":
        if form.validate_on_submit():
                if not (form.branch_name.data.startswith('dev-') or form.branch_name.data.startswith('test-')):
                    flash("Failed. Branch name should start with 'dev-' or 'test-'", "danger")
                    return redirect('/createbranchproduct/%s' % product)
                if len(form.branch_name.data) >= 17:
                    flash("Failed. Branch name should be less than 17 chars. Current len is %s" % len(form.branch_name.data), "danger")
                    return redirect('/createbranchproduct/%s' % product)
        if form.submit.data:
            branch_name = form.branch_name.data
            parent_branch_name = form.parent_branch_name.data
            parent_QA_build_no = form.parent_QA_build_no.data
            cdets_id = form.cdets_id.data
            if form.expiry_date.data:
                expiry_date = form.expiry_date.data.strftime("%Y-%m-%d")
            else:
                # If expiry date is not given take 1 year as default
                expiry_date = (datetime.datetime.utcnow() + datetime.timedelta(days=365)).strftime("%Y-%m-%d")
            if parent_QA_build_no != "latest":
                if not validate_qa_build(parent_QA_build_no, parent_branch_name, product):
                    flash("Plz enter a valid QA build number","danger")
                    return redirect('/createbranchproduct/%s' % product)
            branch_description = form.branch_description.data
            enable_branch_protection = form.enable_branch_protection.data

            if enable_branch_protection:
                skip_branch_pro = False
            else:
                skip_branch_pro = True

            trigger_qa_build = form.trigger_qa_build.data

            if trigger_qa_build:
                skip_db_entry = False
            else:
                skip_db_entry = True
            if product == 'lbs-libraries':
                product= 'ulb'
            parameter = {'BRANCH_NAME': branch_name,
                         'BRANCH_TYPE': "dev",
                         'BRANCH_DESCRIPTION': branch_description,
                         'PRODUCT' : product,
                         'PARENT_BRANCH' : parent_branch_name,
                         'PARENT_QA_BUILD_NO' : parent_QA_build_no,
                         'BRANCH_OWNER': username,
                         'EXPIRY_DATE' : expiry_date,
                         'SKIP_BRANCH_PROTECTION' : skip_branch_pro,
                         'SKIP_DB' : skip_db_entry,
                         'COMMIT_AUTHOR_CEC_ID' : str(oidc.user_getfield('uid')),
                         'RELENG_BRANCH_NAME' : "main"
                         }
            if cdets_id:
                parameter['COMMIT_CDETS_ID'] = cdets_id
            trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('CREATE_BRANCH_PRODUCT_JOB_NAME'),
                                                    parameter)
            flash ("Branch creation is in progress. You will receive a Bot message with jenkins job details", "success")
            return redirect('/createbranchhome/%s' % product)
    return render_template('create_branch_product.html', product=product, form=form, product_org=product_org)

@branch_ops.route('/mergebranchrequest/<product>', methods=['GET', 'POST'])
@oidc.require_login
def create_merge_request(product):
    global merge_request_parameter,branch_status
    username = str(oidc.user_getfield('uid'))
    email = str(oidc.user_getfield('uid')) + "@cisco.com"
    room_id = os.environ['room_id']
    branch_status = []
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'
    # Get product org for the product
    perm, team_name = check_team(username, product, product_org, 'admins')
    if perm==1:
        perm, team_name = check_team(username, product, product_org, 'write')
        if perm==1:
            flash("Operation Denied .You are not part of %s GitHub Team . "
                  "Only %s team's member can sync branches via Dashboard. " % (team_name, team_name), "danger")
    github_obj = GithubApi()
    all_repos = github_obj.list_all_repos_names(product_org)
    if not all_repos:
        flash("Couldn't find the repos.", "danger")
    form = MergeForm()
    merge_start_time = int(time.time())

    if request.method == "POST":
        repos_to_merge_branch = request.form.getlist("repo_list")
        source_branch=form.source_branch.data
        destination_branch=form.destination_branch.data
        dry_run =form.dry_run.data
        auto_merge =form.auto_merge.data
        select_all_repos=form.select_all_repos.data
        notify_by_email=form.notify_by_email.data
        if destination_branch.startswith(('rel', 'tl')) or destination_branch == 'main' or destination_branch == 'stage':
            branch_status = 'throttle-enabled'
            approval_status = "Pending"
            approver = "-"
            print("Destination branch is a throttle enabled branch so automerge will be disabled")
            auto_merge = False
        elif destination_branch.startswith('dev'):
            # Check if the branch is throttle enabled
            branch_status = check_if_dev_branch_throttle_enabled(product_org, destination_branch)
            if branch_status == 'throttle-enabled':
                branch_status = 'throttle-enabled'
                approval_status = "Pending"
                approver = "-"
                auto_merge = False
            else:
                    branch_status == 'dev-branch'
                    approval_status = '-'
                    approver = "-"
                    auto_merge =form.auto_merge.data
        else:
            branch_status = check_if_dev_branch_throttle_enabled(product_org, destination_branch)
            if branch_status == 'throttle-enabled':
                branch_status = 'throttle-enabled'
                approval_status = "Pending"
                approver = "-"
                auto_merge = False
            else:
                branch_status == 'dev-branch'
                approval_status = '-'
                approver = "-"
            print("Destination branch is not a throttle enabled branch so automerge can be enabled/disabled based on user inputs")
            auto_merge =form.auto_merge.data
        if dry_run:
            approval_status = "-"
            approver = "-"
        if form.validate_on_submit():
            if len(repos_to_merge_branch) < 1 and not select_all_repos:
                flash("Failed. No repos selected for branch merge.", "danger")
                return redirect('/mergebranch/%s' % product)
            if select_all_repos:
                repos_to_merge_branch_str = ",".join(all_repos)
            else:
                repos_to_merge_branch_str = ",".join(repos_to_merge_branch)
            if not notify_by_email:
                notify_by_email = email
            else:
                notify_by_email=form.notify_by_email.data
                notify_by_email += "," + username
                notify_by_cec_list = notify_by_email.split(",")
                notify_by_email_list = [x + "@cisco.com" for x in notify_by_cec_list]
                notify_by_email = ','.join([str(elem) for elem in notify_by_email_list])
            merge_request_parameter = {
                    'ORG': product_org,
                    'PRODUCT' : product,
                    'SOURCE_BRANCH': source_branch,
                    'DESTINATION_BRANCH': destination_branch,
                    'DRY_RUN': dry_run,
                    'AUTO_MERGE': auto_merge,
                    'REPO': repos_to_merge_branch_str,
                    'MERGE_STATUS': 'JOB_UNDERWAY',
                    'START_TIME': merge_start_time,
                    'TRIGGERED_BY': oidc.user_getfield('uid'),
                    'DB_INST': environ.get("DB_INST"),
                    'APPROVAL_STATUS': approval_status,
                    'APPROVER': approver,
                    'NOTIFY_BY_EMAIL': notify_by_email,
            }
            merge_branch_request(username, product, merge_request_parameter)
            merge_id = get_current_merge_id(str(oidc.user_getfield('uid')),product,merge_start_time)
            flash("Processing your Merge request transaction %s .Plz wait or use the refresh button to get the data on dashboard....." % merge_id,"info")
            merge_request_parameter['MERGE_ID'] = merge_id
            if dry_run or branch_status == "dev-branch":
                merge_request_parameter['COMMIT_AUTHOR_CEC_ID'] = str(oidc.user_getfield('uid'))
                trigger_jenkins_build.delay(username, product , environ.get('MERGE_PIPELINE_JOB_NAME'), merge_request_parameter)
                return redirect('/product_sync_homepage/%s' % product)
            else:
                approvers_list =  get_tt_approvers(product_org)
                sync_request_notification(merge_request_parameter,approvers_list)
        return redirect('/product_sync_homepage/%s' % product)
    return render_template('merge_branch_request.html', product_org=product_org, product=product, repo_info=all_repos,
                           form=form)

@branch_ops.route('/product_sync_homepage/<product>', methods=['GET', 'POST'])
@oidc.require_login
def product_sync_homepage(product):
    room_id = os.environ['room_id']
    username = str(oidc.user_getfield('uid'))
    email = str(oidc.user_getfield('uid')) + "@cisco.com"
    current_time = int(time.time())
    time_24_back = current_time - 86400
    db_merged_branch_infoupdated = []
    all_builds = []
    product_list = get_product_list()
    if product not in product_list:
        flash(
            "%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
            "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'
    # Get product org for the product
    db_merged_branch_info = query_db_merge_requests(product_org,product)
    for item in db_merged_branch_info:
        if item['start_time']:
            item['start_time'] = datetime.datetime.fromtimestamp(int(item["start_time"])).strftime('%d %b %Y, %H:%M')
        db_merged_branch_infoupdated.append(item)

    branch_sync_builds = []
    current_builds = {}
    triggered_build_mobj = TriggeredBuilds()
    for row in triggered_build_mobj.query.filter_by(product_name=product).order_by(TriggeredBuilds.start_time.desc()):
        all_builds.append(row.__dict__)
    for data in all_builds:
        # converting start_time to human readable format
        if data["start_time"]:
            if data["status"] in ["IN-PROGRESS"]:
                if data["start_time"] >= time_24_back:
                    data["start_time"] = datetime.datetime.fromtimestamp(int(data["start_time"])).strftime(
                        '%d %b %Y, %H:%M')
                    # ETA is in seconds, converting to mins.
                    if data["eta"]:
                        data["eta"] = int(data["eta"] / 60)
                    if data["job_name"] == environ.get('MERGE_PIPELINE_JOB_NAME'):
                        branch_sync_builds.append(data)

    current_builds["Branch Sync"] = branch_sync_builds
    form = MergeStatusForm()
    user_role,repo_name = get_dashboard_user_role(product_org,username)
    if request.method == "POST":
        if form.validate_on_submit():
            if form.refresh_merge_home.data:
                return redirect('/product_sync_homepage/%s' % product)
            if form.cancel_merge.data:
                cancelmergeid = form.merge_id.data
                cancelmergeid = int(cancelmergeid)
                flash("Processing your request to close all PRs under this Merge request ID %s...." % cancelmergeid, "info")
                cancel_merge_parameter = {'MERGE_ID' : cancelmergeid, 'TRIGGERED_BY' : username}
                trigger_jenkins_build.delay(username, product, environ.get('CLOSE_MERGE_ID'),cancel_merge_parameter)
                return redirect('/product_sync_homepage/%s' % product)
            if form.refresh_merge.data:
                refreshmergeid = form.merge_id.data
                refreshmergeid = int(refreshmergeid)
                flash("Processing your request to refresh all PRs under this Merge request ID %s ,Please wait for a while..." % refreshmergeid, "info")
                merge_refresh_parameter = {
                    'MERGE_ID': refreshmergeid,
                    'DB_INST': environ.get("DB_INST")
                }
                trigger_jenkins_build.delay(username, product , environ.get('MERGE_REFRESH_JOB_NAME'), merge_refresh_parameter)
                refresh_merge_status(refreshmergeid)
                return redirect('/product_sync_homepage/%s' % product)
            if form.approve.data:
                action = "Approved"
                mergeid = form.merge_id.data
                flash("Processing your request to process this Merge request ID %s.... and action is %s" % (mergeid ,action), "info")
                process_sync_request(str(oidc.user_getfield('uid')), form.merge_id.data, action)
                sync_data = get_sync_request(form.merge_id.data)
                del sync_data[0]['_sa_instance_state']
                sync_data_dict = {}
                for d in sync_data:
                    sync_data_dict.update(d)
                sync_data_dict_form = {
                    'MERGE_ID': sync_data_dict['merge_id'],
                    'ORG': sync_data_dict['merge_org'],
                    'PRODUCT': product,
                    'SOURCE_BRANCH': sync_data_dict['source_branch'],
                    'DESTINATION_BRANCH': sync_data_dict['destination_branch'],
                    'DRY_RUN': sync_data_dict['dry_run'],
                    'REPO': sync_data_dict['merge_repo'],
                    'MERGE_STATUS': sync_data_dict['merge_status'],
                    'START_TIME': sync_data_dict['start_time'],
                    'TRIGGERED_BY': sync_data_dict['triggered_by'],
                    'DB_INST': environ.get("DB_INST"),
                    'APPROVAL_STATUS': sync_data_dict['approval_status'],
                    'APPROVER': sync_data_dict['approver'],
                    'NOTIFY_BY_EMAIL': sync_data_dict['notify_by_email'],
                    'COMMIT_AUTHOR_CEC_ID' : sync_data_dict['triggered_by']
                }
                #Trigger jenkins build with the merge request parameter
                trigger_jenkins_build.delay(username, product , environ.get('MERGE_PIPELINE_JOB_NAME'), sync_data_dict_form)
                approvers_list =  get_tt_approvers(product_org)
                sync_request_notification(sync_data_dict_form,approvers_list)
                return redirect('/product_sync_homepage/%s' % product)
            if form.deny.data:
                action = "Denied"
                mergeid = form.merge_id.data
                flash("Processing your request to process this Merge request ID %s.... and action is %s" % (mergeid ,action), "info")
                process_sync_request(str(oidc.user_getfield('uid')), form.merge_id.data, action)
                sync_data = get_sync_request(form.merge_id.data)
                del sync_data[0]['_sa_instance_state']
                sync_data_dict = {}
                for d in sync_data:
                    sync_data_dict.update(d)
                sync_data_dict_form = {
                    'MERGE_ID': sync_data_dict['merge_id'],
                    'ORG': sync_data_dict['merge_org'],
                    'PRODUCT': product,
                    'SOURCE_BRANCH': sync_data_dict['source_branch'],
                    'DESTINATION_BRANCH': sync_data_dict['destination_branch'],
                    'DRY_RUN': sync_data_dict['dry_run'],
                    'REPO': sync_data_dict['merge_repo'],
                    'MERGE_STATUS': "NA",
                    'START_TIME': sync_data_dict['start_time'],
                    'TRIGGERED_BY': sync_data_dict['triggered_by'],
                    'DB_INST': environ.get("DB_INST"),
                    'APPROVAL_STATUS': sync_data_dict['approval_status'],
                    'APPROVER': sync_data_dict['approver'],
                    'NOTIFY_BY_EMAIL': sync_data_dict['notify_by_email']
                }
                approvers_list =  get_tt_approvers(product_org)
                sync_request_notification(sync_data_dict_form,approvers_list)
                return redirect('/product_sync_homepage/%s' % product)
    return render_template('product_sync_homepage.html', product_org=product_org, product=product,db_merged_branch_info=db_merged_branch_infoupdated,
                         form=form, current_builds=current_builds,user_role=user_role)

@branch_ops.route('/pr_info/<merge_id>/<product>', methods=['GET', 'POST'])
@oidc.require_login
def pr_branches(product, merge_id):
    product_org = get_product_organization(product)
    username = str(oidc.user_getfield('uid'))
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')
    if environ.get("ENVIRONMENT") != "PRODUCTION":
        if product == 'cnee':
            product_org = 'mobile-cnat-infrastructure-releng'
    db_merged_pr_branch_info = []
    db_merged_pr_branch_info = get_pr_info_by_merge_id(merge_id)
    form = PRStatusForm()
    if form.validate_on_submit():
        if form.close_pr.data:
            cancelprid = form.pr_url.data
            flash("Processing your request to close the PR %s...." % cancelprid, "info")
            cancel_pr_parameter = {'PR_URL' : cancelprid, 'MERGE_ID' : merge_id, 'TRIGGERED_BY' : username }
            trigger_jenkins_build.delay(username, product , environ.get('CLOSE_PR'), cancel_pr_parameter)
            return redirect('/pr_info/%s/%s' % (merge_id, product))
        if form.ready_for_merge.data:
            branches_ready_for_merge = (request.form.getlist(product + "_Select"))
            pr_id_list = ",".join(branches_ready_for_merge)
            merge_pr_parameter = {'PR_URL' : pr_id_list, 'MERGE_ID' : merge_id, 'MERGE' : True, 'TRIGGERED_BY' : username }
            trigger_jenkins_build.delay(username, product , environ.get('CLOSE_PR'), merge_pr_parameter)
            return redirect('/pr_info/%s/%s' % (merge_id, product))
        if form.page_refresh.data:
            return redirect('/pr_info/%s/%s' % (merge_id, product))
        if form.merge_home.data:
            return redirect('/product_sync_homepage/%s' % product)
        elif form.refresh_pr.data:
            # Use github APIs to check the Live status of the PRs
            #refresh_pr_page(merge_id,DB_INST)
            merge_refresh_parameter = {
                    'MERGE_ID': merge_id,
                    'DB_INST': environ.get("DB_INST")
                }
            flash("Triggered the dashboard refresh job from jenkins for merge id %s ,Please wait till the status gets refreshed" % merge_id , 'info')
            trigger_jenkins_build.delay(username, product , environ.get('MERGE_REFRESH_JOB_NAME'), merge_refresh_parameter)
            refresh_merge_status(merge_id)
            return redirect('/pr_info/%s/%s' % (merge_id, product))
    return render_template('product_sync_pr_page.html', product_org=product_org, product=product,
                           db_merged_pr_branch_info=db_merged_pr_branch_info, merge_id = merge_id, form=form)

@branch_ops.route('/sync_approver/<product>', methods=['GET', 'POST'])
@oidc.require_login
def sync_approver(product):
    product_list = get_product_list()
    tt_role_obj = TT_Role()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    product_org = get_product_organization(product)
    if not product_org:
        flash("%s Product Org details not be found in DataBase. <NAME_EMAIL>" % product, "danger")
        return redirect('/home')
    tt_approver_list = tt_role_obj.query.filter_by(products_organization=product_org,role="approver").all()
    return render_template('show_sync_approvers.html', tt_approver_list=tt_approver_list, product=product)

@cache.memoize()
def query_branches(org, branch_status):
    branch_info = []
    branch_mobj = Branches()
    # Search for all the branches associated with that org
    data = branch_mobj.query.filter_by(products_organization=org, branch_name='main').first()
    data = data.__dict__
    if data["branch_creation_date"]:
        data["branch_creation_date"] = parse(data["branch_creation_date"]).date()
    # covert the timestamp from string to only date
    branch_info.append(data)
    for row in branch_mobj.query.filter_by(products_organization=org).order_by(Branches.branch_name.desc()).filter(Branches.branch_name != 'main'):
        data = row.__dict__
        # covert the timestamp from string to only date
        if data["branch_creation_date"]:
            data["branch_creation_date"] = parse(data["branch_creation_date"]).date()
        if data["rer_number"]:
            if re.search("PC", data["rer_number"]):
                data["rer_number"] = data["rer_number"].split(":")[1]
                data["pc_rer"] = "yes"
            else:
                data["pc_rer"] = None
        if branch_status == "all":
            branch_info.append(data)
        elif data["branch_status"] not in ['obsolete' , 'stale']:
            branch_info.append(data)
        else:
            pass
    return branch_info
