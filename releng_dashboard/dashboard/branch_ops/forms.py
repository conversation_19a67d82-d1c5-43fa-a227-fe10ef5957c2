from flask_wtf import FlaskForm
from wtforms import SubmitField, StringField, BooleanField, RadioField, SelectField, HiddenField
from wtforms.validators import DataRequired, Length
from wtforms.fields.html5 import DateField
from dashboard.tasks import list_to_choices

class BranchForm(FlaskForm):
    submit_all = SubmitField(label="Show all branches - Active & Inactive")
    submit_active = SubmitField(label="Show only active branches")
    
class DeleteForm(FlaskForm):
    delete = SubmitField('Delete Branches')
    submit = SubmitField('Submit')
    renew = SubmitField('Renew Branch')
    branch_name =  HiddenField()
    repo_name = HiddenField()
    deletiondate = HiddenField()


class CreateBranchOrgForm(FlaskForm):
    submit = SubmitField('Create Branch')
    branch = StringField('Branch', [DataRequired()])
    parent_branch = StringField('Parent branch name', [DataRequired()])
    cdets_id = StringField('CDETS ID')
    parent_QA_build_no = StringField('Parent QA build no')
    select_all_repos = <PERSON>oleanField('All Repos')
    create_branch_ref = BooleanField('Change Branch Reference')


class CreateBranchProductForm(FlaskForm):
    branches = []
    branch_name = StringField('Branch name', [DataRequired()])
    parent_branch_name = SelectField('Parent branch name', choices=list_to_choices(branches), default='main')
    parent_QA_build_no = StringField('Parent QA build no',[DataRequired()],default='latest')
    branch_description = StringField('Branch description',[DataRequired()])
    expiry_date = DateField('Exipry date of branch', format='%Y-%m-%d')
    enable_branch_protection = BooleanField('Enable Branch Protection',default='False')
    trigger_qa_build = BooleanField('Enable QA Builds', default='False')
    cdets_id = StringField('CDETS ID')
    submit = SubmitField('Create Branch')


class CreateBranchHomeForm(FlaskForm):
    submit = SubmitField('Next')
    create_branch_type = RadioField('create_branch',choices=[('O','Create Branch for only this Org'),
                                                   ('P','Create Branch for this Org & dependent Orgs ')])

class MergeForm(FlaskForm):
    submit = SubmitField('Merge Branch')
    source_branch = StringField('Source Branch', [DataRequired()])
    destination_branch = StringField('Destination Branch', [DataRequired()])
    dry_run = BooleanField('dry_run')
    auto_merge = BooleanField('auto_merge')
    select_all_repos = BooleanField('All Repos')
    notify_by_email = StringField('Additonal users to be notified as comma separated mail IDs')

class MergeStatusForm(FlaskForm):
    merge_id = HiddenField('Merge ID')
    refresh_merge_home = SubmitField(label="Refresh Merge Page")
    cancel_merge = SubmitField(label="Cancel Merge")
    refresh_merge = SubmitField(label="Refresh Merge")
    approve = SubmitField(label="Approve")
    deny = SubmitField(label="Deny")
    cancel = SubmitField(label="Cancel")

class PRStatusForm(FlaskForm):
    close_pr = SubmitField(label="Close PR")
    pr_url = HiddenField()
    merge_home = SubmitField('Sync Home Page')
    refresh_pr = SubmitField('Fetch live update of PR and Merge')
    page_refresh = SubmitField(label="Refresh")
    ready_for_merge = SubmitField('Ready for merge')