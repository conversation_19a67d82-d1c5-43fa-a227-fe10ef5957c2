from .. import db


class StaleBranches(db.Model):
    __tablename__ = 'STALE_BRANCHES'
    product = db.Column(db.String(100), primary_key=True)
    repo = db.Column(db.String(100), primary_key=True)
    branch_name = db.Column(db.String(100), primary_key=True)
    last_user = db.Column(db.String(100))
    lastcommitdate = db.Column(db.String(100))
    expiry_date = db.Column(db.String(100))
    branch_status = db.Column(db.String(200))
    head_sha = db.Column(db.String(200))
    protection_status = db.Column(db.String(100))


class DashboardDeletedBranches(db.Model):
    __tablename__ = 'dashboard_deleted_branches'
    product = db.Column(db.String(100), primary_key=True)
    repo = db.Column(db.String(100), primary_key=True)
    branch_name = db.Column(db.String(100), primary_key=True)
    deleted_by = db.Column(db.String(100))
    deleted_date = db.Column(db.String(100))
    head_sha = db.Column(db.String(200))

class DashboardMergeData(db.Model):
    __tablename__ = 'DASHBOARD_MERGE_DATA'
    merge_id = db.Column(db.Integer(), primary_key=True)
    merge_org = db.Column(db.String(20))
    merge_repo = db.Column(db.String(200))
    source_branch = db.Column(db.String(20))
    destination_branch = db.Column(db.String(20))
    start_time = db.Column(db.BigInteger)
    end_time = db.Column(db.BigInteger)
    jenkins_build_url = db.Column(db.String(200))
    triggered_by = db.Column(db.String(20))
    merge_status = db.Column(db.String(20))
    dry_run = db.Column(db.String(20))
    merge_report = db.Column(db.String(50))
    approval_status = db.Column(db.String(20))
    approver = db.Column(db.String(20))
    notify_by_email = db.Column(db.String(200))

class DashboardMergePR(db.Model):
    __tablename__ = 'DASHBOARD_MERGE_PR'
    merge_pr_id = db.Column(db.Integer(), primary_key=True)
    merge_id = db.Column(db.Integer())
    pr_link = db.Column(db.String(500))
    pr_authors = db.Column(db.String(100))
    pr_status = db.Column(db.String(20))
    start_time = db.Column(db.DateTime())
    end_time = db.Column(db.DateTime())
    merge_pr_repo = db.Column(db.String(20))
    merge_pr_branch = db.Column(db.String(50))
    pr_build_failure = db.Column(db.String(500))
    source_branch_sha = db.Column(db.String(50))
    destination_branch_sha = db.Column(db.String(50))