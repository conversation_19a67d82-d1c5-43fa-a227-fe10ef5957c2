from flask import render_template, redirect, flash, Blueprint
from dashboard.github_ops.forms import PRForm, PR_Bypass_Form
from dashboard.github_ops.models import PullRequest
from dashboard.tasks import get_product_list, get_product_organization, get_branch_list, get_dashboard_user_role, get_dashboard_role_list, get_user_role
from dashboard.github_ops.tasks import pr_priority_change
import sys
import os
from os import environ
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
from GithubApi import GithubApi
import configparser
from dashboard import oidc
from Constants import PRODUCT_LIST
from dashboard.throttle_tracker.tasks import get_tt_role
from dashboard.access_control.tasks import get_access_role,get_access_owners
from dashboard.build.tasks import trigger_jenkins_build

github_ops = Blueprint('github_ops', __name__)
grafana_host=os.environ["GRAFANA_HOST"]

# Trigger qa build job
@github_ops.route('/pull_request/<product>', methods=['GET', 'POST'])
@oidc.require_login
def pre_commit_ci(product):
    form = PRForm()
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    product_org = get_product_organization(product)
    pr_info = query_pull_request(product_org)
    github_obj = GithubApi()
    pr = PullRequest()
    user_role =  get_user_role(oidc.user_getfield('uid'))
    pre_commit_ci_ini = os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/pre_commit_ci.ini"
    config = configparser.ConfigParser()
    supported_branches = []
    try:
        config.read(os.path.realpath(pre_commit_ci_ini))
        supported_branches = config.get(product_org, 'supported_branches').split(",")
    except Exception as e:
        flash("%s is not supported for Pre-commit CI " % product, "info")
    '''
    branches = get_branch_list(product)
    for branch in branches:
        for prod_branch in ['main','rel','tot']:
            if re.search(prod_branch, branch):
                supported_branches.append(branch)
    '''

    if form.validate_on_submit():
        if form.abort_smoke.data:
            message = "abort smoke"
            flash("Processing your request to abort smoke. Plz wait for few mins...", "info")
            result = github_obj.add_pr_comment(product_org, form.repository.data, form.pr_number.data, message)
            form.abort_smoke.data = None
            if not result:
                flash("Fail to post a comment on github from dashboard for %s" % form.pr_number.data, "danger")
        if form.start_smoke.data:
            message = "run smoke"
            flash("Processing your request to run smoke. Plz wait for few mins...", "info")
            result = github_obj.add_pr_comment(product_org, form.repository.data, form.pr_number.data, message)
            if not result:
                flash("Fail to post a comment on github from dashboard for %s" % form.pr_number.data, "danger")
            form.start_smoke.data = None
        if form.inc_priority.data:
            flash("Processing your request to inc priority. Plz wait for few mins...", "info")
            pr_priority_change(product_org, form.pr_from_branch.data, form.pr_to_branch.data, form.pr_smoke_priority.data, action="inc")
            form.inc_priority.data = None
        if form.dec_priority.data:
            flash("Processing your request to dec priority. Plz wait for few mins...", "info")
            pr_priority_change(product_org, form.pr_from_branch.data, form.pr_to_branch.data, form.pr_smoke_priority.data, action="dec")
            form.dec_priority.data = None
    return render_template('pull_request.html', product=product, pr_info=pr_info, product_org=product_org, form=form,
                           supported_branches= supported_branches, user_role=user_role)



@github_ops.route('/pr_stats/<product>', methods=['GET', 'POST'])
@oidc.require_login
def pr_stats(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product.upper(), "danger")
        return redirect('/home')
    product_org_name = PRODUCT_LIST[product]
    user_id = oidc.user_getfield('uid')
    user_role_github_access = get_access_role(user_id,product_org_name)
    user_role = get_tt_role(user_id,product_org_name)
    if user_role not in ['approver','releng'] and user_role_github_access not in ['approver','releng']:
        flash("User not permitted to access PR stats for %s " % product.upper(), "info")
        return redirect('/%s' % product)
    return render_template('product_pr_stats.html', product=product, product_org_name=product_org_name, grafana_host=grafana_host )

@github_ops.route('/bypass_pr_check/<product>', methods=['GET', 'POST'])
@oidc.require_login
def bypass_pr_check(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product.upper(), "danger")
        return redirect('/home')
    if product in ["upf","staros"]:
        flash("%s product do not support this feature. For further assistance, <NAME_EMAIL> " % product, "info")
        return redirect('/home')
    product_org_name = PRODUCT_LIST[product]
    user_id = oidc.user_getfield('uid')
    user_role, user_allowed_repo_name = get_dashboard_user_role(product_org_name, user_id)
    allowed_role_group = ['RELENG','ORG_OWNER','DEV_MANAGER','TECH_LEAD']
    if user_role not in allowed_role_group:
        flash("You are not permitted to access PR bypass page","danger")
        return redirect('/%s' % product)
    form = PR_Bypass_Form()
    if form.validate_on_submit():
        # Check if the user is allowed to approve request
        pr_url = form.pr_url.data
        pr_url_info = pr_url.split("/")
        pr_organization = pr_url_info[3]
        pr_repo = pr_url_info[4]

        if pr_organization != product_org_name:
            flash("You are trying to bypass PR check for some other github organization. Plz check the PR_URL","danger")
            return redirect('/%s' % product)
        if user_allowed_repo_name != "all" and pr_repo not in user_allowed_repo_name:
            flash("You are not permitted to bypass PR for %s. You have permissions for repos : %s" %(pr_repo, user_allowed_repo_name),"danger")
            return redirect('/%s' % product)
        else:
            # Trigger build to bypass
            mail_to_list = get_dashboard_role_list(product_org_name,allowed_role_group)
            parameter = {'PULL_REQUEST_URL': form.pr_url.data,
                         'PR_CHECK_NAME' : form.pr_checks.data,
                         'JUSTIFICATION': form.justification.data,
                         'APPROVER': user_id,
                         'RELENG_BRANCH_NAME': environ.get('RELENG_BRANCH_NAME'),
                         'MAIL_TO' : ",".join(mail_to_list)
                         }
            flash("Processing your request. Plz wait for few mins...", "info")
            trigger_jenkins_build.delay(str(user_id), product, environ.get('PR_BY_PASS_JOB_NAME'),
                                        parameter)

            return redirect('/%s' % product)
    return render_template('product_pr_bypass.html', form=form, product=product,product_org_name=product_org_name)

#@cache.memoize()
def query_pull_request(product_org):
    pr_obj = PullRequest()
    pr_info = []
    for row in pr_obj.query.filter_by(organization=product_org, pr_author=oidc.user_getfield('uid')):
        pr_info.append(row.__dict__)
    for row in pr_obj.query.filter_by(organization=product_org):
        pr_data = row.__dict__
        if not pr_data["pr_author"] == oidc.user_getfield('uid'):
            pr_info.append(pr_data)
    return pr_info
