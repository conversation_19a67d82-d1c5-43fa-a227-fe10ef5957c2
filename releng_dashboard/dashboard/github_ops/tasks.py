from dashboard.github_ops.models import PullRequest
from .. import db
from pprint import pprint

def pr_priority_change(product_org,pr_from_branch, pr_to_branch, pr_smoke_priority, action):
    pr_obj = PullRequest()
    new_smoke_priority = None
    if action == "inc":
        if pr_smoke_priority == "low":
            new_smoke_priority = "medium"
        elif pr_smoke_priority == "medium":
            new_smoke_priority = "critical"
    else:
        if pr_smoke_priority == "critical":
            new_smoke_priority = "medium"
        elif pr_smoke_priority == "medium":
            new_smoke_priority = "low"
    if new_smoke_priority:
        pr_obj.query.filter_by(organization=product_org, pr_from_branch=pr_from_branch, pr_to_branch=pr_to_branch).update(dict(smoke_priority=new_smoke_priority))
        try:
            db.session.commit()
            pprint("Priority %s for %s from %s to %s" % (action, pr_from_branch, pr_smoke_priority, new_smoke_priority))
        except Exception as e:
            db.session.rollback()
            pprint("Fail to %s priority for %s from %s to %s" % (action, pr_from_branch, pr_smoke_priority, new_smoke_priority))
        finally:
            db.session.close()
    else:
        pprint("Fail to %s priority for %s from %s to %s" % (action, pr_from_branch, pr_smoke_priority, new_smoke_priority))
