from .. import db

class PullRequest(db.Model):
    __tablename__ = 'PULL_REQUESTS'
    organization = db.Column(db.String(200))
    repository = db.Column(db.String(200))
    pr_to_branch = db.Column(db.String(200))
    pr_from_branch = db.Column(db.String(200))
    pr_url = db.Column(db.String(200))
    pr_head_sha = db.Column(db.String(200))
    pr_number = db.Column(db.Integer, primary_key=True)
    pr_state = db.Column(db.String(200))
    smoke_status = db.Column(db.String(200))
    sa_status = db.Column(db.String(200))
    tt_status = db.Column(db.String(200))
    smoke_priority = db.Column(db.String(200))
    sa_url = db.Column(db.String(100))
    pr_author = db.Column(db.String(200))
    smoke_aborted_by = db.Column(db.String(200))
    commented_on = db.Column(db.String(200))
    smoke_pre_check = db.Column(db.String(200))
    smoke_pre_check_url = db.Column(db.String(200))
    smoke_pre_check_error = db.Column(db.String(2000))
    smoke_product_build = db.Column(db.String(200))
    smoke_product_build_url = db.Column(db.String(200))
    smoke_product_build_error = db.Column(db.String(2000))
    smoke_test_run = db.Column(db.String(200))
    smoke_test_run_url = db.Column(db.String(200))
    smoke_test_run_error = db.Column(db.String(2000))
    cdets = db.Column(db.String(200))

