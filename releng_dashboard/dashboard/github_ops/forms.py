from flask_wtf import FlaskForm
from wtforms import SubmitField, HiddenField, SelectField, StringField
from dashboard.tasks import list_to_choices

class PRForm(FlaskForm):
    pr_number = HiddenField()
    repository = HiddenField()
    pr_from_branch = HiddenField()
    pr_to_branch = HiddenField()
    pr_smoke_priority = HiddenField()
    abort_smoke = SubmitField(label="abort smoke")
    start_smoke = SubmitField(label="run smoke")
    inc_priority = SubmitField(label="Increase Priority")
    dec_priority = SubmitField(label="Decrease Priority")

class PR_Bypass_Form(FlaskForm):
    pr_check_list = ['static-analysis','code-coverage', 'Unit-test','smoke-test','CDETS-Check','Throttle Approval','sonar-check']
    pr_checks = SelectField('PR Check', choices=list_to_choices(pr_check_list), default='code-coverage')
    pr_url = StringField('PR Url',default=' ')
    justification = StringField('Justification',default=' ')
    submit = SubmitField(label="Bypass PR Check")
