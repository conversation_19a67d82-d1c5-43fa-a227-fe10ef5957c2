from flask_wtf import FlaskForm
from wtforms import SubmitField, StringField, BooleanField, RadioField, SelectField, HiddenField
from wtforms.validators import DataRequired, Length
from wtforms.fields.html5 import DateField
from dashboard.tasks import list_to_choices

class LOCForm(FlaskForm):
    submit = SubmitField('Next')
    loc_type = RadioField('loc_type',choices=[('D','LOC Between Dates'),
                                              ('U','LOC By User'),
                                              ('Q','LOC Between QA Builds')])


class DateLocForm(FlaskForm):
    submit = SubmitField('Submit')
    branch = StringField('Branch', [DataRequired()])
    from_date = DateField('DatePicker', [DataRequired()], format='%Y-%m-%d',)
    to_date = DateField('DatePicker', [DataRequired()], format='%Y-%m-%d')
    recp_list = StringField('recp_list')


class QALocForm(FlaskForm):
    branches = []
    submit = SubmitField('Submit')
    start_build = StringField('start_build', [DataRequired()], default='first')
    end_build = StringField('end_build', [DataRequired()], default='latest')
    start_branch_name = SelectField('Branch_Name', choices=list_to_choices(branches), default='main')
    end_branch_name = SelectField('Branch_Name', choices=list_to_choices(branches), default='main')
    recp_list = StringField('recp_list')

class UserLocForm(DateLocForm):
    #user_list = StringField('user_list', [DataRequired()])
    user_list = StringField('user_list')

class CdetsDiffForm(FlaskForm):
    branches = []
    submit = SubmitField('Submit')
    release_branch_name = SelectField('Release Branch Name', choices=list_to_choices(branches), default='rel-2024.04')
    mail_to = StringField('Mail To')