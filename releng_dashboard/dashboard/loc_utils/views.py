from flask import render_template, redirect, flash, Blueprint, request
from dashboard.tasks import get_product_list, get_product_organization, get_branch_list, get_dashboard_user_role, get_rel_branch_list
from dashboard.build.tasks import trigger_jenkins_build
from dashboard.tasks import list_to_choices
from dashboard.build.models import Builds
from dashboard.models import Branches
from dashboard.loc_utils.forms import LOCForm, DateLocForm, QALocForm, UserLocForm, CdetsDiffForm
from os import environ
from dashboard import oidc
import sys
import os
from dashboard.access_control.tasks import get_access_role

sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../../../libs/")
from GithubApi import GithubApi
from Constants import PRODUCT_LIST
from Constants import supported_cdets_diff_branch
from Bot import bot_send_message

loc_utils = Blueprint('loc_utils', __name__)
grafana_host=os.environ["GRAFANA_HOST"]

@loc_utils.route('/loc/<product>', methods=['GET', 'POST'])
@oidc.require_login
def loc(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    if product in ["cee", "base-vm", "deployer", "cee-bm", "base-bm", "deployer-bm", "staros", "upf", "up-recovery", "smi-nso"]:
        flash("LOC for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    if product in ["app-infra", "golang-lib", "cnee", "lbs-libraries"]:
        return redirect('/dateloc/%s' % product)

    form = LOCForm()
    if request.method == "POST":
        loc_type = form.loc_type.data
        if form.validate_on_submit():
            if loc_type == 'D':
                return redirect('/dateloc/%s' % product)
            elif loc_type == 'U':
                return redirect('/userloc/%s' % product)
            else:
                return redirect('/qaloc/%s' % product)

    return render_template('generate_loc.html', form=form, product=product)


@loc_utils.route('/dateloc/<product>', methods=['GET', 'POST'])
@oidc.require_login
def date_loc(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    if product in ["cee", "base-vm", "deployer", "cee-bm", "base-bm", "deployer-bm", "staros", "upf", "up-recovery", "smi-nso"]:
        flash("LOC for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    form = DateLocForm()
    if request.method == "POST":
        user_id = oidc.user_getfield('uid')
        recp_list = form.recp_list.data
        recp_list = recp_list + "," + user_id
        start_date = form.from_date.data.strftime("%Y-%m-%d")
        end_date = form.to_date.data.strftime("%Y-%m-%d")
        # form.ercp_list.data
        parameter = {'product': product,
                     'branch': form.branch.data,
                     'start_date': start_date,
                     'end_date': end_date,
                     'recp_list': recp_list
                     }
        if form.validate_on_submit():
            flash("Processing your request. Plz wait for few mins..."
                  "You will get the Spark Notification with Job Details and Later the report via Email."
                  , "info")
            trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('DATE_LOC_JOB_NAME'),
                                        parameter)
    return render_template('date_loc.html', form=form, product=product)


@loc_utils.route('/qaloc/<product>', methods=['GET', 'POST'])
@oidc.require_login
def qa_loc(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    if product in ["cee", "base-vm", "deployer", "cee-bm", "base-bm", "deployer-bm", "app-infra", "golang-lib", "cnee", "staros", "upf", "up-recovery", "smi-nso", "lbs-libraries"]:
        flash("LOC between QA builds for %s is not supported. And only supported for 5G products." % product, "info")
        return redirect('/%s' % product)

    form = QALocForm()
    branches = get_branch_list(product)
    form.start_branch_name.choices = list_to_choices(branches)
    form.end_branch_name.choices = list_to_choices(branches)
    if not branches:
        branches = ['main']

    if request.method == "POST":
        user_id = oidc.user_getfield('uid')
        start_branch = form.start_branch_name.data
        end_branch = form.end_branch_name.data
        start_build = form.start_build.data
        end_build = form.end_build.data
        # if end_build is 'latest' than find the latest build on the branch
        if end_build == 'latest':
            end_build = query_latest_build(product, end_branch)
        if end_build is None:
            flash("No Build found for To branch : %s . Please select another branch."
                  % end_branch, "info")
            return redirect('/qaloc/%s' % product)

        # if start_build is 'first' than find the first build on the branch
        if start_build == 'first':
            start_build = query_first_build(product, start_branch)
        if start_build is None:
            flash("No Build found for from branch : %s . Please select another branch."
                  % start_branch, "info")
            return redirect('/qaloc/%s' % product)
        recp_list = form.recp_list.data
        recp_list = recp_list + "," + user_id

        if product == 'app-infra':
            j_product = 'appinfra'
        elif product == 'golang-lib':
            j_product = 'golang'
        else:
            j_product = product
        parameter = {'product': j_product,
                     'start_build': start_build,
                     'end_build': end_build,
                     'recp_list': recp_list
                     }
        if form.validate_on_submit():
            # flash("From %s:::%s : To %s:::%s" % (start_branch, parameter["start_build"],
            #                                     end_branch, parameter["end_build"]), "info")

            flash("Processing your request. Plz wait for few mins..."
                  "You will get the Spark Notification with Job Details and Later the report via Email."
                  , "info")
            trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('QA_LOC_JOB_NAME'),
                                        parameter)

    return render_template('qa_loc.html', form=form, product=product, branches=branches)

@loc_utils.route('/userloc/<product>', methods=['GET', 'POST'])
@oidc.require_login
def user_loc(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    if product in ["cee", "base-vm", "deployer", "cee-bm", "base-bm", "deployer-bm", "staros", "upf", "up-recovery", "smi-nso"]:
        flash("LOC for %s is not supported" % product.upper(), "info")
        return redirect('/%s' % product)
    product_org_name = PRODUCT_LIST[product]
    user_id = oidc.user_getfield('uid')
    user_role,repo_name = get_dashboard_user_role(product_org_name,user_id)
    if not user_role in ["ORG_OWNER","DEV_MANAGER","RELENG"]:
        flash("User not permitted to create LOC Report for %s " % product.upper(), "info")
        return redirect('/%s' % product)

    form = UserLocForm()
    if request.method == "POST":
        user_id = oidc.user_getfield('uid')
        recp_list = form.recp_list.data
        recp_list = recp_list + "," + user_id
        start_date = form.from_date.data.strftime("%Y-%m-%d")
        end_date = form.to_date.data.strftime("%Y-%m-%d")
        user_list = form.user_list.data
        # form.ercp_list.data
        parameter = {'product': product,
                     'branch': form.branch.data,
                     'start_date': start_date,
                     'end_date': end_date,
                     'recp_list': recp_list,
                     'user_list': user_list
                     }
        if form.validate_on_submit():
            invalid_user_id = set()
            github_obj = GithubApi()
            for id in user_list.split(','):
                if not github_obj.is_github_member(id):
                    invalid_user_id.add(id)
            if user_list.strip() == '' :    #Get the report for all the users
                invalid_user_id = set() 
            if len(invalid_user_id) > 0:
                msg = "Invalid user id(s) :{u} ".format(u = invalid_user_id).replace('{','').replace('}','').replace("'",'')
                flash(msg, "info")
                return redirect('/userloc/%s' % product)
            elif end_date < start_date:
                flash("Wrong Date range", "info")
                return redirect('/userloc/%s' % product)
            else:
                flash("Processing your request. Please wait for few mins..."
                  "You will get the Webex Notification with Job Details and Later the report via Email."
                  , "info")
                trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('DATE_USER_LOC_JOB_NAME'),
                                        parameter)
    return render_template('user_loc.html', form=form, product=product)


@loc_utils.route('/loc_trend/<product>', methods=['GET', 'POST'])
@oidc.require_login
def sanity_report(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product.upper(), "danger")
        return redirect('/home')
    product_org_name = PRODUCT_LIST[product]
    user_id = oidc.user_getfield('uid')
    user_role,repo_name = get_dashboard_user_role(product_org_name,user_id)
    if not user_role in ["ORG_OWNER","DEV_MANAGER","RELENG"]:
        flash("User not permitted to create LOC Report for %s " % product.upper(), "info")
        return redirect('/%s' % product)
    product_org_name = PRODUCT_LIST[product]
    return render_template('product_loc_trend.html', product=product, product_org=product_org_name, grafana_host=grafana_host )



@loc_utils.route('/cdets_diff/<product>', methods=['GET', 'POST'])
@oidc.require_login
def cdets_diff(product):
    product_list = get_product_list()
    if product not in product_list:
        flash("%s product is not part of 5g Cloud Native. If it is, <NAME_EMAIL>" % product,
              "danger")
        return redirect('/home')

    if product in ["cee", "base-vm", "deployer", "cee-bm", "base-bm", "deployer-bm", "staros", "upf"]:
        flash("CDETS Diff for %s is not supported" % product, "info")
        return redirect('/%s' % product)

    form = CdetsDiffForm()
    product_org = get_product_organization(product)
    form.release_branch_name.choices = list_to_choices(supported_cdets_diff_branch)

    if request.method == "POST":
        user_id = oidc.user_getfield('uid')
        recp_list = form.mail_to.data
        recp_list = recp_list + "," + user_id
        parameter = {'ORGANIZATION': product_org,
                     'RELEASE_BRANCH_NAME': form.release_branch_name.data,
                     'MAIL_TO': recp_list
                     }
        if form.validate_on_submit():
            flash("Processing your request. Plz wait for few mins..."
                  "You will get the Spark Notification with Job Details and Later the report via Email."
                  , "info")
            trigger_jenkins_build.delay(str(oidc.user_getfield('uid')), product, environ.get('CDETS_DIFF_JOB_NAME'),
                                        parameter)
    return render_template('cdets_diff.html', form=form, product=product)


def query_latest_build(product, branch):
    build_mobj = Builds()
    result = build_mobj.query.filter_by(products_product_name=product, branches_branch_name=branch,
                                        build_status='Passed').order_by(Builds.id.desc()).limit(1).all()
    if result:
        data = result[0].__dict__
        return data["build_number"]
    else:
        return None


def query_first_build(product, branch):
    build_mobj = Builds()
    if branch == 'main':
        product_org = get_product_organization(product)
        branch_obj = Branches()
        br_result = branch_obj.query.filter_by(products_organization=product_org, branch_name=branch).all()
        if br_result:
            data = br_result[0].__dict__
            branch_version = data["branch_version"]
        else:
            return None
        if branch_version:
            result = build_mobj.query.filter_by(products_product_name=product, branches_branch_name=branch,
                                                branches_branch_version=branch_version, build_status='Passed').order_by(
                Builds.id).limit(1).all()
        else:
            return None

    else:
        result = build_mobj.query.filter_by(products_product_name=product, branches_branch_name=branch,
                                            build_status='Passed').order_by(Builds.id).limit(1).all()
    if result:
        data = result[0].__dict__
        return data["build_number"]
    else:
        return None

    # if the branch is main, get the current release cadence of the main branch and
    # find the 1st successful build on the branch .
