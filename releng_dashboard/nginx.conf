events { worker_connections 1024;}

http {
    upstream localhost {
        server releng_dashboard-web-1:80;
        server releng_dashboard-web-2:80;
        server releng_dashboard-web-3:80;

    }


    server {
        listen 443 ssl;
        #server_name localhost;
        ssl_certificate /tmp/cert.cer;
        ssl_certificate_key /tmp/pvt.key;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;

        location / {
            #include proxy_params;
            proxy_pass https://localhost;
        }
    }
}
