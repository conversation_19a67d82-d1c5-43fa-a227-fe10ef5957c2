version: "3"

volumes:
   cisco-bins:


services:
   web:
      build:
         context: ../
         dockerfile: releng_dashboard/docker-flask/Dockerfile
      environment:
              - JENKINS_USERNAME=$jenkins_username
              - JENKINS_TOKEN=$jenkins_token
              - USERNAME=$username
              - FULL_TOKEN=$full_token
              - DB_PROD_PASSWORD=$db_prod_password
              - DB_DEV_PASSWORD=$db_dev_password
              - V_CN_CISCO_GROUP_USER=$cisco_grp_user
              - V_CN_CISCO_GROUP_PW=$cisco_grp_pw
              - V_CN_MYID_CLIENTID=$myid_clntid
              - V_CN_MYID_CLIENTSECRET=$myid_clntsec
              - V_CN_CISCO_GROUP_USER_2=$cisco_grp_user_2
              - V_CN_CISCO_GROUP_PW_2=$cisco_grp_pw_2
              - V_CN_MYID_CLIENTID_2=$myid_clntid_2
              - V_CN_MYID_CLIENTSECRET_2=$myid_clntsec_2
              - room_id=${room_id} 
      restart: always
      #ports:
         #- "443:80"
      depends_on:
         - rabbit
      volumes:
         - .:/app/releng_dashboard
         - cisco-bins:/usr/cisco/bin
  

   rabbit:
      hostname: rabbit
      image: rabbitmq:3.8-management
      environment:
         - RABBITMQ_DEFAULT_USER=rabbitmq
         - RABBITMQ_DEFAULT_PASS=rabbitmq
      ports:
         - "5673:5672"
         - "15672:15672"
      restart: always


   celery:
      build:
         context: ../
         dockerfile: releng_dashboard/docker-flask/Dockerfile
      hostname: dashboard
      entrypoint: celery
      command: -A dashboard.celery worker --loglevel=info
      volumes:
         - .:/app/releng_dashboard
      links:
         - rabbit
      depends_on:
         - rabbit
      restart: always
      environment:
              - JENKINS_USERNAME=$jenkins_username
              - JENKINS_TOKEN=$jenkins_token
              - USERNAME=$username
              - FULL_TOKEN=$full_token
              - DB_PROD_PASSWORD=$db_prod_password
              - DB_DEV_PASSWORD=$db_dev_password    
              - room_id=${room_id}          

# Reference from : https://blog.carbonteq.com/python-flask-celery-docker/

   nginx:
      image: nginx
      ports:
       #- "80:80"
       - "443:443"
      volumes:
       - './nginx.conf:/etc/nginx/nginx.conf'
       - './pvt.key:/etc/nginx/certs/pvt.key'
       - './cert.cer:/etc/nginx/certs/cert.cer'
      depends_on:
       - web
      restart: always
