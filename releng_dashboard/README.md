#5G Releng Dashboard

Production server link: https://cn-rel-dash-lnx/

### How to Build & Deploy the App in Docker?

#### Prerequisite
1. Docker Enginer is up & running
2. Able to clone this repo

`cd releng_dashboard/`

#### Build and deploy in DEV mode
`./startup.sh -e dev -r <role-id> -s <secret-id> `
or
`./startup.sh -e dev -t <token> `

After starting the services. We can access dashboard on.

https://localhost or https://<hostname>

#### Build and deploy in Staging Server
Host: sjc-5g-rel3-lnx

#### Build and deploy in Staging mode
`./startup.sh -e staging -r <role-id> -s <secret-id> `
or
`./startup.sh -e staging -t <token> `

After starting the services. We can access dashboard on.

https://sjc-5g-rel3-lnx.cisco.com/

And rabbitmq server on http://sjc-5g-rel3-lnx.cisco.com:15672

#### Build and deploy in Production mode
`cd releng_dashboard/;`

`./startup.sh -e prod -r <role-id> -s <secret-id> `
or
`./startup.sh -e prod -t <token> `

After starting the services. We can access dashboard on.

https://cn-rel-dash-lnx.cisco.com/


#### Inspecting Services / Monitoring logs

`docker-compose ps`

`sudo docker-compose logs [service_name] -f --tail=10`

to monitor all the services

`docker-compose logs -f --tail=100`

In above command we use -f flag to follow logs and --tail to fetch last 10 lines you can always increase this number to your liking.

#### Interacting with Python flask container

`docker-compose exec -it web /bin/bash`

#### Stopping containers

At last we will cover how we can stop all the running services.

`docker-compose down`

### How to start the application without Docker

#### Installations
##### Python packages

     `pip3 install -r requirements.txt`

##### Oracle Client
  install oracle client from https://www.oracle.com/database/technologies/instant-client/macos-intel-x86-downloads.html

##### RabbitMQ
 (on mac) brew install rabbitmq

#### Env Setup
    `export ORACLE_HOME=/opt/oracle/instantclient_12_2/ (path where instantclient is available)`

    `export PATH=${ORACLE_HOME}:${PATH}`

    `export DYLD_LIBRARY_PATH=/opt/oracle/instantclient_12_2:${DYLD_LIBRARY_PATH}`

    ` export PATH=$PATH:/usr/local/opt/rabbitmq/sbin`

#### Start Applications  
##### RabbitMQ
 execute `rabbitmq-server` to start

##### Celery
`cd releng_dashboard`
`celery -A dashboard.celery worker --loglevel=info`

##### Flask
`cd releng_dashboard`
`python3 app.py`

Application would be available at http://localhost:5000/

#### Before pushing the code
##### Run flake8 check
`cd releng_dashboard`

`flake8 .`

How to delete __pycache__ files

find . | grep -E "(__pycache__|\.pyc|\.pyo$)" | xargs rm -rf


##### Create DB Tables from model class using SQLAlchemy

1. Import all classes from sqlalchemy and from sqlalchemy.ext.declarative import declarative_base
2. Create sqlalchemy engine
3. define class for Model inheriting from  Base class
4. Create instance for declarative_base
4. Run Base.metadata.create_all(engine)

<B>Note:</B>


Create table with Flask-SQLAlchemy is not working in my dev setup. This procedure is to create a new table using SQLAlchemy.
If the table already exists, and there are modifications to the schema in the Alchemy model, quick and dirty way is to change the DB table schema using Oracle SQLDeveloper.<BR>
     <B>TODO:</B> Explore SQLAlchemy migrate and Alembic to change schemas from Model.

<B>Example to create a oracle DB table in CN Dev DB: </B>

     from sqlalchemy import *
     from sqlalchemy.ext.declarative import declarative_base
     engine = create_engine('oracle://CN_REL_DB:<Password>@sjc-dbdl-eit04.cisco.com:1552/EIFLNXD4', echo=True)
     Base=declarative_base()
     class AlchemyTestTable(Base):
          __tablename__ = 'ALCHEMY_TEST_TABLE'
          id = Column(Integer, primary_key=True)
          name = Column(String(100), default=None)
          email = Column(String(100), default=None)
     Base.metadata.create_all(engine)

#### Start Dashboard in Linux production server
* Install docker and docker-compose
* Enable Cisco proxy settings (Reachout to GLS Lab team for support)
* Start docker service (`systemctl start docker`)
* Checkout code
* cd releng_dashboard
* sudo ./startup.sh prod # Start in production mode using production DB
* sudo ./startup.sh # Start in dev mode using dev DB
Run `docker-compose *` as sudo and don't login as root </BR>
Code path: cn-rel-dash-lnx:/opt/5g-dashboard/releng-tools/releng_dashboard </BR>

#### Deploy new changes in production server (These steps has to be tested)
* Take downtime for Dashboard
* cd ${dashboard_path}
* Bring down flash and ngnix containers
* Wait for celery nodes to finish tasks
* Bring down celery
* git pull
* sudo ./startup.sh prod
* Ensure the all the docker containers are up and running



### SSL certificate generation
Plz check wiki : https://wiki.cisco.com/pages/viewpage.action?pageId=1269521494
