from dashboard import app
from dashboard.sonar.views import sonar
from dashboard.build.views import build
from dashboard.home.views import home
from dashboard.test_report.views import test_report
from dashboard.branch_ops.views import branch_ops
from dashboard.loc_utils.views import loc_utils
from dashboard.github_ops.views import github_ops
from dashboard.error_pages.handlers import error_pages
from dashboard.utils.celery_utils import init_celery
from dashboard.rest_api.views import rest_api
from dashboard import celery
from dashboard.throttle_tracker.views import throttle_tracker
from dashboard.access_control.views import access_control
from os import environ

if __name__ == '__main__':
    app.register_blueprint(build)
    app.register_blueprint(throttle_tracker)
    app.register_blueprint(home)
    app.register_blueprint(error_pages)
    app.register_blueprint(test_report)
    app.register_blueprint(branch_ops)
    app.register_blueprint(loc_utils)
    app.register_blueprint(github_ops)
    app.register_blueprint(rest_api)
    app.register_blueprint(access_control)
    app.register_blueprint(sonar)
    init_celery(celery, app)
    #  Self Signed certificate
    #app.run(host='0.0.0.0', ssl_context='adhoc', port=80)
    #  CA certificate
    host='0.0.0.0'
    port=80
    with open("dashboard/docker-host", 'r') as f: docker_host=f.read().rstrip()
    f.close()

    environment = environ.get("ENVIRONMENT")

    if environment == "PRODUCTION" or  environment == "STAGING" :
        ssl_context=('cert.cer', 'pvt.key')
    else:
        ssl_context='adhoc'
    #app.run(host=host, ssl_context=('ssl-cert/cn-rel-dash-lnx.cisco.com-69475.cer', 'ssl-cert/cn-rel-dash-lnx.key'), port=port)
    app.run(host=host, ssl_context=ssl_context, port=port)
