events { worker_connections 1024;}

http {
    upstream localhost {
        server releng_dashboard_web_1:80;
        server releng_dashboard_web_2:80;
        server releng_dashboard_web_3:80;

    }


    server {
        listen 443 ssl;
        #server_name localhost;
        ssl_certificate /etc/nginx/certs/cert.cer;
        ssl_certificate_key /etc/nginx/certs/pvt.key;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;

        location / {
            #include proxy_params;
            proxy_pass https://localhost;
        }
    }
}
