#!/usr/bin/env python3

# Monitor a web portal on periodic basis
# Raise alert after MAX_RETRY
# Autoremediate

import sys
import os
import subprocess
import requests
import urllib3
import time
import argparse

parser = argparse.ArgumentParser(description="Monitor and auto-remediate Web Portal")

parser.add_argument("-l", "--url", action="store", required = True, help="Web portal link to monitor")
parser.add_argument("-r", "--remediate", action="store", required = True, help="Script to remediate if portal is down")
args = parser.parse_args()
url = args.url
remediate_script = args.remediate

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning) 

MAX_RETRY = 20
RETRY_INTERVAL = 60 # Seconds
TIMEOUT = 10 # Seconds
access_issue = False
issue_message = ''

tried = 0
response = None
while (tried < MAX_RETRY):
    try:
        tried += 1
        print ("ATTEMPT: %s" % tried)
        response = requests.get(url, timeout=TIMEOUT, verify = False)
    except Exception as e:
        access_issue = True
        issue_message = str(e)
    if access_issue:
        sys.stderr.write("Error: %s\n" % issue_message)        
        print("Sleeping for %s sec ..." % RETRY_INTERVAL)
        time.sleep(RETRY_INTERVAL)
    else:
        print("Successfully connected to %s" % url)
        break

if (tried == MAX_RETRY):
    # Check whether remediation script is already running 
    # Check whether startup.sh is running
    cmd = "ps waux | grep startup.sh | grep -v grep | grep -v %s | wc -l | awk '{print $1;}' " % (sys.argv[0])
    print ("Running cmd: %s" % cmd)
    p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE)
    stdout = p.stdout.read()
    out = int(stdout)
    if out > 0:
        # startup.sh is running at this moment
        print("Startup script is already running in the system ")
        sys.exit(0)
    try:
        with open("startup.sh.PID", "r") as fh:
            pid = fh.readline().strip()
            if os.path.exists("/proc/%s/status" % pid):
                print("Startup script is already running in the system with PID",  pid)
        fh.close()
    except Exception as e:
        print("startup.sh run not detected.")
        print ("Running Remediation script (%s)..." % remediate_script)
        os.system(remediate_script)

