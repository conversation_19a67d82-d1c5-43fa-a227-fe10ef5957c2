#!/bin/bash
#set -x
# kirubs, Jul 23, 2020
# <PERSON>ript to monitor releng dashboard web hosts and auto-remediate
# Will work only in the monitoring container from image 'dashboard_monit'
# Exit code:
#	0 when all containers are up
#	1 when container(s) are down and were successfully restarted
#	2 when containers(s) are down and unable to restart container
# Steps
## Check whether the https link for the web hosts as accessible
## If not, retry for 5 times, and restart the affected web container

host_prefix='releng_dashboard-web-'
domain='releng_dashboard_default'
hosts_suffix='1 2 3'
MAX_COUNT=5
SLEEP_INTERVAL=5
exit_code=0

for i in $hosts_suffix
do
	echo "----------------------------"
	host=${host_prefix}${i}
	echo "Checking '${host}'"
	fqdn="${host}.${domain}"
	url="https://${fqdn}:80/ping"
	count=1
	while [ $count -lt $MAX_COUNT ]
	do
		/opt/releng_dashboard/docker-monit/check_https_443 $url
		if [ $? -eq 0 ];
		then
			echo "Success"
			break
		else
			echo "Error: Unable to fetch $url"
			sleep $SLEEP_INTERVAL 
			count=$(( $count + 1 ))
			echo "Retry ${count}"
			echo "-------"
		fi
	done
	if [ $count -eq $MAX_COUNT ]; then
 		echo "Error: Issue with '${fqdn}'."
		echo "Attempting to restart ${fqdn}..."
		echo "####"
		set -x
		cd /opt/releng_dashboard/
		docker stop ${host}
		docker start ${host}
		set +x
		if [ $? -eq 0 ]; then
			echo "Restart of '${host}' successful"
			exit_code=1
		else
			echo "Error: Restart of '${host}' failed"
			exit_code=2
		fi
		echo "####"
	fi

done
echo MONIT_SCRIPT_EXIT_STATUS ${exit_code} 
exit ${exit_code}
