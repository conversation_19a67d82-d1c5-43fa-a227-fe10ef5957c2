#!/bin/bash
LOG_FILE=/tmp/dashboard_restart_log.txt

usage() {
	echo "Usage: $0 <DASHBOARD_SRC_DIR>"
	exit 1
}

if [ $# -ne 1 ]; 
then
	usage
fi

dashboard_dir=$1
echo "The script monitors web hosts on the releng dashboard and automatically restarts containers if their https links become inaccessible."
echo "docker run --network=releng_dashboard_default -v /var/run/docker.sock:/var/run/docker.sock -v "${dashboard_dir}":/opt/releng_dashboard/ dashboard_monit:v1"

docker run \
--network=releng_dashboard_default -v /var/run/docker.sock:/var/run/docker.sock \
-v "${dashboard_dir}":/opt/releng_dashboard/ \
dashboard_monit:v1 
exit_code=$?

if [ ${exit_code} -ne 0 ];
then
	if [ ${exit_code} -eq 1 ];
	then
		mail_subject='INFO: Releng Dashboard web container(s) restarted successfully'
	fi
	if [ ${exit_code} -eq 2 ];
	then
		mail_subject='Critial: ACTION-REQUIRED: Releng Dashboard web container(s) down. Unable to restart containers'
	fi
	mail -s "${mail_subject}" <EMAIL> < ${LOG_FILE}
	
fi	
