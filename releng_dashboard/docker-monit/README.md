# 5G Releng Dashboard Monitoring and Auto Remediation

This sub-dir has the following, 
* Dockerfile to build monitoring and remediation container
* <PERSON>ript to check availability of the static ping page in all the three web containers
* <PERSON><PERSON>t to run the monitoring and remediation container and send email notification when down containers were successfully restarted or failed to restart

### How to Build the monitoring container?
```./build.sh [--full-build # If we need to rebuild the container from scratch] ```

#### Build failure workaround
Build works in MAC OS or in Ubuntu server with local account. 
Following are the steps to build from local and deploy in a different server
1. Makesure docker engine is up and running
2. Run `build.sh`
3. After successful completion of the build, create tar-ball for the built docker image
```
docker image save dashboard_monit:v1 > dashboard-monit.tar
gzip dashboard-monit.tar
```
4. Copy the tarball to remote server
``` scp dashboard-monit.tar.gz <server-host>:<destination-dir> ```
5. Load the docker image
```
gzip -d dashboard-monit.tar.gz
docker load -i dashboard-monit.tar
```

### How to run monitoring and remediation with notification?
```
./monitor_remediate_notify.sh <Dashboard-src-dir>
Ex:
./monitor_remediate_notify.sh /opt/5g-dashboard/releng-tools/releng_dashboard/
```
