#!/bin/bash
# Version 0.4
# Changelog: add timeout
# Adopted Since Jan 2015 rewritten and maintained by <PERSON><PERSON><PERSON>
# Updated for Dashboard Monitoring, Kiruba Suthan, Feb 16, 2021

# Variables definition


# Need to unset https_proxy for the monitoring to work inside docker network. 
# The containers are inside host docker network, hence there is no need of any proxy
unset https_proxy

# my PID
mypid="$$"
html_tmp="/tmp/tmp_html.$mypid"
rep_tmp="/tmp/tmp_rep.$mypid"
exit_code=2

if [ $# -lt 1 ]
then
        echo "Arguments are missing!  Run ./check_https IP_or_DNS port (optional) "
    echo "Eg:  ./check_https  https://mywebsite.com"
    exit 1
fi


/usr/bin/wget --timeout=10 --no-check-certificate --output-document=$html_tmp -S $1  2> $rep_tmp 

case $? in 
    0) if grep -q "Invalid"  $rep_tmp ;   then 
        exit_code=2
        cat $rep_tmp
       else    
         cat $rep_tmp | grep "HTTP/1" | grep "OK"
             exit_code=0
      fi
    ;;

    1) echo Generic error code.
    cat $rep_tmp | grep "HTTP/1"
          exit_code=1
    ;;

        2) echo  "Parse error, for instance, when parsing command-line options, the .wgetrc or .netrc..."
     exit_code=1
    ;;

        3) echo   "File I/O error"
          exit_code=1
    ;;

        4) echo "Network failure, cannot contact website"
    cat $rep_tmp | grep "HTTP/1"
          exit_code=2
    ;;

        7) echo  "Protocol errors"
      exit_code=1
    ;;

        8) #server is able to serve the page, but still something is wrong in the page
     cat $rep_tmp | grep "HTTP/1"
      exit_code=1
    ;;
    *) echo "Unknown error"
    exit_code=2
    ;;
    
esac
if [ -e $html_tmp ]
then
    rm $html_tmp
fi

if [ -e $rep_tmp ]
then
        rm $rep_tmp
fi
exit $exit_code

