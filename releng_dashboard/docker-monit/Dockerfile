FROM ubuntu:latest
RUN apt-get update -y
RUN apt-get install -y curl gnupg2 lsb-release 
RUN DEBIAN_FRONTEND=noninteractive apt-get install -y software-properties-common
RUN apt-get install -y vim wget iputils-ping dnsutils

# download.docker.com is accessible only via proxy
#RUN export https_proxy=proxy-wsa.esl.cisco.com:80 \
#    && curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add - \
#    && unset https_proxy

RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -

#RUN unset http_proxy
#RUN unset https_proxy

#ENV https_proxy proxy-wsa.esl.cisco.com:80
#ENV http_proxy proxy-wsa.esl.cisco.com:80

RUN curl https://download.docker.com/linux/ubuntu/dists/focal/InRelease

RUN add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

RUN unset https_proxy && unset http_proxy && apt-get update -y
RUN apt-get install -y docker-ce docker-ce-cli containerd.io

#COPY check_https_443 /opt/

# Install docker-compose
RUN curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
RUN chmod +x /usr/local/bin/docker-compose

#CMD  /opt/check_https_443 'https://cn-rel-dash-lnx.cisco.com/ping'
#CMD /opt/check_https_443 $url
CMD /opt/releng_dashboard/docker-monit/monitor_remediate.sh
