FROM python:3.8

ARG PRODUCTION

ENV http_proxy http://proxy-wsa.esl.cisco.com:80/
ENV https_proxy http://proxy-wsa.esl.cisco.com:80/

RUN apt-get update && apt-get install -y --no-install-recommends \
     locales \
     libsasl2-dev libldap2-dev libssl-dev libopencv-dev libaio-dev \
     libldap-2.4-2 vim git

RUN pwd
COPY . /app/

WORKDIR /app/releng_dashboard/ora_instantclient_lite
RUN dpkg -i oracle-instantclient19.6-basiclite_19.*******-2_amd64.deb

RUN locale-gen en_US.UTF-8
ENV LC_CTYPE en_US.UTF-8


WORKDIR /app/releng_dashboard

RUN pip3 install --upgrade pip
RUN pip3 install -r requirements.txt

#ENV FLASK_APP app.py

#EXPOSE 5000

#ENTRYPOINT [ "flask run --host=0.0.0.0" ]

#ENTRYPOINT [ "python3" ]
#CMD [ "app.py" ]


# Build cmd `docker build -t 5g-releng-dash:latest .`
# Run cmd `docker run -d -p 5000:5000 5g-releng-dash:latest`
# See logs docker logs 7e6107268a6d