FROM releng/baseflask:0.1

ARG PRODUCTION

COPY . /app/
WORKDIR /app/releng_dashboard

RUN if [ "$PRODUCTION" = "true" ]; \
then mv .env-prod .env; \
else export http_proxy= \
         https_proxy= \
         no_proxy=; \
fi

ENV FLASK_APP app.py

EXPOSE 5000

#ENTRYPOINT [ "flask run --host=0.0.0.0" ]

ENTRYPOINT [ "python3" ]
CMD [ "app.py" ]


# Build cmd `docker build -t 5g-releng-dash:latest .`
# Run cmd `docker run -d -p 5000:5000 5g-releng-dash:latest`
# See logs docker logs 7e6107268a6d
