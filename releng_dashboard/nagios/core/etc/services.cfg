define service{
        use                     generic-service
        host_name               cn-rel-dash-lnx
        service_description     CPU Load
        check_command           check_nrpe!check_load
        event_handler           dump_process_bycpu
        }

define service{
        use                     generic-service
        host_name               cn-rel-dash-lnx
        service_description     Total Processes
        check_command           check_nrpe!check_total_procs
        }

define service{
        use                     generic-service
        host_name               cn-rel-dash-lnx
        service_description     Current Users
        check_command           check_nrpe!check_users
        }

define service{
        use                     generic-service
        host_name               cn-rel-dash-lnx
        service_description     SSH Monitoring
        check_command           check_nrpe!check_ssh
        }

define service{
        use                     generic-service
        host_name               cn-rel-dash-lnx
        service_description     Disk Space
        check_command           check_nrpe!check_root_disk
}

define service{
        use                     generic-service
        host_name               cn-rel-dash-lnx
        service_description     Dashboard Portal
        check_command           check_nrpe!check_cn-rel-dash-lnx
}

define service{
        use                     generic-service
        host_name               cn-rel-dash-lnx
        service_description     Grafana Portal
        check_command           check_nrpe!check_grafana_portal
}

define command{
        command_name            dump_process_bycpu
        command_line            /usr/local/nagios/libexec/eventhandlers/cpu-usage
}
