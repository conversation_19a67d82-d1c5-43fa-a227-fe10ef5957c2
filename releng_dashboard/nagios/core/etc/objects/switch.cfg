###############################################################################
# SWITCH.CFG - SAMPLE CONFIG FILE FOR MONITORING A SWITCH
#
#
# NOTES: This config file assumes that you are using the sample configuration
#    files that get installed with the Nagios quickstart guide.
#
###############################################################################



###############################################################################
#
# HOST DEFINITIONS
#
###############################################################################

# Define the switch that we'll be monitoring

define host {

    use                     generic-switch                      ; Inherit default values from a template
    host_name               linksys-srw224p                     ; The name we're giving to this switch
    alias                   Linksys SRW224P Switch              ; A longer name associated with the switch
    address                 *************                       ; IP address of the switch
    hostgroups              switches                            ; Host groups this switch is associated with
}



###############################################################################
#
# HOST GROUP DEFINITIONS
#
###############################################################################

# Create a new hostgroup for switches

define hostgroup {

    hostgroup_name          switches                            ; The name of the hostgroup
    alias                   Network Switches                    ; Long name of the group
}



###############################################################################
#
# SERVICE DEFINITIONS
#
###############################################################################

# Create a service to PING to switch

define service {

    use                     generic-service                     ; Inherit values from a template
    host_name               linksys-srw224p                     ; The name of the host the service is associated with
    service_description     PING                                ; The service description
    check_command           check_ping!200.0,20%!600.0,60%      ; The command used to monitor the service
    check_interval          5                                   ; Check the service every 5 minutes under normal conditions
    retry_interval          1                                   ; Re-check the service every minute until its final/hard state is determined
}



# Monitor uptime via SNMP

define service {

    use                     generic-service                     ; Inherit values from a template
    host_name               linksys-srw224p
    service_description     Uptime
    check_command           check_snmp!-C public -o sysUpTime.0
}



# Monitor Port 1 status via SNMP

define service {

    use                     generic-service                     ; Inherit values from a template
    host_name               linksys-srw224p
    service_description     Port 1 Link Status
    check_command           check_snmp!-C public -o ifOperStatus.1 -r 1 -m RFC1213-MIB
}



# Monitor bandwidth via MRTG logs

define service {

    use                     generic-service                     ; Inherit values from a template
    host_name               linksys-srw224p
    service_description     Port 1 Bandwidth Usage
    check_command           check_local_mrtgtraf!/var/lib/mrtg/*************_1.log!AVG!1000000,1000000!5000000,5000000!10
}
