{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 4, "iteration": 1631871012115, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 13, "w": 18, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 3, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 5, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["1h", "none"], "type": "time"}], "metricColumn": "products_product_name", "rawQuery": false, "rawSql": "SELECT\n  $__timeGroupAlias(start_time,1h),\n  products_product_name AS metric,\n  avg(passed_percentage)\nFROM IVT_REPORTS\nWHERE\n  $__timeFilter(start_time) AND\n  products_product_name IN ($product) AND\n  branch_name IN ($branch)\nGROUP BY 1,2\nORDER BY $__timeGroup(start_time,1h)", "refId": "A", "select": [[{"params": ["passed_percentage"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}]], "table": "IVT_REPORTS", "timeColumn": "start_time", "timeColumnType": "datetime", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["products_product_name", "IN", "($product)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch_name", "IN", "($branch)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CN IVT Pass percentage", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Pass Percentage", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": true, "text": ["smf"], "value": ["smf"]}, "datasource": "MySQL", "definition": "select distinct products_product_name from IVT_REPORTS;", "error": null, "hide": 0, "includeAll": true, "label": "Product", "multi": true, "name": "product", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": true, "text": "ccg", "value": "ccg"}, {"selected": true, "text": "smf", "value": "smf"}], "query": "select distinct products_product_name from IVT_REPORTS;", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": ["rel-2020.02.0"], "value": ["rel-2020.02.0"]}, "datasource": "MySQL", "definition": "select distinct branch_name from IVT_REPORTS;", "error": null, "hide": 0, "includeAll": true, "label": "Branch", "multi": true, "name": "branch", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": true, "text": "rel-2020.02.0", "value": "rel-2020.02.0"}, {"selected": false, "text": "rel-2021.01", "value": "rel-2021.01"}], "query": "select distinct branch_name from IVT_REPORTS;", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "IVT Pass percentage", "uid": "XZpyUfEGk", "version": 14}