{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 10, "iteration": 1631870913194, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "org_name", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  org_name AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Org", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "org_name", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  org_name AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Org", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 15, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "repo_name", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  repo_name AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Repo", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "repo_name", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  repo_name AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Repo", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 7, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "branch", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  branch AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Branch", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "branch", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  branch AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Branch", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 9, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "author", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  author AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Author", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["$__interval", "none"], "type": "time"}], "metricColumn": "author", "rawQuery": true, "rawSql": "SELECT\n  $__timeGroupAlias(commit_date_on_branch,$__interval),\n  author AS metric,\n  avg(CONVERT(lines_added,SIGNED INTEGER) + CONVERT(lines_deleted,SIGNED INTEGER)) AS \"changed_lines\"\nFROM cn_repo_stats\nWHERE\n  $__timeFilter(commit_date_on_branch) AND\n  org_name  IN ($org) AND\n  repo_name IN ($repo) AND\n  branch IN ($branch_name) AND\n  author IN ($author)\nGROUP BY 1,2\nORDER BY $__timeGroup(commit_date_on_branch,$__interval)", "refId": "A", "select": [[{"params": ["lines_added"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["changed_lines"], "type": "alias"}]], "table": "cn_repo_stats", "timeColumn": "commit_date_on_branch", "timeColumnType": "text", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["org_name ", "IN", "($org)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["repo_name", "IN", "($repo)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch", "IN", "($branch_name)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["author", "IN", "($author)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changed Lines Author", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": true, "tags": [], "text": ["All"], "value": ["$__all"]}, "datasource": "MySQL", "definition": "select distinct org_name from CN_REPO_STATS ;", "error": null, "hide": 0, "includeAll": true, "label": "Org", "multi": true, "name": "org", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "mobile-cnat-smf", "value": "mobile-cnat-smf"}, {"selected": false, "text": "mobile-cnat-amf", "value": "mobile-cnat-amf"}, {"selected": false, "text": "mobile-cnat-policy", "value": "mobile-cnat-policy"}, {"selected": false, "text": "mobile-cnat-cpc", "value": "mobile-cnat-cpc"}, {"selected": false, "text": "mobile-cnat-infrastructure", "value": "mobile-cnat-infrastructure"}, {"selected": false, "text": "mobile-cnat-golang-lib", "value": "mobile-cnat-golang-lib"}, {"selected": false, "text": "mobile-cnat-rcm", "value": "mobile-cnat-rcm"}, {"selected": false, "text": "mobile-cnat-udc", "value": "mobile-cnat-udc"}, {"selected": false, "text": "mobile-cnat-bng", "value": "mobile-cnat-bng"}, {"selected": false, "text": "mobile-cnat-app-infra", "value": "mobile-cnat-app-infra"}, {"selected": false, "text": "lbs-libraries", "value": "lbs-libraries"}, {"selected": false, "text": "mobile-cnat-nrf", "value": "mobile-cnat-nrf"}, {"selected": false, "text": "mobile-cnat-cn", "value": "mobile-cnat-cn"}, {"selected": false, "text": "mobile-cnat-sgw", "value": "mobile-cnat-sgw"}, {"selected": false, "text": "mobile-cnat-kpm", "value": "mobile-cnat-kpm"}, {"selected": false, "text": "mobile-cnat-pats", "value": "mobile-cnat-pats"}], "query": "select distinct org_name from CN_REPO_STATS ;", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "tags": [], "text": ["All"], "value": ["$__all"]}, "datasource": "MySQL", "definition": "select distinct repo_name from CN_REPO_STATS where org_name in ( $org);", "error": null, "hide": 0, "includeAll": true, "label": "Repo", "multi": true, "name": "repo", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "smf-service", "value": "smf-service"}, {"selected": false, "text": "amf-cd-pipeline", "value": "amf-cd-pipeline"}, {"selected": false, "text": "amf-service", "value": "amf-service"}, {"selected": false, "text": "smf-cd-pipeline", "value": "smf-cd-pipeline"}, {"selected": false, "text": "amf-sctp-lb", "value": "amf-sctp-lb"}, {"selected": false, "text": "smf-common", "value": "smf-common"}, {"selected": false, "text": "smf-rest-ep", "value": "smf-rest-ep"}, {"selected": false, "text": "amf-common", "value": "amf-common"}, {"selected": false, "text": "amf-protocol-ep", "value": "amf-protocol-ep"}, {"selected": false, "text": "smf-ops-center", "value": "smf-ops-center"}, {"selected": false, "text": "smf-configuration", "value": "smf-configuration"}, {"selected": false, "text": "amf-rest-ep", "value": "amf-rest-ep"}, {"selected": false, "text": "smf-nodemgr", "value": "smf-nodemgr"}, {"selected": false, "text": "smf-protocol", "value": "smf-protocol"}, {"selected": false, "text": "smf-udp-proxy", "value": "smf-udp-proxy"}, {"selected": false, "text": "amf-ops-center", "value": "amf-ops-center"}, {"selected": false, "text": "amf-products", "value": "amf-products"}, {"selected": false, "text": "amf-ccpu-sctp", "value": "amf-ccpu-sctp"}, {"selected": false, "text": "smf-dashboard", "value": "smf-dashboard"}, {"selected": false, "text": "amf-configuration", "value": "amf-configuration"}, {"selected": false, "text": "local_configs", "value": "local_configs"}, {"selected": false, "text": "smf-dns-proxy", "value": "smf-dns-proxy"}, {"selected": false, "text": "li-ep", "value": "li-ep"}, {"selected": false, "text": "oam-pod", "value": "oam-pod"}, {"selected": false, "text": "amf-dashboard", "value": "amf-dashboard"}, {"selected": false, "text": "amf-gosctp-lb", "value": "amf-gosctp-lb"}, {"selected": false, "text": "smf-products", "value": "smf-products"}, {"selected": false, "text": "IVT-Regression", "value": "IVT-Regression"}], "query": "select distinct repo_name from CN_REPO_STATS where org_name in ( $org);", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "tags": [], "text": ["All"], "value": ["$__all"]}, "datasource": "MySQL", "definition": "select distinct branch from CN_REPO_STATS;", "error": null, "hide": 0, "includeAll": true, "label": "Branch", "multi": true, "name": "branch_name", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "rel-2020.02.0", "value": "rel-2020.02.0"}, {"selected": false, "text": "master", "value": "master"}, {"selected": false, "text": "main", "value": "main"}, {"selected": false, "text": "rel-2020.03.0", "value": "rel-2020.03.0"}, {"selected": false, "text": "rel-2020.04", "value": "rel-2020.04"}, {"selected": false, "text": "tl-2020.02.5.t1", "value": "tl-2020.02.5.t1"}, {"selected": false, "text": "rel-2020.05", "value": "rel-2020.05"}, {"selected": false, "text": "rel-2021.01", "value": "rel-2021.01"}, {"selected": false, "text": "rel-2021.02", "value": "rel-2021.02"}, {"selected": false, "text": "tl-2021.02.1.t1", "value": "tl-2021.02.1.t1"}, {"selected": false, "text": "tl-2021.01.1.t1", "value": "tl-2021.01.1.t1"}, {"selected": false, "text": "rel-xr-7.4.x", "value": "rel-xr-7.4.x"}, {"selected": false, "text": "rel-2021.03", "value": "rel-2021.03"}], "query": "select distinct branch from CN_REPO_STATS;", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": ["s<PERSON><PERSON><PERSON><PERSON>"], "value": ["s<PERSON><PERSON><PERSON><PERSON>"]}, "datasource": "MySQL", "definition": "select distinct  author from CN_REPO_STATS ;", "error": null, "hide": 0, "includeAll": true, "label": "Author", "multi": true, "name": "author", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "s<PERSON><PERSON><PERSON><PERSON>", "value": "s<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "a<PERSON><PERSON>", "value": "a<PERSON><PERSON>"}, {"selected": false, "text": "berv", "value": "berv"}, {"selected": false, "text": "pandebna", "value": "pandebna"}, {"selected": false, "text": "auto-target-branch[bot]", "value": "auto-target-branch[bot]"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "dmevada", "value": "dmevada"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "vdummiku", "value": "vdummiku"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>a", "value": "<PERSON><PERSON><PERSON>a"}, {"selected": false, "text": "harsshuk", "value": "harsshuk"}, {"selected": false, "text": "madhs", "value": "madhs"}, {"selected": false, "text": "kardurai", "value": "kardurai"}, {"selected": false, "text": "vratna<PERSON>", "value": "vratna<PERSON>"}, {"selected": false, "text": "shilps", "value": "shilps"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "porao", "value": "porao"}, {"selected": false, "text": "hajs", "value": "hajs"}, {"selected": false, "text": "vmeghana", "value": "vmeghana"}, {"selected": false, "text": "suracham", "value": "suracham"}, {"selected": false, "text": "supratch", "value": "supratch"}, {"selected": false, "text": "prechowd", "value": "prechowd"}, {"selected": false, "text": "nkak", "value": "nkak"}, {"selected": false, "text": "syamkun", "value": "syamkun"}, {"selected": false, "text": "pragredd", "value": "pragredd"}, {"selected": false, "text": "nekka", "value": "nekka"}, {"selected": false, "text": "admalpan", "value": "admalpan"}, {"selected": false, "text": "vinokamb", "value": "vinokamb"}, {"selected": false, "text": "balajin<PERSON>", "value": "balajin<PERSON>"}, {"selected": false, "text": "mmanvend", "value": "mmanvend"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "a<PERSON><PERSON><PERSON>", "value": "a<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "nchinnan", "value": "nchinnan"}, {"selected": false, "text": "manopate", "value": "manopate"}, {"selected": false, "text": "srih", "value": "srih"}, {"selected": false, "text": "kkondijo", "value": "kkondijo"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "arnagar2", "value": "arnagar2"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "navpriya", "value": "navpriya"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "vsharma3", "value": "vsharma3"}, {"selected": false, "text": "sumit<PERSON>h", "value": "sumit<PERSON>h"}, {"selected": false, "text": "anuchatu", "value": "anuchatu"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "rahul<PERSON>y", "value": "rahul<PERSON>y"}, {"selected": false, "text": "mbelgaum", "value": "mbelgaum"}, {"selected": false, "text": "abgupta8", "value": "abgupta8"}, {"selected": false, "text": "rraj2", "value": "rraj2"}, {"selected": false, "text": "j<PERSON><PERSON><PERSON>", "value": "j<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "nkaplesh", "value": "nkaplesh"}, {"selected": false, "text": "rsamsonr", "value": "rsamsonr"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "skunji<PERSON>", "value": "skunji<PERSON>"}, {"selected": false, "text": "anandku2", "value": "anandku2"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "anashah2", "value": "anashah2"}, {"selected": false, "text": "shks", "value": "shks"}, {"selected": false, "text": "rathegs", "value": "rathegs"}, {"selected": false, "text": "sreekv", "value": "sreekv"}, {"selected": false, "text": "rpentako", "value": "rpentako"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "tmcsween", "value": "tmcsween"}, {"selected": false, "text": "gyakula", "value": "gyakula"}, {"selected": false, "text": "k<PERSON><PERSON><PERSON>", "value": "k<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "sakr", "value": "sakr"}, {"selected": false, "text": "mura<PERSON>", "value": "mura<PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "sharadr", "value": "sharadr"}, {"selected": false, "text": "g<PERSON><PERSON><PERSON>", "value": "g<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "nperiswa", "value": "nperiswa"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "pbairago", "value": "pbairago"}, {"selected": false, "text": "r<PERSON><PERSON><PERSON>", "value": "r<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "rahulj4", "value": "rahulj4"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "r<PERSON><PERSON>m", "value": "r<PERSON><PERSON>m"}, {"selected": false, "text": "di<PERSON><PERSON><PERSON>", "value": "di<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "an<PERSON><PERSON>a", "value": "an<PERSON><PERSON>a"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "slala", "value": "slala"}, {"selected": false, "text": "rahulku6", "value": "rahulku6"}, {"selected": false, "text": "sharindr", "value": "sharindr"}, {"selected": false, "text": "vegorrep", "value": "vegorrep"}, {"selected": false, "text": "prdewang", "value": "prdewang"}, {"selected": false, "text": "dsenapat", "value": "dsenapat"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "prata<PERSON><PERSON>", "value": "prata<PERSON><PERSON>"}, {"selected": false, "text": "tulaggar", "value": "tulaggar"}, {"selected": false, "text": "venb", "value": "venb"}, {"selected": false, "text": "presunda", "value": "presunda"}, {"selected": false, "text": "an<PERSON><PERSON><PERSON>", "value": "an<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "peaswara", "value": "peaswara"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "stirulka", "value": "stirulka"}, {"selected": false, "text": "rgoguen", "value": "rgoguen"}, {"selected": false, "text": "jhemstre", "value": "jhemstre"}, {"selected": false, "text": "<PERSON>hor<PERSON>i", "value": "<PERSON>hor<PERSON>i"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "vkamalar", "value": "vkamalar"}, {"selected": false, "text": "ogodbole", "value": "ogodbole"}, {"selected": false, "text": "rkhandek", "value": "rkhandek"}, {"selected": false, "text": "m<PERSON><PERSON><PERSON>", "value": "m<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "sarmurug", "value": "sarmurug"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "swsp", "value": "swsp"}, {"selected": false, "text": "kmahamka", "value": "kmahamka"}, {"selected": false, "text": "kikaki", "value": "kikaki"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "anuparoy", "value": "anuparoy"}, {"selected": false, "text": "ran<PERSON>", "value": "ran<PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "n<PERSON><PERSON><PERSON>", "value": "n<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "harkris4", "value": "harkris4"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON>a", "value": "<PERSON><PERSON>a"}, {"selected": false, "text": "shn<PERSON><PERSON><PERSON>", "value": "shn<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "rmarakal", "value": "rmarakal"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "sanboddu", "value": "sanboddu"}, {"selected": false, "text": "jkhairat", "value": "jkhairat"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "samulla", "value": "samulla"}, {"selected": false, "text": "kthummur", "value": "kthummur"}, {"selected": false, "text": "a<PERSON>roy", "value": "a<PERSON>roy"}, {"selected": false, "text": "psodhia", "value": "psodhia"}, {"selected": false, "text": "smi-build-gen", "value": "smi-build-gen"}, {"selected": false, "text": "mavaze", "value": "mavaze"}, {"selected": false, "text": "anilpand", "value": "anilpand"}, {"selected": false, "text": "harpindk", "value": "harpindk"}, {"selected": false, "text": "ppatharl", "value": "ppatharl"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "svel<PERSON>e", "value": "svel<PERSON>e"}, {"selected": false, "text": "bputtare", "value": "bputtare"}, {"selected": false, "text": "shi<PERSON><PERSON><PERSON>", "value": "shi<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "s<PERSON>i", "value": "s<PERSON>i"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "r<PERSON><PERSON><PERSON>", "value": "r<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "priss", "value": "priss"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "jobjoy", "value": "jobjoy"}, {"selected": false, "text": "gkathare", "value": "gkathare"}, {"selected": false, "text": "mtadi", "value": "mtadi"}, {"selected": false, "text": "ksirshik", "value": "ksirshik"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "nagenkri", "value": "nagenkri"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "an<PERSON>r", "value": "an<PERSON>r"}, {"selected": false, "text": "ndantulu", "value": "ndantulu"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "grungta", "value": "grungta"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "p<PERSON><PERSON><PERSON>", "value": "p<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "yachgupt", "value": "yachgupt"}, {"selected": false, "text": "pratim<PERSON>", "value": "pratim<PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "prasaku7", "value": "prasaku7"}, {"selected": false, "text": "<PERSON><PERSON>i", "value": "<PERSON><PERSON>i"}, {"selected": false, "text": "pajageka", "value": "pajageka"}, {"selected": false, "text": "sndhanwa", "value": "sndhanwa"}, {"selected": false, "text": "tut<PERSON>al", "value": "tut<PERSON>al"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "gypandey", "value": "gypandey"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "hitchhab", "value": "hitchhab"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "akbhat2", "value": "akbhat2"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "ravshar5", "value": "ravshar5"}, {"selected": false, "text": "akadwive", "value": "akadwive"}, {"selected": false, "text": "sanj", "value": "sanj"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "krith<PERSON>u", "value": "krith<PERSON>u"}, {"selected": false, "text": "vaikadam", "value": "vaikadam"}, {"selected": false, "text": "surasin2", "value": "surasin2"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "jobeaupr", "value": "jobeaupr"}, {"selected": false, "text": "m<PERSON><PERSON>", "value": "m<PERSON><PERSON>"}, {"selected": false, "text": "yuel", "value": "yuel"}, {"selected": false, "text": "sashaile", "value": "sashaile"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "b<PERSON><PERSON><PERSON>", "value": "b<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "rakban<PERSON>", "value": "rakban<PERSON>"}, {"selected": false, "text": "sanshety", "value": "sanshety"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "b<PERSON><PERSON><PERSON>", "value": "b<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "bhdesai", "value": "bhdesai"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "rotare", "value": "rotare"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "natanata", "value": "natanata"}, {"selected": false, "text": "gnnatara", "value": "gnnatara"}, {"selected": false, "text": "rimane", "value": "rimane"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "armukhop", "value": "armukhop"}, {"selected": false, "text": "vkhasare", "value": "vkhasare"}, {"selected": false, "text": "b<PERSON><PERSON><PERSON>", "value": "b<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "mitgblds", "value": "mitgblds"}, {"selected": false, "text": "v<PERSON><PERSON>i", "value": "v<PERSON><PERSON>i"}, {"selected": false, "text": "kkaithwa", "value": "kkaithwa"}, {"selected": false, "text": "vijagarw", "value": "vijagarw"}, {"selected": false, "text": "p<PERSON><PERSON><PERSON>", "value": "p<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "vid<PERSON><PERSON>", "value": "vid<PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "na<PERSON><PERSON>", "value": "na<PERSON><PERSON>"}, {"selected": false, "text": "kdhotre", "value": "kdhotre"}, {"selected": false, "text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "vkarna", "value": "vkarna"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "p<PERSON><PERSON>na", "value": "p<PERSON><PERSON>na"}, {"selected": false, "text": "epanjiar", "value": "epanjiar"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "cn-admin-gen", "value": "cn-admin-gen"}, {"selected": false, "text": "k<PERSON><PERSON>", "value": "k<PERSON><PERSON>"}, {"selected": false, "text": "c<PERSON><PERSON><PERSON>", "value": "c<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"selected": false, "text": "rahukum4", "value": "rahukum4"}, {"selected": false, "text": "veravell", "value": "veravell"}, {"selected": false, "text": "hamangap", "value": "hamangap"}, {"selected": false, "text": "pramekum", "value": "pramekum"}, {"selected": false, "text": "ankusku2", "value": "ankusku2"}, {"selected": false, "text": "swarya", "value": "swarya"}, {"selected": false, "text": "nmallepu", "value": "nmallepu"}, {"selected": false, "text": "k<PERSON><PERSON><PERSON>", "value": "k<PERSON><PERSON><PERSON>"}, {"selected": false, "text": "prekhand", "value": "prekhand"}], "query": "select distinct  author from CN_REPO_STATS ;", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1y", "to": "now"}, "timepicker": {}, "timezone": "", "title": "LOC  Dashboard", "uid": "6QRhHI47z", "version": 119}