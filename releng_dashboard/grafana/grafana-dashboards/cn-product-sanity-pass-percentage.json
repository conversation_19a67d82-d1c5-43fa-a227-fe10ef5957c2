{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 6, "iteration": 1631870798625, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.3", "pointradius": 4, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [{"params": ["1h", "none"], "type": "time"}], "metricColumn": "branch_name", "rawQuery": false, "rawSql": "SELECT\n  $__timeGroupAlias(start_time,1h),\n  branch_name AS metric,\n  avg(passed_percentage) AS \"passed_percentage\"\nFROM SANITY_REPORTS\nWHERE\n  $__timeFilter(start_time) AND\n  products_product_name IN ($product) AND\n  functional_suite = \"Total\" AND\n  status = 'COMPLETED' AND\n  branch_name IN ($branch)\nGROUP BY 1,2\nORDER BY $__timeGroup(start_time,1h)", "refId": "A", "select": [[{"params": ["passed_percentage"], "type": "column"}, {"params": ["avg"], "type": "aggregate"}, {"params": ["passed_percentage"], "type": "alias"}]], "table": "SANITY_REPORTS", "timeColumn": "start_time", "timeColumnType": "datetime", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}, {"datatype": "text", "name": "", "params": ["products_product_name", "IN", "($product)"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["functional_suite", "=", "\"Total\""], "type": "expression"}, {"datatype": "text", "name": "", "params": ["status", "=", "'COMPLETED'"], "type": "expression"}, {"datatype": "text", "name": "", "params": ["branch_name", "IN", "($branch)"], "type": "expression"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Panel Title", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": [""], "value": [""]}, "datasource": "MySQL", "definition": "select distinct products_product_name from SANITY_REPORTS;", "error": null, "hide": 0, "includeAll": true, "label": "Product", "multi": true, "name": "product", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": false, "text": "ccg", "value": "ccg"}], "query": "select distinct products_product_name from SANITY_REPORTS;", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": [""], "value": [""]}, "datasource": "MySQL", "definition": "select distinct branch_name from SANITY_REPORTS;", "error": null, "hide": 0, "includeAll": true, "label": "Branch", "multi": true, "name": "branch", "options": [], "query": "select distinct branch_name from SANITY_REPORTS;", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "CN Product Sanity Pass Percentage", "uid": "HgleHiqMk", "version": 8}