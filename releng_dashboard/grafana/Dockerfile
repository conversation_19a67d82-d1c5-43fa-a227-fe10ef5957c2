FROM grafana/grafana:latest

ENV http_proxy http://proxy-wsa.esl.cisco.com:80/
ENV https_proxy http://proxy-wsa.esl.cisco.com:80/

USER root

RUN apk --no-cache add wget unzip
RUN wget -nv https://grafana.com/api/plugins/grafana-piechart-panel/versions/latest/download -O /tmp/grafana-piechart-panel.zip
RUN unzip -q /tmp/grafana-piechart-panel.zip -d /tmp
RUN mv /tmp/grafana-piechart-panel /var/lib/grafana/plugins/grafana-piechart-panel
