#!/bin/bash

if [ $# -eq 0 ];
then
  echo "Usage: $0 [ DEV | PROD ]"
  exit 1
fi

HOSTNAME=`hostname`
perl -pi -e "s/dashboard_hostname/$HOSTNAME/g" config/grafana.ini

if [ $1 == 'PROD' ]; then
  ln -sf ../../ssl-cert/cn-rel-dash-lnx.key grafana-ssl/pvt.key
  ln -sf ../../ssl-cert/cn-rel-dash-lnx.cisco.com.cer grafana-ssl/cert.cer
elif [ $1 == 'DEV' ]; then
  ln -sf ../../ssl-cert/sjc-5g-rel3-lnx.key grafana-ssl/pvt.key
  ln -sf ../../ssl-cert/sjc-5g-rel3-lnx.cisco.com.cer grafana-ssl/cert.cer
else
  echo "Usage: $0 [ DEV | PROD ]"
  exit 1
fi
docker-compose build
docker-compose up -d
exit $?
