## CN Sanity Analytics Dashboard

Production server link: https://cn-rel-dash-lnx.cisco.com:3000/ 

### How to Build & Deploy the Sanity grafana dashboard?

#### Prerequisite
1. Docker Enginer (v19.03.12 or earlier) is up & running
2. Permission to clone releng-tools repo


#### Note: 
* MySQL DB servers is not coming up with Docker v20.x. Works only with v19.x.
* <B>Any changes to script 'oracle2mysql.py' needs rebuild of mysql containers (Command `cd releng_dashboard/grafana/docker-python; ./buildOracle2SqlImg.sh`) </B>
* If a new table is added to 'oracle2mysql.py' script, 'CNDB_READ' user should be given read access to the table (see IVT_REPORTS table for reference)
* If the current user doesn't have permission to docker socket, add 'sudo' before all the docker commands. <BR>
* Current base directory is assumed to be ${CLONED_PATH}/releng_dashboard/
* GRAFANA PROD DIR LOCATION: cn-rel-dash-lnx:/opt/5g-dashboard/grafana/releng-tools/releng_dashboard/
* Run 'git pull origin' from GRAFANA PROD DIR whenever there is any change in grafana directory in releng-tools
* Daily Oracle to Sql conversion is done by the cron job <B>0 * * * * docker run ora-mysql-sync > /opt/5g-dashboard/grafana-logs/docker-ora-mysql-sync.log 2>&1</B>
#### Build and deploy app in docker for production server. 
* login to production server : cn-rel-dash-lnx.cisco.com
* `git checkout <BRANCH>` \# If the latest code changes not in main<BR>
* `cd /opt/5g-dashboard/grafana/releng-tools/releng_dashboard/grafana` <BR>
* \# Make sure below two directories are pointing to right vol 
  ```
  lrwxrwxrwx 1 root root   34 Dec  4  2020 data -> /opt/5g-dashboard/grafana-vol/data
  lrwxrwxrwx 1 root root   42 Dec  4  2020 grafana-data -> /opt/5g-dashboard/grafana-vol/grafana-data 
  ```
* sudo ./startup.sh PROD
  * `perl -pi -e "s/dashboard_hostname/cn-rel-dash-lnx.cisco.com/g" config/grafana.ini`
  * `cp ../../ssl-cert/cn-rel-dash-lnx.key grafana-ssl/pvt.key`
  * `cp ../../ssl-cert/cn-rel-dash-lnx.cisco.com.cer grafana-ssl/cert.cer`
  * Build command: `docker-compose build`
  * Deploy command: `docker-compose up -d`
* Build docker container to sync oracle DB with mysql server running in the localhost
  * `cd /opt/5g-dashboard/grafana/releng-tools/releng_dashboard/grafana/docker-python`
  * Get latest changes: `git pull origin` 
  * Create a file .env with following content 
  ```
     VAULT_ROLE_ID=XX
     VAULT_SECRET_ID=XX
  ```
     < use same role & secret id as that of dashboard > 
  * Build command: sudo /opt/5g-dashboard/grafana/releng-tools/releng_dashboard/grafana/docker-python/ora-mysql-sync-notify.sh
* Check the logs and ensure Grafana is up at port 3000 (https) and able to connect to mysql 

#### Setup in a new server
* If the setup is done in a new server, do the following (refer the source-code commit history for more info)
  * Generate ssl certificates and copy to grafana/grafana-ssl/
  * Update the following values in grafana/config/grafana.ini for the new server 
    * cert_file
    * cert_key
    * domain
   * <a href="https://wwwin-github.cisco.com/mobile-cnat-build/releng-tools/commit/088a5ca884cd8aa9f56b6e50441a944f827fc005#diff-b1c152547aa7c78dfbbd12f47ce28722/" target="_blank"> Example changes </a>

#### Usefull links
* <a href="https://grafana.com/docs/grafana/latest/installation/docker/" target="_blank"> Grafana in Docker </a>
* <a href="https://phoenixnap.com/kb/mysql-docker-container" target="_blank"> Mysql in Docker </a>
* <a href="https://www.mysqltutorial.org/getting-started-with-mysql/" target="_blank"> Mysql quick start </a>



## TO FILL IN

#### Prod server setup details
#### Backup of data directories 

    
