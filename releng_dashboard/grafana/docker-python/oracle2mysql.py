#!/usr/bin/env python3
# tokonski/rnv, Sept 2022, Oracle now using service_name (not sid)
# kirubs, Nov 2021, Oracle to mysql dump of Sanity table after processing
# kirubs, Feb, 2021, Enhanced script to handle ora to mysql dump for any table
# TODO: Optimize for incremental updates

from sqlalchemy import create_engine, MetaData, Table
from sqlalchemy.sql import select
import pandas as pd
import re
import sys
import os
dir_path = os.path.dirname(os.path.realpath(__file__))
sys.path.insert(0,os.path.realpath(dir_path))

from dotenv import load_dotenv
load_dotenv()

print("""This script copies data from specified tables in an Oracle database to corresponding tables in a MySQL database. 
The script iterates over a list of tables, retrieves data from the Oracle tables and writes it to the corresponding MySQL tables. 
Any errors encountered during the execution are logged, and the script sets an exit code to indicate success or failure.""")


PASSWORD= environ.get('DB_READ_ONLY_PASSWORD')
USER_NAME= environ.get('DB_READ_ONLY_USER')
GRAFANA_MYSQL_PASSWORD= environ.get('GRAFANA_MYSQL_PWD')
SQLALCHEMY_DATABASE_URI = "oracle" + '://' + USER_NAME + ':' + PASSWORD +'@' + "sjc-dbpl-eit02.cisco.com" + ':' + "1552" + '/?service_name=' + "EIFLNXP4_SRVC_ALL.CISCO.COM"
ora_engine = create_engine(SQLALCHEMY_DATABASE_URI, echo=True)
ora_db_name = 'CN_REL_DB'

#mysql_engine = create_engine("mysql+pymysql://grafana:" + GRAFANA_MYSQL_PASSWORD + "@rtp-5g-rel2-lnx.cisco.com/5g_releng")
mysql_engine = create_engine("mysql+pymysql://grafana:" + GRAFANA_MYSQL_PASSWORD + "@cn-rel-dash-lnx.cisco.com/5g_releng")
mysql_conn = mysql_engine.connect()

tables = (('IVT_REPORTS', 'IVT_REPORTS'), ('SANITY_REPORTS', 'SANITY_REPORTS'),('CN_REPO_STATS', 'CN_REPO_STATS'),('SONAR', 'SONAR_REPORT'))
exit_code = 0

for row in tables:
    (ora_table, mysql_table) = (row)
    query = 'SELECT * FROM %s.%s' % (ora_db_name, ora_table)
    df = pd.read_sql_query(query, con=ora_engine)

    # We need special handling of Sanity data
    if ora_table == 'SANITY':
        data = pd.DataFrame(columns=['product', 'date_time', 'branch', 'pass_percentage'])

        for i in df.index:
            values=dict()
            values['product'] = df.loc[i]['product'].upper()
            values['date_time'] = pd.to_datetime(df.loc[i]['date_timestamp'])
            values['branch'] = df.loc[i]['branch_name']
            try:
                regex = re.search("\d+", df.loc[i]['passed_percentage'])
                values['pass_percentage'] = int(regex.group(0))
            except:
                values['pass_percentage'] = int(0) # Ideally it should be int value equivalent to None. int(0) is not None
            data = data.append(values, ignore_index=True)

        last_week_sanity = data[data['date_time'] >= pd.to_datetime('2020-10-11')]
    elif ora_table.lower() == 'cn_repo_stats' : 
        data = pd.DataFrame(columns=['id','org_name', 'repo_name', 'branch', 'author','commit_id','lines_added','lines_deleted' ])
        data['commit_date_on_branch']= pd.to_datetime(df['commit_date_on_branch'], format='%Y-%m-%d')
        data['org_name'] = df['org_name']
        data['repo_name'] = df['repo_name']
        data['branch'] = df['branch'].str.replace('master', 'main')
        data['author'] = df['author']
        data['id'] = df['id']
        data['commit_id'] = df['commit_id']
        data['lines_added'] = df['lines_added']
        data['lines_deleted'] = df['lines_deleted']
        data['cdet'] = df['cdet']
        data['associated_pr'] = df['associated_pr']
        data['lines_modified'] = df['lines_modified']
        data['files_changed'] = df['files_changed']
        data['pr_from_branch'] = df['pr_from_branch']
        data['epic_ids'] = df['epic_ids']
        data['feature_ids'] = df['feature_ids']
        data['severity'] = df['severity']
        data['epic_ids_desc'] = df['epic_ids_desc']
        data['feature_desc'] = df['feature_desc']
        
    # For other tables, write AS-IS
    else:
        data = df 
        
    print ("Writing to '%s' table..." % mysql_table)
    try:
        data.to_sql(con=mysql_conn, name=mysql_table, if_exists='replace')
    except Exception as e:
        print ("Error: Unable to write to %s" % mysql_table)
        print ("Exception: \n%s" % e)
        exit_code = 1

sys.exit(exit_code)
