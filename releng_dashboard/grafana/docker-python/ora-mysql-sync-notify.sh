#!/bin/bash
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 DB_READ_ONLY_PASSWORD DB_READ_ONLY_USER GRAFANA_MYSQL_PWD"
    exit 1
fi

DB_READ_ONLY_PASSWORD="$1"
DB_READ_ONLY_USER="$2"
GRAFANA_MYSQL_PWD="$3"

LOG_FILE=/opt/5g-dashboard/grafana-logs/docker-ora-mysql-sync.log
echo "\n docker run ora-mysql-sync"
docker run \
  -e DB_READ_ONLY_PASSWORD="${DB_READ_ONLY_PASSWORD}" \
  -e DB_READ_ONLY_USER="${DB_READ_ONLY_USER}" \
  -e GRAFANA_MYSQL_PWD="${GRAFANA_MYSQL_PWD}" \
  ora-mysql-sync

if [ $? -ne 0 ];
then
  mail -s 'Oracle to MySQL sync Failure' <EMAIL> < ${LOG_FILE}
fi
