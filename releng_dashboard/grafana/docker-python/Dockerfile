FROM python:3.8

ARG DEBIAN_FRONTEND=noninteractive

ENV http_proxy http://proxy-wsa.esl.cisco.com:80/
ENV https_proxy http://proxy-wsa.esl.cisco.com:80/

RUN apt-get update -y
RUN apt-get install -y locales vim \
libopencv-dev libaio-dev

COPY ./ora_instantclient_lite /app/
COPY ./oracle2mysql.py /app/
COPY ./Constants.py /app/
COPY ./.env /app/
WORKDIR /app
RUN dpkg -i oracle-instantclient19.6-basiclite_19.*******-2_amd64.deb

RUN pip3 install sqlalchemy pandas cx_Oracle pymysql hvac==0.11.2 python-dotenv


CMD ["python3", "oracle2mysql.py"]

# Build command: ./buildOracle2SqlImg.sh #docker build -t ora-mysql-sync .
# Run command  : docker run ora-mysql-sync
