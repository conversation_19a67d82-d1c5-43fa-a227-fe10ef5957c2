#!/bin/bash

# <PERSON><PERSON> for backing up grafana in the releng dashboard
# 30 13 * * 2-6 /opt/5g-dashboard/releng-tools/releng_dashboard/grafana/grafana-bkup.sh 
#
rsync -alrzv --delete /opt/grafana-backup/ /auto/mcbu-5g-sw-master/cn-dashboard/cn-rel-dash-lnx/ > /tmp/gf.log 2>&1
rvalue=$?
if [ $rvalue -eq 0 ];
then
        exit 0
        #echo "Test Ignore" >> /tmp/gf.log 
fi
{
          echo From: <EMAIL>
            echo To: <EMAIL>
              echo Subject: Grafana backup failed
                echo 
                  #echo This is the message
                  cat  /tmp/gf.log 
          } |sendmail -t
