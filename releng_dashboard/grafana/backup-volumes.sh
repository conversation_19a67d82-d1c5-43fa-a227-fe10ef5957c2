#!/bin/sh

if [ `whoami` != 'root' ]; 
then 
    echo Must be a root user to run backup
    exit 1
fi

if [ $# -ne 1 ];
then
    echo Missing argument
    echo "$0 <GRAFANA_DIR>"
    exit 1
fi

BACKUP_ROOT=/opt/grafana-backup
BACKUP_MYSQL=${BACKUP_ROOT}/mysql
BACKUP_GRAFANA=${BACKUP_ROOT}/grafana

mkdir -p ${BACKUP_MYSQL} ${BACKUP_GRAFANA}

now=`date +'%Y%m%d_%H%M%S'`

cd $1

EXIT_STATUS=0

tar cvzf ${BACKUP_MYSQL}/${now}.tar.gz data
if [ $? -ne 0 ];
then
    echo Backup of Mysql data failed 
    EXIT_STATUS=1
fi

tar cvzf ${BACKUP_GRAFANA}/${now}.tar.gz grafana-data
if [ $? -ne 0 ];
then
    echo Backup of Grafana data failed
    EXIT_STATUS=1
fi

DAYS=20
echo Cleaning-up backups older than ${DAYS}...
find ${BACKUP_MYSQL}/ -ctime +${DAYS} | xargs rm 
if [ $? -ne 0 ];
then
    echo Unable to delete old MySQL backup
    EXIT_STATUS=1
fi

find ${BACKUP_GRAFANA} -ctime +${DAYS} | xargs rm
if [ $? -ne 0 ];
then
    echo Unable to delete old Grafana backup
    EXIT_STATUS=1
fi

exit ${EXIT_STATUS}
