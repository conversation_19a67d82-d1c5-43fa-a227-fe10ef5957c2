version: '3'

services:

  mysqldb:
    container_name: mysql
    image: mysql:8.0.22
    #image: mysql:5.7
    #image: mysql:8.0.2
    environment:
      - MYSQL_DATABASE=5g_releng
    ports:
      - "3306:3306"
    volumes:
      - "./config/my.conf:/etc/mysql/conf.d/config-file.cnf"
      - "./data:/var/lib/mysql:rw"

  grafana:
    build : .
    container_name: grafana
    ports:
      - "3000:3000"
    env_file:
      - "env.grafana"
    user: "0"
    links:
      - mysqldb
    volumes:
      - "./config/grafana.ini:/etc/grafana/grafana.ini"
      - "./grafana-ssl/:/var/ssl"
      - "./grafana-data/:/var/lib/grafana"

# Reference: https://www.stefanproell.at/2018/10/12/grafana-and-influxdb-with-ssl-inside-a-docker-container/
