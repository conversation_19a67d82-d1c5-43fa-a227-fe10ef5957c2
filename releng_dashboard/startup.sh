#!/bin/bash
set -e
echo $$ > startup.sh.PID
print_usage()
{
    echo "startup.sh -e|--environment <prod/dev> -a| --buildall <all|web>"
}   # end of print_usage

while [ "$1" != "" ]; do
    case $1 in
        -e | --environment ) shift
                    environment=$1;;              
        -ju | --jenkins-username ) shift
                    export jenkins_username=$1;;
        -jt | --jenkins-token ) shift
                    export jenkins_token=$1;;
        -u | --username ) shift
                    export username=$1;;
        -ft | --full-token ) shift
                    export full_token=$1;;
        -jat | --jenkins-api-token ) shift
                    export jenkins_api_token=$1;;
        -dbp | --db-prod-password ) shift
                    export db_prod_password=$1;;
        -dbd | --db-dev-password ) shift
                    export db_dev_password=$1;;
        -cs | --client_secret ) shift
                    export CLIENT_SECRET=$1;;
        -cgu | --cisco-grp-user ) shift
                    export cisco_grp_user=$1;;
        -cgp | --cisco-grp-pw ) shift
                    export cisco_grp_pw=$1;;
        -cld | --myid-clientid ) shift
                    export myid_clntid=$1;;
        -cls | --myid-client-sec ) shift
                    export myid_clntsec=$1;;
        -cgu2 | --cisco-grp-user2 ) shift
                    export cisco_grp_user_2=$1;;
        -cgp2 | --cisco-grp-pw2 ) shift
                    export cisco_grp_pw_2=$1;;
        -cld2 | --myid-clientid2 ) shift
                    export myid_clntid_2=$1;;
        -cls2 | --myid-client-sec2 ) shift
                    export myid_clntsec_2=$1;;
        -a | --buildall )    shift
                    buildall=$1;;
        -r | --room_id ) shift
                    export room_id=$1;;
        -pd | --prod_db ) shift
                    export prod_db=$1;;
        -h | --help ) print_usage
                    exit;;
        * ) print_usage
          exit 1
    esac
    shift
done

if [ -z "$environment" ];then
  echo "environment is mandatory"
  print_usage
  exit 1
fi

# Had to create symlink for ssl key and cert in CWD to mount it as volume to nginx; as docker volume is not working with relative paths
if [ "$environment" = 'prod' ]; then
  ln -sf ssl-cert/cn-rel-dash-lnx.key pvt.key
  ln -sf ssl-cert/cn-rel-dash-lnx.cisco.com.cer cert.cer
else
  ln -sf ssl-cert/sjc-5g-rel3-lnx.key pvt.key
  ln -sf ssl-cert/sjc-5g-rel3-lnx.cisco.com.cer cert.cer
fi

# Write docker hostname to a file accessible to the container
hostname > dashboard/docker-host

if [ "$environment" = 'prod' ] || [ "$environment" = 'staging' ]; then
    env_file="dashboard/.env-prod"
else
    env_file="dashboard/.env"
fi

if [ "$environment" = 'prod' ] || [ "$environment" = 'staging' ];
then
   cp -fp dashboard/.env-prod dashboard/.env
   HOSTNAME=`hostname`
   perl -pi -e "s/hostname/$HOSTNAME/g" dashboard/client_secrets-prod.json
   perl -pi -e "s/client_secret_token/$CLIENT_SECRET/g" dashboard/client_secrets-prod.json
   cp -fp dashboard/client_secrets-prod.json dashboard/client_secrets.json
   perl -pi -e "s/hostname/$HOSTNAME/g" ${env_file}
fi

if [ "$environment" = 'staging' ] && [ "$prod_db" != 'true' ];
then
   echo "Dashboard would be in staging mode"
   perl -pi -e "s/PRODUCTION/STAGING/g" dashboard/.env
   perl -pi -e "s/5g-prod/5g-dev/g" dashboard/.env
elif [ "$environment" = 'prod' ] || [ "$environment" = 'staging' ];
then
   echo "Dashboard would be in production mode"
else
   echo "Dashboard would be in dev mode"
fi

# add branch name in .env file
current_branch=`git rev-parse --abbrev-ref HEAD`
echo "RELENG_BRANCH_NAME=$current_branch" >> .env

if [ "$buildall" = 'all' ];
then
	echo "Rebuilding base images"
	pushd ../
	docker build --no-cache -t  releng/baseflask:0.1 -f releng_dashboard/docker-flask/baseFlaskDockerfile .
        popd
elif [ "$buildall" = 'web' ];
then
	echo "Rebuilding web image"
	pushd ../
	docker build --no-cache -f releng_dashboard/docker-flask/Dockerfile  releng_dashboard/docker-flask
        popd
else
	echo "No base image build"
fi

if [ "$environment" = 'prod' ];then
   cp -pf nginx-cn-rel-dash-lnx.conf nginx.conf
   cp -pf ssl-cert/cn-rel-dash-lnx.key ssl-cert/pvt.key
   cp -pf ssl-cert/cn-rel-dash-lnx.cisco.com.cer ssl-cert/cert.cer
elif [ "$environment" = 'staging' ];then
   cp -pf nginx-sjc-5g-rel3-lnx.conf nginx.conf
   cp -pf ssl-cert/sjc-5g-rel3-lnx.key ssl-cert/pvt.key
   cp -pf ssl-cert/sjc-5g-rel3-lnx.cisco.com.cer ssl-cert/cert.cer
else
   cp -pf nginx-localhost.conf nginx.conf
fi


docker-compose build

if [ $? -eq 0 ]; then
  docker-compose up -d --scale web=3
else
  echo Error: Build Failed
  exit 1
fi
